use rkyv::{Archive, Deserialize as RkyvDeserialize, Serialize as RkyvSerialize};
use libp2p::PeerId;
use std::collections::{HashSet, HashMap};
use std::time::Duration;
use rand::Rng;
use std::hash::{<PERSON>h, <PERSON><PERSON>};
use std::collections::hash_map::DefaultHasher;

pub type ShardId = u32;
pub type BallotNumber = u64;
pub type InstanceId = u64;

#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum PaxosRole {
    Pro<PERSON>r,
    Accept<PERSON>,
    <PERSON>rner,
    DistinguishedProposer, // Leader-like role for efficiency
}

#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum NodeState {
    Follower,
    Candidate,
    Leader,
}

// Multi-Paxos specific structures
#[derive(Debu<PERSON>, <PERSON><PERSON>, PartialEq, Eq, Hash)]
pub struct Ballot {
    pub number: BallotNumber,
    pub proposer_id: PeerId,
}

impl Ballot {
    pub fn new(number: BallotNumber, proposer_id: PeerId) -> Self {
        Self { number, proposer_id }
    }

    pub fn next(&self) -> Self {
        Self {
            number: self.number + 1,
            proposer_id: self.proposer_id,
        }
    }
}

impl PartialOrd for Ballot {
    fn partial_cmp(&self, other: &Self) -> Option<std::cmp::Ordering> {
        Some(self.cmp(other))
    }
}

impl Ord for Ballot {
    fn cmp(&self, other: &Self) -> std::cmp::Ordering {
        self.number.cmp(&other.number)
            .then_with(|| self.proposer_id.cmp(&other.proposer_id))
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Archive, RkyvDeserialize, RkyvSerialize)]
pub enum ShardAssignmentStrategy {
    HashBased,
    RoundRobin,
    ConsistentHashing,
}

#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct ShardConfig {
    pub shard_count: u32,
    pub assignment_strategy: ShardAssignmentStrategy,
    pub replication_factor: u32,
}

impl Default for ShardConfig {
    fn default() -> Self {
        Self {
            shard_count: 4,
            assignment_strategy: ShardAssignmentStrategy::HashBased,
            replication_factor: 1,
        }
    }
}

// Multi-Paxos message types
#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct PrepareMessage {
    pub ballot: BallotNumber,
    pub proposer_id: Vec<u8>,
    pub instance_id: InstanceId,
    pub shard_id: ShardId,
}

#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct PromiseMessage {
    pub ballot: BallotNumber,
    pub acceptor_id: Vec<u8>,
    pub instance_id: InstanceId,
    pub shard_id: ShardId,
    pub accepted_ballot: Option<BallotNumber>,
    pub accepted_value: Option<PaxosValue>,
}

#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct AcceptMessage {
    pub ballot: BallotNumber,
    pub proposer_id: Vec<u8>,
    pub instance_id: InstanceId,
    pub shard_id: ShardId,
    pub value: PaxosValue,
}

#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct AcceptedMessage {
    pub ballot: BallotNumber,
    pub acceptor_id: Vec<u8>,
    pub instance_id: InstanceId,
    pub shard_id: ShardId,
    pub value: PaxosValue,
}

#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct PaxosValue {
    pub command: LogCommand,
    pub client_id: Vec<u8>,
    pub sequence_number: u64,
}

// Legacy Raft message types (for backward compatibility during migration)
#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct VoteRequest {
    pub term: u64,
    pub candidate_id: Vec<u8>,
    pub last_log_index: u64,
    pub last_log_term: u64,
    pub shard_id: ShardId,
}

#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct VoteResponse {
    pub term: u64,
    pub vote_granted: bool,
    pub voter_id: Vec<u8>,
    pub shard_id: ShardId,
}

#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct AppendEntries {
    pub term: u64,
    pub leader_id: Vec<u8>,
    pub prev_log_index: u64,
    pub prev_log_term: u64,
    pub entries: Vec<LogEntry>,
    pub leader_commit: u64,
    pub shard_id: ShardId,
}

#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub struct LogEntry {
    pub term: u64,
    pub index: u64,
    pub command: LogCommand,
    pub shard_id: ShardId,
}

#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub enum LogCommand {
    AssignTask { task_id: [u8; 16], worker: Vec<u8> },
    UpdateTaskStatus { task_id: [u8; 16], status: crate::types::TaskStatus },
}

impl LogCommand {
    pub fn get_task_id(&self) -> [u8; 16] {
        match self {
            LogCommand::AssignTask { task_id, .. } => *task_id,
            LogCommand::UpdateTaskStatus { task_id, .. } => *task_id,
        }
    }
}

#[derive(Debug, Clone)]
pub struct PaxosInstance {
    pub instance_id: InstanceId,
    pub promised_ballot: Option<BallotNumber>,
    pub accepted_ballot: Option<BallotNumber>,
    pub accepted_value: Option<PaxosValue>,
    pub decided_value: Option<PaxosValue>,
    pub is_decided: bool,
}

impl PaxosInstance {
    pub fn new(instance_id: InstanceId) -> Self {
        Self {
            instance_id,
            promised_ballot: None,
            accepted_ballot: None,
            accepted_value: None,
            decided_value: None,
            is_decided: false,
        }
    }
}

#[derive(Debug, Clone)]
pub struct ShardPaxosState {
    pub shard_id: ShardId,
    pub role: PaxosRole,
    pub current_ballot: BallotNumber,
    pub highest_promised_ballot: BallotNumber,
    pub distinguished_proposer: Option<PeerId>,
    pub instances: HashMap<InstanceId, PaxosInstance>,
    pub next_instance_id: InstanceId,
    pub last_executed_instance: InstanceId,

    // Performance optimizations (preserved from Raft)
    pub startup_time: std::time::Instant,
    pub startup_grace_period: Duration,
    pub last_leader_seen: Option<std::time::Instant>,
    pub consecutive_failures: u32,
    pub backoff_multiplier: u32,
    pub last_heartbeat: std::time::Instant,

    // Proposer state
    pub pending_proposals: HashMap<InstanceId, PaxosValue>,
    pub promises_received: HashMap<InstanceId, HashSet<PeerId>>,
    pub accepts_received: HashMap<InstanceId, HashSet<PeerId>>,

    // Acceptor state
    pub promised_ballots: HashMap<InstanceId, BallotNumber>,
    pub accepted_proposals: HashMap<InstanceId, (BallotNumber, PaxosValue)>,
}

// Legacy Raft state structure (for backward compatibility)
#[derive(Debug, Clone)]
pub struct ShardState {
    pub shard_id: ShardId,
    pub current_term: u64,
    pub voted_for: Option<PeerId>,
    pub state: NodeState,
    pub leader_id: Option<PeerId>,
    pub votes_received: HashSet<PeerId>,
    pub last_heartbeat: std::time::Instant,
    pub election_timeout: Duration,
    pub log: Vec<LogEntry>,
    pub commit_index: u64,
    pub last_applied: u64,
    pub failed_election_count: u32,
    pub last_election_attempt: Option<std::time::Instant>,
    pub startup_time: std::time::Instant,
    pub startup_grace_period: Duration,
    pub last_leader_seen: Option<std::time::Instant>,
    pub consecutive_election_failures: u32,
    pub backoff_multiplier: u32,
}

impl ShardPaxosState {
    pub fn new(shard_id: ShardId, _peer_id: PeerId) -> Self {
        let startup_time = std::time::Instant::now();

        Self {
            shard_id,
            role: PaxosRole::Acceptor,
            current_ballot: 0,
            highest_promised_ballot: 0,
            distinguished_proposer: None,
            instances: HashMap::new(),
            next_instance_id: 1,
            last_executed_instance: 0,

            // Performance optimizations
            startup_time,
            startup_grace_period: Duration::from_secs(5),
            last_leader_seen: None,
            consecutive_failures: 0,
            backoff_multiplier: 1,
            last_heartbeat: startup_time,

            // Proposer state
            pending_proposals: HashMap::new(),
            promises_received: HashMap::new(),
            accepts_received: HashMap::new(),

            // Acceptor state
            promised_ballots: HashMap::new(),
            accepted_proposals: HashMap::new(),
        }
    }

    pub fn is_in_startup_grace_period(&self) -> bool {
        std::time::Instant::now().duration_since(self.startup_time) < self.startup_grace_period
    }

    pub fn get_adaptive_check_interval(&self) -> Duration {
        let base_interval_ms = 500_u64;
        let backoff_factor = 2_u64.pow(self.consecutive_failures.min(5));
        let backoff_interval_ms = base_interval_ms * backoff_factor;
        Duration::from_millis(backoff_interval_ms.min(10000))
    }

    pub fn should_attempt_proposal(&self, connected_peers: usize, subscribed_peers: usize) -> bool {
        if self.is_in_startup_grace_period() {
            return false;
        }

        if self.role != PaxosRole::DistinguishedProposer {
            return false;
        }

        // For single-node scenarios, wait longer before proposing
        if connected_peers == 0 && subscribed_peers == 0 {
            return std::time::Instant::now().duration_since(self.startup_time) > Duration::from_secs(10);
        }

        true
    }

    pub fn has_recent_leader_activity(&self) -> bool {
        if let Some(last_seen) = self.last_leader_seen {
            std::time::Instant::now().duration_since(last_seen) < Duration::from_secs(30)
        } else {
            false
        }
    }

    pub fn become_distinguished_proposer(&mut self, peer_id: PeerId) {
        self.role = PaxosRole::DistinguishedProposer;
        self.distinguished_proposer = Some(peer_id);
        self.consecutive_failures = 0;
        self.backoff_multiplier = 1;
        self.last_leader_seen = Some(std::time::Instant::now());
        log::info!("Shard {} 成为Distinguished Proposer", self.shard_id);
    }

    pub fn become_acceptor(&mut self) {
        self.role = PaxosRole::Acceptor;
        self.distinguished_proposer = None;
        log::info!("Shard {} 成为Acceptor", self.shard_id);
    }

    pub fn proposal_failed(&mut self) {
        self.consecutive_failures += 1;
        self.backoff_multiplier = (self.backoff_multiplier * 2).min(8);
        log::warn!("Shard {} 提案失败，连续失败: {}，退避倍数: {}",
                  self.shard_id, self.consecutive_failures, self.backoff_multiplier);
    }

    // Multi-Paxos Phase 1: Prepare
    pub fn handle_prepare(&mut self, prepare: &PrepareMessage) -> Option<PromiseMessage> {
        let instance_id = prepare.instance_id;

        // Check if we've already promised a higher ballot
        if let Some(&promised_ballot) = self.promised_ballots.get(&instance_id) {
            if prepare.ballot <= promised_ballot {
                return None; // Reject - already promised higher ballot
            }
        }

        // Promise this ballot
        self.promised_ballots.insert(instance_id, prepare.ballot);

        // Get any previously accepted value for this instance
        let (accepted_ballot, accepted_value) = self.accepted_proposals.get(&instance_id)
            .map(|(ballot, value)| (Some(*ballot), Some(value.clone())))
            .unwrap_or((None, None));

        Some(PromiseMessage {
            ballot: prepare.ballot,
            acceptor_id: Vec::new(), // Will be filled by caller
            instance_id,
            shard_id: self.shard_id,
            accepted_ballot,
            accepted_value,
        })
    }

    // Multi-Paxos Phase 2: Accept
    pub fn handle_accept(&mut self, accept: &AcceptMessage) -> Option<AcceptedMessage> {
        let instance_id = accept.instance_id;

        // Check if we've promised a higher ballot
        if let Some(&promised_ballot) = self.promised_ballots.get(&instance_id) {
            if accept.ballot < promised_ballot {
                return None; // Reject - promised higher ballot
            }
        }

        // Accept the proposal
        self.promised_ballots.insert(instance_id, accept.ballot);
        self.accepted_proposals.insert(instance_id, (accept.ballot, accept.value.clone()));

        Some(AcceptedMessage {
            ballot: accept.ballot,
            acceptor_id: Vec::new(), // Will be filled by caller
            instance_id,
            shard_id: self.shard_id,
            value: accept.value.clone(),
        })
    }

    // Proposer logic for Phase 1
    pub fn start_prepare_phase(&mut self, instance_id: InstanceId, ballot: BallotNumber) {
        self.promises_received.insert(instance_id, HashSet::new());
        log::info!("Shard {} 开始Prepare阶段，实例: {}，ballot: {}",
                  self.shard_id, instance_id, ballot);
    }

    // Handle promise responses
    pub fn handle_promise_response(&mut self, promise: &PromiseMessage, quorum_size: usize) -> Option<PaxosValue> {
        let instance_id = promise.instance_id;

        // Add to promises received
        if let Some(promises) = self.promises_received.get_mut(&instance_id) {
            let proposer_id = PeerId::from_bytes(&promise.acceptor_id).ok()?;
            promises.insert(proposer_id);

            // Check if we have a quorum
            if promises.len() >= quorum_size {
                // Find the value with the highest ballot number from promises
                let mut _highest_ballot: Option<BallotNumber> = None;
                let mut chosen_value = None;

                // In a real implementation, we'd collect all promise responses
                // For now, we'll use the value from this promise if it exists
                if let Some(accepted_value) = &promise.accepted_value {
                    chosen_value = Some(accepted_value.clone());
                }

                return chosen_value;
            }
        }

        None
    }

    // Start Accept phase
    pub fn start_accept_phase(&mut self, instance_id: InstanceId, ballot: BallotNumber, value: PaxosValue) {
        self.accepts_received.insert(instance_id, HashSet::new());
        self.pending_proposals.insert(instance_id, value);
        log::info!("Shard {} 开始Accept阶段，实例: {}，ballot: {}",
                  self.shard_id, instance_id, ballot);
    }

    // Handle accepted responses
    pub fn handle_accepted_response(&mut self, accepted: &AcceptedMessage, quorum_size: usize) -> bool {
        let instance_id = accepted.instance_id;

        // Add to accepts received
        if let Some(accepts) = self.accepts_received.get_mut(&instance_id) {
            if let Ok(acceptor_id) = PeerId::from_bytes(&accepted.acceptor_id) {
                accepts.insert(acceptor_id);

                // Check if we have a quorum
                if accepts.len() >= quorum_size {
                    // Value is decided!
                    if let Some(instance) = self.instances.get_mut(&instance_id) {
                        instance.decided_value = Some(accepted.value.clone());
                        instance.is_decided = true;
                    } else {
                        // Create new instance
                        let mut instance = PaxosInstance::new(instance_id);
                        instance.decided_value = Some(accepted.value.clone());
                        instance.is_decided = true;
                        self.instances.insert(instance_id, instance);
                    }

                    log::info!("Shard {} 实例 {} 达成共识", self.shard_id, instance_id);
                    return true;
                }
            }
        }

        false
    }

    pub fn get_next_instance_id(&mut self) -> InstanceId {
        let id = self.next_instance_id;
        self.next_instance_id += 1;
        id
    }
}

impl ShardState {
    pub fn new(shard_id: ShardId) -> Self {
        let mut rng = rand::thread_rng();
        // Increase base election timeout to reduce startup sensitivity
        let election_timeout = Duration::from_millis(500 + rng.gen::<u64>() % 500);
        let startup_time = std::time::Instant::now();

        Self {
            shard_id,
            current_term: 0,
            voted_for: None,
            state: NodeState::Follower,
            leader_id: None,
            votes_received: HashSet::new(),
            last_heartbeat: startup_time,
            election_timeout,
            log: Vec::new(),
            commit_index: 0,
            last_applied: 0,
            failed_election_count: 0,
            last_election_attempt: None,
            startup_time,
            startup_grace_period: Duration::from_secs(5), // 5 second grace period
            last_leader_seen: None,
            consecutive_election_failures: 0,
            backoff_multiplier: 1,
        }
    }
    
    pub fn reset_election_timer(&mut self) {
        self.last_heartbeat = std::time::Instant::now();
        let mut rng = rand::thread_rng();
        // Use longer, more conservative election timeouts
        self.election_timeout = Duration::from_millis(500 + rng.gen::<u64>() % 500);
    }
    
    pub fn is_election_timeout(&self) -> bool {
        // Don't trigger elections during startup grace period
        if self.is_in_startup_grace_period() {
            return false;
        }
        std::time::Instant::now().duration_since(self.last_heartbeat) > self.election_timeout
    }

    pub fn is_in_startup_grace_period(&self) -> bool {
        std::time::Instant::now().duration_since(self.startup_time) < self.startup_grace_period
    }

    pub fn get_adaptive_election_check_interval(&self) -> Duration {
        // Base interval starts at 500ms for better performance
        let base_interval_ms = 500_u64;

        // Apply exponential backoff based on consecutive failures
        let backoff_factor = 2_u64.pow(self.consecutive_election_failures.min(5));
        let backoff_interval_ms = base_interval_ms * backoff_factor;

        // Cap the maximum interval at 10 seconds
        Duration::from_millis(backoff_interval_ms.min(10000))
    }

    pub fn should_attempt_election(&self, connected_peers: usize, subscribed_peers: usize) -> bool {
        // Don't attempt elections during startup grace period
        if self.is_in_startup_grace_period() {
            return false;
        }

        // Don't attempt if already a leader
        if self.state == NodeState::Leader {
            return false;
        }

        // Check if enough time has passed since last election attempt (backoff)
        if let Some(last_attempt) = self.last_election_attempt {
            let backoff_duration = Duration::from_millis(1000) * self.backoff_multiplier;
            if std::time::Instant::now().duration_since(last_attempt) < backoff_duration {
                return false;
            }
        }

        // Only attempt if we have election timeout
        if !self.is_election_timeout() {
            return false;
        }

        // For single-node scenarios, wait longer before attempting
        if connected_peers == 0 && subscribed_peers == 0 {
            // Wait at least 10 seconds after startup before becoming leader in single-node
            return std::time::Instant::now().duration_since(self.startup_time) > Duration::from_secs(10);
        }

        // For multi-node scenarios, proceed with election
        true
    }

    pub fn has_recent_leader_activity(&self) -> bool {
        if let Some(last_seen) = self.last_leader_seen {
            std::time::Instant::now().duration_since(last_seen) < Duration::from_secs(30)
        } else {
            false
        }
    }
    
    pub fn become_candidate(&mut self, self_id: PeerId) {
        self.state = NodeState::Candidate;
        self.voted_for = Some(self_id);
        self.votes_received.clear();
        self.votes_received.insert(self_id);
        self.reset_election_timer();
        self.last_election_attempt = Some(std::time::Instant::now());
        log::info!("Shard {} 成为候选人，保持任期: {}", self.shard_id, self.current_term);
    }
    
    pub fn become_leader(&mut self, self_id: PeerId) {
        self.current_term += 1;
        self.state = NodeState::Leader;
        self.leader_id = Some(self_id);
        self.reset_election_timer();
        self.failed_election_count = 0;
        self.last_election_attempt = None;
        self.consecutive_election_failures = 0;
        self.backoff_multiplier = 1;
        self.last_leader_seen = Some(std::time::Instant::now());
        log::info!("Shard {} 成为Leader，增加任期到: {}", self.shard_id, self.current_term);
    }
    
    pub fn become_leader_without_term_increment(&mut self, self_id: PeerId) {
        self.state = NodeState::Leader;
        self.leader_id = Some(self_id);
        self.reset_election_timer();
        self.failed_election_count = 0;
        self.last_election_attempt = None;
        self.consecutive_election_failures = 0;
        self.backoff_multiplier = 1;
        self.last_leader_seen = Some(std::time::Instant::now());
        log::info!("Shard {} 成为Leader，保持任期: {}", self.shard_id, self.current_term);
    }
    
    pub fn become_follower(&mut self, term: u64, leader_id: Option<PeerId>) {
        self.state = NodeState::Follower;
        self.current_term = term;
        self.voted_for = None;
        self.leader_id = leader_id;
        self.votes_received.clear();
        self.reset_election_timer();

        if leader_id.is_some() {
            self.failed_election_count = 0;
            self.last_election_attempt = None;
            self.consecutive_election_failures = 0;
            self.backoff_multiplier = 1;
            self.last_leader_seen = Some(std::time::Instant::now());
        }

        if let Some(leader) = leader_id {
            log::info!("Shard {} 成为Follower，任期: {}，Leader: {}", self.shard_id, term, leader);
        } else {
            log::info!("Shard {} 成为Follower，任期: {}", self.shard_id, term);
        }
    }
    
    pub fn election_failed(&mut self) {
        self.state = NodeState::Follower;
        self.voted_for = None;
        self.votes_received.clear();
        self.failed_election_count += 1;
        self.consecutive_election_failures += 1;
        self.backoff_multiplier = (self.backoff_multiplier * 2).min(8); // Cap at 8x backoff
        self.reset_election_timer();

        log::warn!("Shard {} 选举失败，失败次数: {}，连续失败: {}，退避倍数: {}",
                  self.shard_id, self.failed_election_count, self.consecutive_election_failures, self.backoff_multiplier);
    }
}

pub struct PaxosState {
    pub shards: HashMap<ShardId, ShardPaxosState>,
    pub config: ShardConfig,
    pub peer_id: PeerId,
    pub global_ballot: BallotNumber,
    pub quorum_size: usize,
}

// Legacy Raft state (for backward compatibility)
pub struct RaftState {
    pub shards: HashMap<ShardId, ShardState>,
    pub config: ShardConfig,
    pub global_term: u64,
}

impl PaxosState {
    pub fn new(peer_id: PeerId) -> Self {
        Self::with_config(ShardConfig::default(), peer_id)
    }

    pub fn with_config(config: ShardConfig, peer_id: PeerId) -> Self {
        let mut shards = HashMap::new();

        for shard_id in 0..config.shard_count {
            shards.insert(shard_id, ShardPaxosState::new(shard_id, peer_id));
        }

        // Calculate quorum size (majority)
        let quorum_size = (config.replication_factor as usize / 2) + 1;

        Self {
            shards,
            config,
            peer_id,
            global_ballot: 0,
            quorum_size,
        }
    }

    pub fn get_shard_mut(&mut self, shard_id: ShardId) -> Option<&mut ShardPaxosState> {
        self.shards.get_mut(&shard_id)
    }

    pub fn get_shard(&self, shard_id: ShardId) -> Option<&ShardPaxosState> {
        self.shards.get(&shard_id)
    }

    pub fn get_next_ballot(&mut self) -> BallotNumber {
        self.global_ballot += 1;
        self.global_ballot
    }

    pub fn get_adaptive_check_interval(&self, connected_peers: usize, subscribed_peers: usize) -> Duration {
        // For single-node scenarios, use much longer intervals
        if connected_peers == 0 && subscribed_peers == 0 {
            return Duration::from_secs(2);
        }

        // For multi-node scenarios, use the most conservative interval from all shards
        let max_interval = self.shards.values()
            .map(|shard| shard.get_adaptive_check_interval())
            .max()
            .unwrap_or(Duration::from_millis(500));

        max_interval.max(Duration::from_millis(500))
    }

    pub fn should_check_consensus(&self, connected_peers: usize, subscribed_peers: usize) -> bool {
        // Don't check if all shards are in startup grace period
        let all_in_grace_period = self.shards.values()
            .all(|shard| shard.is_in_startup_grace_period());

        if all_in_grace_period {
            return false;
        }

        // For single-node scenarios, be very conservative
        if connected_peers == 0 && subscribed_peers == 0 {
            let min_uptime = self.shards.values()
                .map(|shard| std::time::Instant::now().duration_since(shard.startup_time))
                .min()
                .unwrap_or(Duration::ZERO);

            return min_uptime > Duration::from_secs(10);
        }

        true
    }

    pub fn is_single_node_scenario(&self, connected_peers: usize, subscribed_peers: usize) -> bool {
        connected_peers == 0 && subscribed_peers == 0
    }

    pub fn should_become_distinguished_proposer_immediately(&self, _shard_id: ShardId, connected_peers: usize, subscribed_peers: usize) -> bool {
        connected_peers == 0 && subscribed_peers == 0
    }
}

impl RaftState {
    pub fn new() -> Self {
        Self::with_config(ShardConfig::default())
    }

    pub fn with_config(config: ShardConfig) -> Self {
        let mut shards = HashMap::new();

        for shard_id in 0..config.shard_count {
            shards.insert(shard_id, ShardState::new(shard_id));
        }

        Self {
            shards,
            config,
            global_term: 0,
        }
    }
    
    pub fn get_shard_for_task(&self, task_id: [u8; 16]) -> ShardId {
        match self.config.assignment_strategy {
            ShardAssignmentStrategy::HashBased => {
                let mut hasher = DefaultHasher::new();
                task_id.hash(&mut hasher);
                (hasher.finish() % self.config.shard_count as u64) as ShardId
            }
            ShardAssignmentStrategy::RoundRobin => {
                // Simple round-robin based on task_id bytes
                let sum: u32 = task_id.iter().map(|&b| b as u32).sum();
                sum % self.config.shard_count
            }
            ShardAssignmentStrategy::ConsistentHashing => {
                // Simplified consistent hashing
                let mut hasher = DefaultHasher::new();
                task_id.hash(&mut hasher);
                let hash = hasher.finish();
                ((hash % (self.config.shard_count as u64 * 100)) / 100) as ShardId
            }
        }
    }
    
    pub fn get_shard_mut(&mut self, shard_id: ShardId) -> Option<&mut ShardState> {
        self.shards.get_mut(&shard_id)
    }
    
    pub fn get_shard(&self, shard_id: ShardId) -> Option<&ShardState> {
        self.shards.get(&shard_id)
    }
    
    pub fn get_leader_shards(&self) -> Vec<ShardId> {
        self.shards
            .iter()
            .filter(|(_, shard)| shard.state == NodeState::Leader)
            .map(|(&shard_id, _)| shard_id)
            .collect()
    }
    
    pub fn get_candidate_shards(&self) -> Vec<ShardId> {
        self.shards
            .iter()
            .filter(|(_, shard)| shard.state == NodeState::Candidate)
            .map(|(&shard_id, _)| shard_id)
            .collect()
    }
    
    pub fn get_shards_needing_election(&self) -> Vec<ShardId> {
        self.shards
            .iter()
            .filter(|(_, shard)| {
                shard.state != NodeState::Leader && shard.is_election_timeout()
            })
            .map(|(&shard_id, _)| shard_id)
            .collect()
    }
    
    pub fn update_global_term(&mut self) {
        let max_term = self.shards
            .values()
            .map(|shard| shard.current_term)
            .max()
            .unwrap_or(0);
        self.global_term = max_term;
    }
    
    pub fn can_start_election(&self, _shard_id: ShardId, peer_count: usize) -> bool {
        if peer_count == 0 {
            return true;
        }

        let total_nodes = peer_count + 1;
        total_nodes >= 2
    }
    
    pub fn should_become_leader_immediately(&self, _shard_id: ShardId, connected_peers: usize, subscribed_peers: usize) -> bool {
        connected_peers == 0 && subscribed_peers == 0
    }

    pub fn is_single_node_scenario(&self, connected_peers: usize, subscribed_peers: usize) -> bool {
        // Consider it a single node scenario if we have no connected or subscribed peers
        connected_peers == 0 && subscribed_peers == 0
    }

    pub fn should_start_election(&self, shard_id: ShardId, connected_peers: usize, subscribed_peers: usize) -> bool {
        if let Some(shard) = self.get_shard(shard_id) {
            shard.should_attempt_election(connected_peers, subscribed_peers)
        } else {
            false
        }
    }

    pub fn get_adaptive_election_check_interval(&self, connected_peers: usize, subscribed_peers: usize) -> Duration {
        // For single-node scenarios, use much longer intervals
        if connected_peers == 0 && subscribed_peers == 0 {
            return Duration::from_secs(2); // 2 second interval for single-node
        }

        // For multi-node scenarios, use the most conservative interval from all shards
        let max_interval = self.shards.values()
            .map(|shard| shard.get_adaptive_election_check_interval())
            .max()
            .unwrap_or(Duration::from_millis(500));

        // Ensure minimum interval of 500ms even in multi-node scenarios
        max_interval.max(Duration::from_millis(500))
    }

    pub fn has_any_recent_leader_activity(&self) -> bool {
        self.shards.values().any(|shard| shard.has_recent_leader_activity())
    }

    pub fn should_check_elections(&self, connected_peers: usize, subscribed_peers: usize) -> bool {
        // Don't check elections if we're already a leader
        if self.state() == NodeState::Leader {
            return false;
        }

        // Don't check if all shards are in startup grace period
        let all_in_grace_period = self.shards.values()
            .all(|shard| shard.is_in_startup_grace_period());

        if all_in_grace_period {
            return false;
        }

        // For single-node scenarios, be very conservative
        if connected_peers == 0 && subscribed_peers == 0 {
            // Only check if we've been running for a while and have no recent leader activity
            let min_uptime = self.shards.values()
                .map(|shard| std::time::Instant::now().duration_since(shard.startup_time))
                .min()
                .unwrap_or(Duration::ZERO);

            return min_uptime > Duration::from_secs(10) && !self.has_any_recent_leader_activity();
        }

        // For multi-node scenarios, proceed with normal checks
        true
    }
    
    // Legacy compatibility methods for single-shard operations
    pub fn current_term(&self) -> u64 {
        self.global_term
    }
    
    pub fn voted_for(&self) -> Option<PeerId> {
        // Return voted_for from first shard for compatibility
        self.shards.values().next().and_then(|shard| shard.voted_for)
    }
    
    pub fn state(&self) -> NodeState {
        // Return Leader if any shard is Leader, otherwise return first shard's state
        if self.shards.values().any(|shard| shard.state == NodeState::Leader) {
            NodeState::Leader
        } else {
            self.shards.values().next().map(|shard| shard.state.clone()).unwrap_or(NodeState::Follower)
        }
    }
    
    pub fn leader_id(&self) -> Option<PeerId> {
        // Return leader_id from first leader shard
        self.shards
            .values()
            .find(|shard| shard.state == NodeState::Leader)
            .and_then(|shard| shard.leader_id)
            .or_else(|| self.shards.values().next().and_then(|shard| shard.leader_id))
    }
    
    pub fn reset_election_timer(&mut self) {
        for shard in self.shards.values_mut() {
            shard.reset_election_timer();
        }
    }
    
    pub fn is_election_timeout(&self) -> bool {
        self.shards.values().any(|shard| shard.is_election_timeout())
    }
    
    pub fn become_candidate(&mut self, self_id: PeerId) {
        // Make all shards candidates for compatibility
        for shard in self.shards.values_mut() {
            shard.become_candidate(self_id);
        }
    }
    
    pub fn become_leader(&mut self, self_id: PeerId) {
        // Make all shards leaders for compatibility
        for shard in self.shards.values_mut() {
            shard.become_leader(self_id);
        }
        self.update_global_term();
    }
    
    pub fn become_leader_without_term_increment(&mut self, self_id: PeerId) {
        for shard in self.shards.values_mut() {
            shard.become_leader_without_term_increment(self_id);
        }
    }
    
    pub fn become_follower(&mut self, term: u64, leader_id: Option<PeerId>) {
        for shard in self.shards.values_mut() {
            shard.become_follower(term, leader_id);
        }
        self.global_term = term;
    }
    
    pub fn election_failed(&mut self) {
        for shard in self.shards.values_mut() {
            shard.election_failed();
        }
    }
    
    // Shard-specific operations
    pub fn start_election_with_peers(&mut self, shard_id: ShardId, self_id: PeerId, has_peers: bool) {
        if let Some(shard) = self.get_shard_mut(shard_id) {
            shard.state = NodeState::Candidate;

            // Only increment term if we have peers AND we're not in startup grace period
            if has_peers && !shard.is_in_startup_grace_period() {
                shard.current_term += 1;
                log::info!("Shard {} 开始选举，增加任期到: {}", shard_id, shard.current_term);
            } else if has_peers {
                log::info!("Shard {} 启动期间开始选举，保持任期: {}", shard_id, shard.current_term);
            } else {
                log::info!("Shard {} 单节点场景，保持任期: {}", shard_id, shard.current_term);
            }

            shard.voted_for = Some(self_id);
            shard.votes_received.clear();
            shard.votes_received.insert(self_id);
            shard.reset_election_timer();
            shard.last_election_attempt = Some(std::time::Instant::now());

            self.update_global_term();
        }
    }
    
    pub fn append_log_entry(&mut self, shard_id: ShardId, entry: LogEntry) {
        if let Some(shard) = self.get_shard_mut(shard_id) {
            shard.log.push(entry);
        }
    }
    
    pub fn get_log_entries(&self, shard_id: ShardId, from_index: u64) -> Vec<LogEntry> {
        if let Some(shard) = self.get_shard(shard_id) {
            shard.log
                .iter()
                .skip(from_index as usize)
                .cloned()
                .collect()
        } else {
            Vec::new()
        }
    }
    
    pub fn commit_entries(&mut self, shard_id: ShardId, commit_index: u64) {
        if let Some(shard) = self.get_shard_mut(shard_id) {
            shard.commit_index = commit_index.min(shard.log.len() as u64);
        }
    }
    
    pub fn get_commit_index(&self, shard_id: ShardId) -> u64 {
        self.get_shard(shard_id).map(|shard| shard.commit_index).unwrap_or(0)
    }
    
    pub fn get_last_log_index(&self, shard_id: ShardId) -> u64 {
        self.get_shard(shard_id).map(|shard| shard.log.len() as u64).unwrap_or(0)
    }
    
    pub fn get_last_log_term(&self, shard_id: ShardId) -> u64 {
        self.get_shard(shard_id)
            .and_then(|shard| shard.log.last())
            .map(|entry| entry.term)
            .unwrap_or(0)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    #[test]
    fn test_startup_grace_period() {
        let shard = ShardState::new(0);

        // Should be in startup grace period immediately after creation
        assert!(shard.is_in_startup_grace_period());

        // Should not trigger election timeout during grace period
        assert!(!shard.is_election_timeout());
    }

    #[test]
    fn test_startup_grace_period_expires() {
        let mut shard = ShardState::new(0);

        // Simulate startup grace period expiring by setting startup_time to past
        shard.startup_time = std::time::Instant::now() - Duration::from_secs(10);

        // Should no longer be in startup grace period
        assert!(!shard.is_in_startup_grace_period());
    }

    #[test]
    fn test_election_timeout_respects_grace_period() {
        let mut shard = ShardState::new(0);

        // Set last_heartbeat to past to simulate timeout condition
        shard.last_heartbeat = std::time::Instant::now() - Duration::from_secs(2);

        // Should not timeout during grace period
        assert!(!shard.is_election_timeout());

        // Expire grace period
        shard.startup_time = std::time::Instant::now() - Duration::from_secs(10);

        // Now should timeout
        assert!(shard.is_election_timeout());
    }

    #[test]
    fn test_term_increment_during_startup() {
        let mut raft_state = RaftState::new();
        let peer_id = PeerId::random();

        // During startup grace period, term should not increment
        raft_state.start_election_with_peers(0, peer_id, true);

        if let Some(shard) = raft_state.get_shard(0) {
            // Term should remain 0 during startup grace period
            assert_eq!(shard.current_term, 0);
        }
    }

    #[test]
    fn test_term_increment_after_startup() {
        let mut raft_state = RaftState::new();
        let peer_id = PeerId::random();

        // Expire startup grace period for all shards
        for shard in raft_state.shards.values_mut() {
            shard.startup_time = std::time::Instant::now() - Duration::from_secs(10);
        }

        // Now term should increment when starting election with peers
        raft_state.start_election_with_peers(0, peer_id, true);

        if let Some(shard) = raft_state.get_shard(0) {
            assert_eq!(shard.current_term, 1);
        }
    }

    #[test]
    fn test_single_node_detection() {
        let raft_state = RaftState::new();

        // Should detect single node scenario
        assert!(raft_state.is_single_node_scenario(0, 0));

        // Should not detect single node when peers exist
        assert!(!raft_state.is_single_node_scenario(1, 0));
        assert!(!raft_state.is_single_node_scenario(0, 1));
        assert!(!raft_state.is_single_node_scenario(1, 1));
    }

    #[test]
    fn test_should_start_election_during_startup() {
        let raft_state = RaftState::new();

        // Should not start election during startup grace period
        assert!(!raft_state.should_start_election(0, 1, 0));
        assert!(!raft_state.should_start_election(0, 0, 1));
    }

    #[test]
    fn test_should_start_election_after_startup() {
        let mut raft_state = RaftState::new();

        // Expire startup grace period and set election timeout
        for shard in raft_state.shards.values_mut() {
            shard.startup_time = std::time::Instant::now() - Duration::from_secs(10);
            shard.last_heartbeat = std::time::Instant::now() - Duration::from_secs(2);
        }

        // Should start election when conditions are met
        assert!(raft_state.should_start_election(0, 1, 0));
    }

    #[test]
    fn test_adaptive_election_check_interval() {
        let shard = ShardState::new(0);

        // Initial interval should be reasonable
        let interval = shard.get_adaptive_election_check_interval();
        assert!(interval >= Duration::from_millis(500));
        assert!(interval <= Duration::from_millis(1000));
    }

    #[test]
    fn test_backoff_after_election_failures() {
        let mut shard = ShardState::new(0);

        // Simulate multiple election failures
        shard.election_failed();
        shard.election_failed();
        shard.election_failed();

        assert_eq!(shard.consecutive_election_failures, 3);
        assert!(shard.backoff_multiplier > 1);

        // Interval should increase with failures
        let interval = shard.get_adaptive_election_check_interval();
        assert!(interval > Duration::from_millis(1000));
    }

    #[test]
    fn test_should_attempt_election_with_backoff() {
        let mut shard = ShardState::new(0);

        // Expire startup grace period
        shard.startup_time = std::time::Instant::now() - Duration::from_secs(10);
        shard.last_heartbeat = std::time::Instant::now() - Duration::from_secs(2);

        // Should attempt initially
        assert!(shard.should_attempt_election(1, 0));

        // Simulate election failure and recent attempt
        shard.election_failed();
        shard.last_election_attempt = Some(std::time::Instant::now());

        // Should not attempt immediately due to backoff
        assert!(!shard.should_attempt_election(1, 0));
    }

    #[test]
    fn test_single_node_adaptive_interval() {
        let raft_state = RaftState::new();

        // Single-node scenario should have longer interval
        let single_node_interval = raft_state.get_adaptive_election_check_interval(0, 0);
        let multi_node_interval = raft_state.get_adaptive_election_check_interval(1, 0);

        assert!(single_node_interval > multi_node_interval);
        assert_eq!(single_node_interval, Duration::from_secs(2));
    }

    #[test]
    fn test_should_check_elections() {
        let mut raft_state = RaftState::new();

        // Should not check during startup grace period
        assert!(!raft_state.should_check_elections(1, 0));

        // Expire grace period
        for shard in raft_state.shards.values_mut() {
            shard.startup_time = std::time::Instant::now() - Duration::from_secs(10);
        }

        // Should check for multi-node
        assert!(raft_state.should_check_elections(1, 0));

        // Should be conservative for single-node initially (within 10 seconds)
        for shard in raft_state.shards.values_mut() {
            shard.startup_time = std::time::Instant::now() - Duration::from_secs(8);
        }
        assert!(!raft_state.should_check_elections(0, 0));

        // Should allow single-node checks after sufficient time with no leader activity
        for shard in raft_state.shards.values_mut() {
            shard.startup_time = std::time::Instant::now() - Duration::from_secs(15);
        }
        assert!(raft_state.should_check_elections(0, 0));
    }

    #[test]
    fn test_recent_leader_activity() {
        let mut shard = ShardState::new(0);

        // No recent activity initially
        assert!(!shard.has_recent_leader_activity());

        // Set recent leader activity
        shard.last_leader_seen = Some(std::time::Instant::now());
        assert!(shard.has_recent_leader_activity());

        // Old activity should not count
        shard.last_leader_seen = Some(std::time::Instant::now() - Duration::from_secs(60));
        assert!(!shard.has_recent_leader_activity());
    }
}
