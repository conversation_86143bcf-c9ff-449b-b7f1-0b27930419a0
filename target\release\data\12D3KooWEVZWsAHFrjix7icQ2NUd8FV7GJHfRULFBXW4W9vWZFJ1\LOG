2025/07/26-18:24:05.204939 36f8 RocksDB version: 8.10.0
2025/07/26-18:24:05.205965 36f8 Compile date 2023-12-15 13:01:14
2025/07/26-18:24:05.205977 36f8 DB SUMMARY
2025/07/26-18:24:05.205984 36f8 Host name (Env):  DESKTOP-RNBFHG8
2025/07/26-18:24:05.205990 36f8 DB Session ID:  FSX4TWMD6N3JXO5FWX2Y
2025/07/26-18:24:05.206137 36f8 SST files in ./data/12D3KooWEVZWsAHFrjix7icQ2NUd8FV7GJHfRULFBXW4W9vWZFJ1 dir, Total Num: 0, files: 
2025/07/26-18:24:05.206144 36f8 Write Ahead Log file in ./data/12D3KooWEVZWsAHFrjix7icQ2NUd8FV7GJHfRULFBXW4W9vWZFJ1: 
2025/07/26-18:24:05.206150 36f8                         Options.error_if_exists: 0
2025/07/26-18:24:05.206155 36f8                       Options.create_if_missing: 1
2025/07/26-18:24:05.206160 36f8                         Options.paranoid_checks: 1
2025/07/26-18:24:05.206261 36f8             Options.flush_verify_memtable_count: 1
2025/07/26-18:24:05.206268 36f8          Options.compaction_verify_record_count: 1
2025/07/26-18:24:05.206270 36f8                               Options.track_and_verify_wals_in_manifest: 0
2025/07/26-18:24:05.206272 36f8        Options.verify_sst_unique_id_in_manifest: 1
2025/07/26-18:24:05.206274 36f8                                     Options.env: 0000027BF5F7D500
2025/07/26-18:24:05.206276 36f8                                      Options.fs: WinFS
2025/07/26-18:24:05.206278 36f8                                Options.info_log: 0000027BF5F16F30
2025/07/26-18:24:05.206279 36f8                Options.max_file_opening_threads: 16
2025/07/26-18:24:05.206281 36f8                              Options.statistics: 0000000000000000
2025/07/26-18:24:05.206283 36f8                               Options.use_fsync: 0
2025/07/26-18:24:05.206285 36f8                       Options.max_log_file_size: 0
2025/07/26-18:24:05.206287 36f8                  Options.max_manifest_file_size: 1073741824
2025/07/26-18:24:05.206288 36f8                   Options.log_file_time_to_roll: 0
2025/07/26-18:24:05.206290 36f8                       Options.keep_log_file_num: 1000
2025/07/26-18:24:05.206292 36f8                    Options.recycle_log_file_num: 0
2025/07/26-18:24:05.206293 36f8                         Options.allow_fallocate: 1
2025/07/26-18:24:05.206295 36f8                        Options.allow_mmap_reads: 0
2025/07/26-18:24:05.206297 36f8                       Options.allow_mmap_writes: 0
2025/07/26-18:24:05.206298 36f8                        Options.use_direct_reads: 0
2025/07/26-18:24:05.206300 36f8                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/26-18:24:05.206302 36f8          Options.create_missing_column_families: 1
2025/07/26-18:24:05.206304 36f8                              Options.db_log_dir: 
2025/07/26-18:24:05.206305 36f8                                 Options.wal_dir: 
2025/07/26-18:24:05.206307 36f8                Options.table_cache_numshardbits: 6
2025/07/26-18:24:05.206309 36f8                         Options.WAL_ttl_seconds: 0
2025/07/26-18:24:05.206310 36f8                       Options.WAL_size_limit_MB: 0
2025/07/26-18:24:05.206312 36f8                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/26-18:24:05.206314 36f8             Options.manifest_preallocation_size: 4194304
2025/07/26-18:24:05.206316 36f8                     Options.is_fd_close_on_exec: 1
2025/07/26-18:24:05.206317 36f8                   Options.advise_random_on_open: 1
2025/07/26-18:24:05.206319 36f8                    Options.db_write_buffer_size: 0
2025/07/26-18:24:05.206321 36f8                    Options.write_buffer_manager: 0000027BF5F7CE70
2025/07/26-18:24:05.206322 36f8         Options.access_hint_on_compaction_start: 1
2025/07/26-18:24:05.206324 36f8           Options.random_access_max_buffer_size: 1048576
2025/07/26-18:24:05.206325 36f8                      Options.use_adaptive_mutex: 0
2025/07/26-18:24:05.206327 36f8                            Options.rate_limiter: 0000000000000000
2025/07/26-18:24:05.206329 36f8     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/26-18:24:05.206331 36f8                       Options.wal_recovery_mode: 2
2025/07/26-18:24:05.206350 36f8                  Options.enable_thread_tracking: 0
2025/07/26-18:24:05.206353 36f8                  Options.enable_pipelined_write: 0
2025/07/26-18:24:05.206355 36f8                  Options.unordered_write: 0
2025/07/26-18:24:05.206356 36f8         Options.allow_concurrent_memtable_write: 1
2025/07/26-18:24:05.206358 36f8      Options.enable_write_thread_adaptive_yield: 1
2025/07/26-18:24:05.206359 36f8             Options.write_thread_max_yield_usec: 100
2025/07/26-18:24:05.206361 36f8            Options.write_thread_slow_yield_usec: 3
2025/07/26-18:24:05.206363 36f8                               Options.row_cache: None
2025/07/26-18:24:05.206365 36f8                              Options.wal_filter: None
2025/07/26-18:24:05.206367 36f8             Options.avoid_flush_during_recovery: 0
2025/07/26-18:24:05.206368 36f8             Options.allow_ingest_behind: 0
2025/07/26-18:24:05.206370 36f8             Options.two_write_queues: 0
2025/07/26-18:24:05.206371 36f8             Options.manual_wal_flush: 0
2025/07/26-18:24:05.206373 36f8             Options.wal_compression: 0
2025/07/26-18:24:05.206375 36f8             Options.atomic_flush: 0
2025/07/26-18:24:05.206376 36f8             Options.avoid_unnecessary_blocking_io: 0
2025/07/26-18:24:05.206378 36f8                 Options.persist_stats_to_disk: 0
2025/07/26-18:24:05.206380 36f8                 Options.write_dbid_to_manifest: 0
2025/07/26-18:24:05.206381 36f8                 Options.log_readahead_size: 0
2025/07/26-18:24:05.206383 36f8                 Options.file_checksum_gen_factory: Unknown
2025/07/26-18:24:05.206391 36f8                 Options.best_efforts_recovery: 0
2025/07/26-18:24:05.206392 36f8                Options.max_bgerror_resume_count: 2147483647
2025/07/26-18:24:05.206394 36f8            Options.bgerror_resume_retry_interval: 1000000
2025/07/26-18:24:05.206396 36f8             Options.allow_data_in_errors: 0
2025/07/26-18:24:05.206397 36f8             Options.db_host_id: __hostname__
2025/07/26-18:24:05.206399 36f8             Options.enforce_single_del_contracts: true
2025/07/26-18:24:05.206401 36f8             Options.max_background_jobs: 4
2025/07/26-18:24:05.206403 36f8             Options.max_background_compactions: -1
2025/07/26-18:24:05.206405 36f8             Options.max_subcompactions: 1
2025/07/26-18:24:05.206406 36f8             Options.avoid_flush_during_shutdown: 0
2025/07/26-18:24:05.206408 36f8           Options.writable_file_max_buffer_size: 1048576
2025/07/26-18:24:05.206410 36f8             Options.delayed_write_rate : 16777216
2025/07/26-18:24:05.206411 36f8             Options.max_total_wal_size: 0
2025/07/26-18:24:05.206413 36f8             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/26-18:24:05.206415 36f8                   Options.stats_dump_period_sec: 600
2025/07/26-18:24:05.206417 36f8                 Options.stats_persist_period_sec: 600
2025/07/26-18:24:05.206418 36f8                 Options.stats_history_buffer_size: 1048576
2025/07/26-18:24:05.206420 36f8                          Options.max_open_files: 1000
2025/07/26-18:24:05.206422 36f8                          Options.bytes_per_sync: 0
2025/07/26-18:24:05.206423 36f8                      Options.wal_bytes_per_sync: 0
2025/07/26-18:24:05.206425 36f8                   Options.strict_bytes_per_sync: 0
2025/07/26-18:24:05.206427 36f8       Options.compaction_readahead_size: 2097152
2025/07/26-18:24:05.206429 36f8                  Options.max_background_flushes: -1
2025/07/26-18:24:05.206430 36f8 Options.daily_offpeak_time_utc: 
2025/07/26-18:24:05.206432 36f8 Compression algorithms supported:
2025/07/26-18:24:05.206435 36f8 	kZSTD supported: 1
2025/07/26-18:24:05.206437 36f8 	kSnappyCompression supported: 1
2025/07/26-18:24:05.206439 36f8 	kBZip2Compression supported: 1
2025/07/26-18:24:05.206440 36f8 	kZlibCompression supported: 1
2025/07/26-18:24:05.206442 36f8 	kLZ4Compression supported: 1
2025/07/26-18:24:05.206444 36f8 	kXpressCompression supported: 0
2025/07/26-18:24:05.206446 36f8 	kLZ4HCCompression supported: 1
2025/07/26-18:24:05.206458 36f8 	kZSTDNotFinalCompression supported: 1
2025/07/26-18:24:05.206462 36f8 Fast CRC32 supported: Not supported on x86
2025/07/26-18:24:05.206464 36f8 DMutex implementation: std::mutex
2025/07/26-18:24:05.212598 36f8 [db/db_impl/db_impl_open.cc:325] Creating manifest 1 
2025/07/26-18:24:05.222791 36f8 [db/version_set.cc:5942] Recovering from manifest file: ./data/12D3KooWEVZWsAHFrjix7icQ2NUd8FV7GJHfRULFBXW4W9vWZFJ1/MANIFEST-000001
2025/07/26-18:24:05.222919 36f8 [db/column_family.cc:618] --------------- Options for column family [default]:
2025/07/26-18:24:05.222923 36f8               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:24:05.222926 36f8           Options.merge_operator: None
2025/07/26-18:24:05.222928 36f8        Options.compaction_filter: None
2025/07/26-18:24:05.222930 36f8        Options.compaction_filter_factory: None
2025/07/26-18:24:05.222931 36f8  Options.sst_partitioner_factory: None
2025/07/26-18:24:05.222933 36f8         Options.memtable_factory: SkipListFactory
2025/07/26-18:24:05.222935 36f8            Options.table_factory: BlockBasedTable
2025/07/26-18:24:05.222973 36f8            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000027BF5F52090)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000027BF5F7D7E0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:24:05.222975 36f8        Options.write_buffer_size: 67108864
2025/07/26-18:24:05.222977 36f8  Options.max_write_buffer_number: 2
2025/07/26-18:24:05.222979 36f8          Options.compression: Snappy
2025/07/26-18:24:05.222981 36f8                  Options.bottommost_compression: Disabled
2025/07/26-18:24:05.222982 36f8       Options.prefix_extractor: nullptr
2025/07/26-18:24:05.222984 36f8   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:24:05.222986 36f8             Options.num_levels: 7
2025/07/26-18:24:05.222987 36f8        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:24:05.222989 36f8     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:24:05.222990 36f8     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:24:05.222992 36f8            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:24:05.222994 36f8                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:24:05.222996 36f8               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:24:05.222997 36f8         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:24:05.222999 36f8         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:05.223001 36f8         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:24:05.223002 36f8                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:24:05.223004 36f8         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:05.223006 36f8         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:05.223008 36f8            Options.compression_opts.window_bits: -14
2025/07/26-18:24:05.223012 36f8                  Options.compression_opts.level: 32767
2025/07/26-18:24:05.223014 36f8               Options.compression_opts.strategy: 0
2025/07/26-18:24:05.223016 36f8         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:24:05.223017 36f8         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:05.223019 36f8         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:05.223021 36f8         Options.compression_opts.parallel_threads: 1
2025/07/26-18:24:05.223022 36f8                  Options.compression_opts.enabled: false
2025/07/26-18:24:05.223024 36f8         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:05.223026 36f8      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:24:05.223027 36f8          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:24:05.223029 36f8              Options.level0_stop_writes_trigger: 36
2025/07/26-18:24:05.223031 36f8                   Options.target_file_size_base: 67108864
2025/07/26-18:24:05.223032 36f8             Options.target_file_size_multiplier: 1
2025/07/26-18:24:05.223034 36f8                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:24:05.223036 36f8 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:24:05.223037 36f8          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:24:05.223039 36f8 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:24:05.223041 36f8 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:24:05.223043 36f8 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:24:05.223044 36f8 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:24:05.223046 36f8 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:24:05.223048 36f8 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:24:05.223049 36f8 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:24:05.223051 36f8       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:24:05.223053 36f8                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:24:05.223054 36f8   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:24:05.223056 36f8                        Options.arena_block_size: 1048576
2025/07/26-18:24:05.223058 36f8   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:24:05.223059 36f8   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:24:05.223061 36f8                Options.disable_auto_compactions: 0
2025/07/26-18:24:05.223063 36f8                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:24:05.223066 36f8                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:24:05.223068 36f8 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:24:05.223070 36f8 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:24:05.223071 36f8 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:24:05.223073 36f8 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:24:05.223075 36f8 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:24:05.223077 36f8 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:24:05.223078 36f8 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:24:05.223080 36f8 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:24:05.223083 36f8                   Options.table_properties_collectors: 
2025/07/26-18:24:05.223085 36f8                   Options.inplace_update_support: 0
2025/07/26-18:24:05.223086 36f8                 Options.inplace_update_num_locks: 10000
2025/07/26-18:24:05.223088 36f8               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:24:05.223090 36f8               Options.memtable_whole_key_filtering: 0
2025/07/26-18:24:05.223091 36f8   Options.memtable_huge_page_size: 0
2025/07/26-18:24:05.223122 36f8                           Options.bloom_locality: 0
2025/07/26-18:24:05.223124 36f8                    Options.max_successive_merges: 0
2025/07/26-18:24:05.223126 36f8                Options.optimize_filters_for_hits: 0
2025/07/26-18:24:05.223128 36f8                Options.paranoid_file_checks: 0
2025/07/26-18:24:05.223130 36f8                Options.force_consistency_checks: 1
2025/07/26-18:24:05.223131 36f8                Options.report_bg_io_stats: 0
2025/07/26-18:24:05.223133 36f8                               Options.ttl: 2592000
2025/07/26-18:24:05.223135 36f8          Options.periodic_compaction_seconds: 0
2025/07/26-18:24:05.223137 36f8                        Options.default_temperature: kUnknown
2025/07/26-18:24:05.223138 36f8  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:24:05.223140 36f8    Options.preserve_internal_time_seconds: 0
2025/07/26-18:24:05.223142 36f8                       Options.enable_blob_files: false
2025/07/26-18:24:05.223143 36f8                           Options.min_blob_size: 0
2025/07/26-18:24:05.223145 36f8                          Options.blob_file_size: 268435456
2025/07/26-18:24:05.223147 36f8                   Options.blob_compression_type: NoCompression
2025/07/26-18:24:05.223148 36f8          Options.enable_blob_garbage_collection: false
2025/07/26-18:24:05.223150 36f8      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:24:05.223152 36f8 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:24:05.223154 36f8          Options.blob_compaction_readahead_size: 0
2025/07/26-18:24:05.223156 36f8                Options.blob_file_starting_level: 0
2025/07/26-18:24:05.223157 36f8         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:24:05.223159 36f8            Options.memtable_max_range_deletions: 0
2025/07/26-18:24:05.223969 36f8 [db/version_set.cc:5993] Recovered from manifest file:./data/12D3KooWEVZWsAHFrjix7icQ2NUd8FV7GJHfRULFBXW4W9vWZFJ1/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/07/26-18:24:05.223980 36f8 [db/version_set.cc:6002] Column family [default] (ID 0), log number is 0
2025/07/26-18:24:05.224212 36f8 [db/db_impl/db_impl_open.cc:646] DB ID: a7914a29-6a0a-11f0-98b6-d4e98a1a402d
2025/07/26-18:24:05.225189 36f8 [db/version_set.cc:5439] Creating manifest 5
2025/07/26-18:24:05.236204 36f8 [db/column_family.cc:618] --------------- Options for column family [tasks]:
2025/07/26-18:24:05.236222 36f8               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:24:05.236224 36f8           Options.merge_operator: None
2025/07/26-18:24:05.236226 36f8        Options.compaction_filter: None
2025/07/26-18:24:05.236227 36f8        Options.compaction_filter_factory: None
2025/07/26-18:24:05.236229 36f8  Options.sst_partitioner_factory: None
2025/07/26-18:24:05.236231 36f8         Options.memtable_factory: SkipListFactory
2025/07/26-18:24:05.236232 36f8            Options.table_factory: BlockBasedTable
2025/07/26-18:24:05.236257 36f8            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000027BF5F58B40)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000027BF5F7DBA0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:24:05.236263 36f8        Options.write_buffer_size: 67108864
2025/07/26-18:24:05.236267 36f8  Options.max_write_buffer_number: 2
2025/07/26-18:24:05.236269 36f8          Options.compression: Snappy
2025/07/26-18:24:05.236271 36f8                  Options.bottommost_compression: Disabled
2025/07/26-18:24:05.236272 36f8       Options.prefix_extractor: nullptr
2025/07/26-18:24:05.236274 36f8   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:24:05.236276 36f8             Options.num_levels: 7
2025/07/26-18:24:05.236277 36f8        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:24:05.236279 36f8     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:24:05.236281 36f8     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:24:05.236282 36f8            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:24:05.236284 36f8                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:24:05.236286 36f8               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:24:05.236287 36f8         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:24:05.236289 36f8         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:05.236291 36f8         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:24:05.236293 36f8                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:24:05.236294 36f8         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:05.236296 36f8         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:05.236298 36f8            Options.compression_opts.window_bits: -14
2025/07/26-18:24:05.236300 36f8                  Options.compression_opts.level: 32767
2025/07/26-18:24:05.236301 36f8               Options.compression_opts.strategy: 0
2025/07/26-18:24:05.236303 36f8         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:24:05.236304 36f8         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:05.236306 36f8         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:05.236308 36f8         Options.compression_opts.parallel_threads: 1
2025/07/26-18:24:05.236309 36f8                  Options.compression_opts.enabled: false
2025/07/26-18:24:05.236311 36f8         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:05.236313 36f8      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:24:05.236314 36f8          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:24:05.236316 36f8              Options.level0_stop_writes_trigger: 36
2025/07/26-18:24:05.236318 36f8                   Options.target_file_size_base: 67108864
2025/07/26-18:24:05.236319 36f8             Options.target_file_size_multiplier: 1
2025/07/26-18:24:05.236321 36f8                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:24:05.236323 36f8 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:24:05.236324 36f8          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:24:05.236327 36f8 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:24:05.236328 36f8 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:24:05.236330 36f8 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:24:05.236332 36f8 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:24:05.236333 36f8 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:24:05.236335 36f8 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:24:05.236336 36f8 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:24:05.236338 36f8       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:24:05.236340 36f8                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:24:05.236342 36f8   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:24:05.236344 36f8                        Options.arena_block_size: 1048576
2025/07/26-18:24:05.236346 36f8   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:24:05.236348 36f8   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:24:05.236350 36f8                Options.disable_auto_compactions: 0
2025/07/26-18:24:05.236352 36f8                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:24:05.236354 36f8                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:24:05.236356 36f8 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:24:05.236358 36f8 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:24:05.236359 36f8 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:24:05.236361 36f8 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:24:05.236363 36f8 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:24:05.236365 36f8 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:24:05.236367 36f8 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:24:05.236369 36f8 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:24:05.236373 36f8                   Options.table_properties_collectors: 
2025/07/26-18:24:05.236375 36f8                   Options.inplace_update_support: 0
2025/07/26-18:24:05.236376 36f8                 Options.inplace_update_num_locks: 10000
2025/07/26-18:24:05.236378 36f8               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:24:05.236380 36f8               Options.memtable_whole_key_filtering: 0
2025/07/26-18:24:05.236382 36f8   Options.memtable_huge_page_size: 0
2025/07/26-18:24:05.236383 36f8                           Options.bloom_locality: 0
2025/07/26-18:24:05.236385 36f8                    Options.max_successive_merges: 0
2025/07/26-18:24:05.236387 36f8                Options.optimize_filters_for_hits: 0
2025/07/26-18:24:05.236388 36f8                Options.paranoid_file_checks: 0
2025/07/26-18:24:05.236390 36f8                Options.force_consistency_checks: 1
2025/07/26-18:24:05.236392 36f8                Options.report_bg_io_stats: 0
2025/07/26-18:24:05.236393 36f8                               Options.ttl: 2592000
2025/07/26-18:24:05.236395 36f8          Options.periodic_compaction_seconds: 0
2025/07/26-18:24:05.236397 36f8                        Options.default_temperature: kUnknown
2025/07/26-18:24:05.236399 36f8  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:24:05.236400 36f8    Options.preserve_internal_time_seconds: 0
2025/07/26-18:24:05.236402 36f8                       Options.enable_blob_files: false
2025/07/26-18:24:05.236404 36f8                           Options.min_blob_size: 0
2025/07/26-18:24:05.236405 36f8                          Options.blob_file_size: 268435456
2025/07/26-18:24:05.236407 36f8                   Options.blob_compression_type: NoCompression
2025/07/26-18:24:05.236409 36f8          Options.enable_blob_garbage_collection: false
2025/07/26-18:24:05.236410 36f8      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:24:05.236412 36f8 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:24:05.236414 36f8          Options.blob_compaction_readahead_size: 0
2025/07/26-18:24:05.236416 36f8                Options.blob_file_starting_level: 0
2025/07/26-18:24:05.236417 36f8         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:24:05.236419 36f8            Options.memtable_max_range_deletions: 0
2025/07/26-18:24:05.236609 36f8 [db/db_impl/db_impl.cc:3637] Created column family [tasks] (ID 1)
2025/07/26-18:24:05.239107 36f8 [db/column_family.cc:618] --------------- Options for column family [raft_state]:
2025/07/26-18:24:05.239116 36f8               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:24:05.239120 36f8           Options.merge_operator: None
2025/07/26-18:24:05.239123 36f8        Options.compaction_filter: None
2025/07/26-18:24:05.239124 36f8        Options.compaction_filter_factory: None
2025/07/26-18:24:05.239126 36f8  Options.sst_partitioner_factory: None
2025/07/26-18:24:05.239128 36f8         Options.memtable_factory: SkipListFactory
2025/07/26-18:24:05.239129 36f8            Options.table_factory: BlockBasedTable
2025/07/26-18:24:05.239151 36f8            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000027BF5F572E0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000027BF5F7D240
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:24:05.239154 36f8        Options.write_buffer_size: 67108864
2025/07/26-18:24:05.239155 36f8  Options.max_write_buffer_number: 2
2025/07/26-18:24:05.239157 36f8          Options.compression: Snappy
2025/07/26-18:24:05.239159 36f8                  Options.bottommost_compression: Disabled
2025/07/26-18:24:05.239160 36f8       Options.prefix_extractor: nullptr
2025/07/26-18:24:05.239162 36f8   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:24:05.239164 36f8             Options.num_levels: 7
2025/07/26-18:24:05.239165 36f8        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:24:05.239167 36f8     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:24:05.239169 36f8     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:24:05.239170 36f8            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:24:05.239172 36f8                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:24:05.239174 36f8               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:24:05.239175 36f8         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:24:05.239177 36f8         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:05.239179 36f8         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:24:05.239180 36f8                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:24:05.239182 36f8         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:05.239184 36f8         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:05.239186 36f8            Options.compression_opts.window_bits: -14
2025/07/26-18:24:05.239188 36f8                  Options.compression_opts.level: 32767
2025/07/26-18:24:05.239189 36f8               Options.compression_opts.strategy: 0
2025/07/26-18:24:05.239191 36f8         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:24:05.239192 36f8         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:05.239194 36f8         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:05.239196 36f8         Options.compression_opts.parallel_threads: 1
2025/07/26-18:24:05.239197 36f8                  Options.compression_opts.enabled: false
2025/07/26-18:24:05.239229 36f8         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:05.239231 36f8      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:24:05.239233 36f8          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:24:05.239234 36f8              Options.level0_stop_writes_trigger: 36
2025/07/26-18:24:05.239236 36f8                   Options.target_file_size_base: 67108864
2025/07/26-18:24:05.239238 36f8             Options.target_file_size_multiplier: 1
2025/07/26-18:24:05.239239 36f8                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:24:05.239241 36f8 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:24:05.239243 36f8          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:24:05.239245 36f8 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:24:05.239247 36f8 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:24:05.239249 36f8 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:24:05.239251 36f8 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:24:05.239252 36f8 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:24:05.239254 36f8 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:24:05.239256 36f8 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:24:05.239257 36f8       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:24:05.239259 36f8                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:24:05.239261 36f8   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:24:05.239262 36f8                        Options.arena_block_size: 1048576
2025/07/26-18:24:05.239264 36f8   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:24:05.239266 36f8   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:24:05.239268 36f8                Options.disable_auto_compactions: 0
2025/07/26-18:24:05.239270 36f8                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:24:05.239272 36f8                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:24:05.239274 36f8 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:24:05.239276 36f8 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:24:05.239277 36f8 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:24:05.239279 36f8 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:24:05.239281 36f8 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:24:05.239283 36f8 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:24:05.239284 36f8 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:24:05.239286 36f8 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:24:05.239291 36f8                   Options.table_properties_collectors: 
2025/07/26-18:24:05.239293 36f8                   Options.inplace_update_support: 0
2025/07/26-18:24:05.239295 36f8                 Options.inplace_update_num_locks: 10000
2025/07/26-18:24:05.239296 36f8               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:24:05.239300 36f8               Options.memtable_whole_key_filtering: 0
2025/07/26-18:24:05.239303 36f8   Options.memtable_huge_page_size: 0
2025/07/26-18:24:05.239304 36f8                           Options.bloom_locality: 0
2025/07/26-18:24:05.239306 36f8                    Options.max_successive_merges: 0
2025/07/26-18:24:05.239308 36f8                Options.optimize_filters_for_hits: 0
2025/07/26-18:24:05.239310 36f8                Options.paranoid_file_checks: 0
2025/07/26-18:24:05.239311 36f8                Options.force_consistency_checks: 1
2025/07/26-18:24:05.239313 36f8                Options.report_bg_io_stats: 0
2025/07/26-18:24:05.239315 36f8                               Options.ttl: 2592000
2025/07/26-18:24:05.239316 36f8          Options.periodic_compaction_seconds: 0
2025/07/26-18:24:05.239319 36f8                        Options.default_temperature: kUnknown
2025/07/26-18:24:05.239321 36f8  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:24:05.239322 36f8    Options.preserve_internal_time_seconds: 0
2025/07/26-18:24:05.239324 36f8                       Options.enable_blob_files: false
2025/07/26-18:24:05.239326 36f8                           Options.min_blob_size: 0
2025/07/26-18:24:05.239327 36f8                          Options.blob_file_size: 268435456
2025/07/26-18:24:05.239329 36f8                   Options.blob_compression_type: NoCompression
2025/07/26-18:24:05.239331 36f8          Options.enable_blob_garbage_collection: false
2025/07/26-18:24:05.239333 36f8      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:24:05.239335 36f8 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:24:05.239337 36f8          Options.blob_compaction_readahead_size: 0
2025/07/26-18:24:05.239338 36f8                Options.blob_file_starting_level: 0
2025/07/26-18:24:05.239340 36f8         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:24:05.239342 36f8            Options.memtable_max_range_deletions: 0
2025/07/26-18:24:05.239492 36f8 [db/db_impl/db_impl.cc:3637] Created column family [raft_state] (ID 2)
2025/07/26-18:24:05.242081 36f8 [db/column_family.cc:618] --------------- Options for column family [task_versions]:
2025/07/26-18:24:05.242090 36f8               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:24:05.242091 36f8           Options.merge_operator: None
2025/07/26-18:24:05.242093 36f8        Options.compaction_filter: None
2025/07/26-18:24:05.242095 36f8        Options.compaction_filter_factory: None
2025/07/26-18:24:05.242096 36f8  Options.sst_partitioner_factory: None
2025/07/26-18:24:05.242098 36f8         Options.memtable_factory: SkipListFactory
2025/07/26-18:24:05.242100 36f8            Options.table_factory: BlockBasedTable
2025/07/26-18:24:05.242116 36f8            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000027BF5F57430)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000027BF5F7CD90
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:24:05.242118 36f8        Options.write_buffer_size: 67108864
2025/07/26-18:24:05.242120 36f8  Options.max_write_buffer_number: 2
2025/07/26-18:24:05.242121 36f8          Options.compression: Snappy
2025/07/26-18:24:05.242123 36f8                  Options.bottommost_compression: Disabled
2025/07/26-18:24:05.242125 36f8       Options.prefix_extractor: nullptr
2025/07/26-18:24:05.242126 36f8   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:24:05.242128 36f8             Options.num_levels: 7
2025/07/26-18:24:05.242130 36f8        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:24:05.242131 36f8     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:24:05.242133 36f8     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:24:05.242135 36f8            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:24:05.242138 36f8                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:24:05.242141 36f8               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:24:05.242143 36f8         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:24:05.242144 36f8         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:05.242146 36f8         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:24:05.242148 36f8                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:24:05.242149 36f8         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:05.242151 36f8         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:05.242153 36f8            Options.compression_opts.window_bits: -14
2025/07/26-18:24:05.242154 36f8                  Options.compression_opts.level: 32767
2025/07/26-18:24:05.242156 36f8               Options.compression_opts.strategy: 0
2025/07/26-18:24:05.242158 36f8         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:24:05.242159 36f8         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:05.242161 36f8         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:05.242163 36f8         Options.compression_opts.parallel_threads: 1
2025/07/26-18:24:05.242164 36f8                  Options.compression_opts.enabled: false
2025/07/26-18:24:05.242166 36f8         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:05.242168 36f8      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:24:05.242169 36f8          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:24:05.242171 36f8              Options.level0_stop_writes_trigger: 36
2025/07/26-18:24:05.242173 36f8                   Options.target_file_size_base: 67108864
2025/07/26-18:24:05.242174 36f8             Options.target_file_size_multiplier: 1
2025/07/26-18:24:05.242176 36f8                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:24:05.242178 36f8 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:24:05.242179 36f8          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:24:05.242181 36f8 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:24:05.242183 36f8 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:24:05.242185 36f8 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:24:05.242186 36f8 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:24:05.242188 36f8 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:24:05.242190 36f8 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:24:05.242191 36f8 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:24:05.242193 36f8       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:24:05.242195 36f8                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:24:05.242196 36f8   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:24:05.242198 36f8                        Options.arena_block_size: 1048576
2025/07/26-18:24:05.242200 36f8   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:24:05.242201 36f8   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:24:05.242203 36f8                Options.disable_auto_compactions: 0
2025/07/26-18:24:05.242206 36f8                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:24:05.242208 36f8                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:24:05.242210 36f8 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:24:05.242211 36f8 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:24:05.242213 36f8 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:24:05.242215 36f8 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:24:05.242216 36f8 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:24:05.242219 36f8 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:24:05.242221 36f8 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:24:05.242223 36f8 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:24:05.242226 36f8                   Options.table_properties_collectors: 
2025/07/26-18:24:05.242228 36f8                   Options.inplace_update_support: 0
2025/07/26-18:24:05.242230 36f8                 Options.inplace_update_num_locks: 10000
2025/07/26-18:24:05.242232 36f8               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:24:05.242233 36f8               Options.memtable_whole_key_filtering: 0
2025/07/26-18:24:05.242235 36f8   Options.memtable_huge_page_size: 0
2025/07/26-18:24:05.242237 36f8                           Options.bloom_locality: 0
2025/07/26-18:24:05.242238 36f8                    Options.max_successive_merges: 0
2025/07/26-18:24:05.242240 36f8                Options.optimize_filters_for_hits: 0
2025/07/26-18:24:05.242242 36f8                Options.paranoid_file_checks: 0
2025/07/26-18:24:05.242243 36f8                Options.force_consistency_checks: 1
2025/07/26-18:24:05.242245 36f8                Options.report_bg_io_stats: 0
2025/07/26-18:24:05.242247 36f8                               Options.ttl: 2592000
2025/07/26-18:24:05.242248 36f8          Options.periodic_compaction_seconds: 0
2025/07/26-18:24:05.242250 36f8                        Options.default_temperature: kUnknown
2025/07/26-18:24:05.242252 36f8  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:24:05.242253 36f8    Options.preserve_internal_time_seconds: 0
2025/07/26-18:24:05.242255 36f8                       Options.enable_blob_files: false
2025/07/26-18:24:05.242256 36f8                           Options.min_blob_size: 0
2025/07/26-18:24:05.242258 36f8                          Options.blob_file_size: 268435456
2025/07/26-18:24:05.242260 36f8                   Options.blob_compression_type: NoCompression
2025/07/26-18:24:05.242261 36f8          Options.enable_blob_garbage_collection: false
2025/07/26-18:24:05.242263 36f8      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:24:05.242265 36f8 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:24:05.242267 36f8          Options.blob_compaction_readahead_size: 0
2025/07/26-18:24:05.242269 36f8                Options.blob_file_starting_level: 0
2025/07/26-18:24:05.242270 36f8         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:24:05.242272 36f8            Options.memtable_max_range_deletions: 0
2025/07/26-18:24:05.242400 36f8 [db/db_impl/db_impl.cc:3637] Created column family [task_versions] (ID 3)
2025/07/26-18:24:05.245159 36f8 [db/column_family.cc:618] --------------- Options for column family [task_index]:
2025/07/26-18:24:05.245167 36f8               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:24:05.245169 36f8           Options.merge_operator: None
2025/07/26-18:24:05.245171 36f8        Options.compaction_filter: None
2025/07/26-18:24:05.245173 36f8        Options.compaction_filter_factory: None
2025/07/26-18:24:05.245175 36f8  Options.sst_partitioner_factory: None
2025/07/26-18:24:05.245177 36f8         Options.memtable_factory: SkipListFactory
2025/07/26-18:24:05.245178 36f8            Options.table_factory: BlockBasedTable
2025/07/26-18:24:05.245197 36f8            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000027BF5F57820)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000027BF5F7CF70
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:24:05.245202 36f8        Options.write_buffer_size: 67108864
2025/07/26-18:24:05.245205 36f8  Options.max_write_buffer_number: 2
2025/07/26-18:24:05.245206 36f8          Options.compression: Snappy
2025/07/26-18:24:05.245208 36f8                  Options.bottommost_compression: Disabled
2025/07/26-18:24:05.245210 36f8       Options.prefix_extractor: nullptr
2025/07/26-18:24:05.245212 36f8   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:24:05.245213 36f8             Options.num_levels: 7
2025/07/26-18:24:05.245215 36f8        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:24:05.245217 36f8     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:24:05.245218 36f8     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:24:05.245220 36f8            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:24:05.245222 36f8                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:24:05.245223 36f8               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:24:05.245225 36f8         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:24:05.245227 36f8         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:05.245228 36f8         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:24:05.245230 36f8                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:24:05.245232 36f8         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:05.245234 36f8         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:05.245235 36f8            Options.compression_opts.window_bits: -14
2025/07/26-18:24:05.245237 36f8                  Options.compression_opts.level: 32767
2025/07/26-18:24:05.245239 36f8               Options.compression_opts.strategy: 0
2025/07/26-18:24:05.245240 36f8         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:24:05.245242 36f8         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:05.245244 36f8         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:05.245245 36f8         Options.compression_opts.parallel_threads: 1
2025/07/26-18:24:05.245247 36f8                  Options.compression_opts.enabled: false
2025/07/26-18:24:05.245249 36f8         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:05.245250 36f8      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:24:05.245252 36f8          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:24:05.245254 36f8              Options.level0_stop_writes_trigger: 36
2025/07/26-18:24:05.245255 36f8                   Options.target_file_size_base: 67108864
2025/07/26-18:24:05.245257 36f8             Options.target_file_size_multiplier: 1
2025/07/26-18:24:05.245259 36f8                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:24:05.245260 36f8 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:24:05.245262 36f8          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:24:05.245264 36f8 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:24:05.245266 36f8 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:24:05.245267 36f8 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:24:05.245269 36f8 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:24:05.245271 36f8 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:24:05.245273 36f8 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:24:05.245275 36f8 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:24:05.245277 36f8       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:24:05.245278 36f8                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:24:05.245280 36f8   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:24:05.245282 36f8                        Options.arena_block_size: 1048576
2025/07/26-18:24:05.245283 36f8   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:24:05.245285 36f8   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:24:05.245287 36f8                Options.disable_auto_compactions: 0
2025/07/26-18:24:05.245289 36f8                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:24:05.245291 36f8                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:24:05.245293 36f8 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:24:05.245294 36f8 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:24:05.245296 36f8 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:24:05.245298 36f8 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:24:05.245299 36f8 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:24:05.245301 36f8 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:24:05.245303 36f8 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:24:05.245305 36f8 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:24:05.245307 36f8                   Options.table_properties_collectors: 
2025/07/26-18:24:05.245309 36f8                   Options.inplace_update_support: 0
2025/07/26-18:24:05.245310 36f8                 Options.inplace_update_num_locks: 10000
2025/07/26-18:24:05.245312 36f8               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:24:05.245314 36f8               Options.memtable_whole_key_filtering: 0
2025/07/26-18:24:05.245316 36f8   Options.memtable_huge_page_size: 0
2025/07/26-18:24:05.245318 36f8                           Options.bloom_locality: 0
2025/07/26-18:24:05.245320 36f8                    Options.max_successive_merges: 0
2025/07/26-18:24:05.245321 36f8                Options.optimize_filters_for_hits: 0
2025/07/26-18:24:05.245323 36f8                Options.paranoid_file_checks: 0
2025/07/26-18:24:05.245325 36f8                Options.force_consistency_checks: 1
2025/07/26-18:24:05.245326 36f8                Options.report_bg_io_stats: 0
2025/07/26-18:24:05.245328 36f8                               Options.ttl: 2592000
2025/07/26-18:24:05.245330 36f8          Options.periodic_compaction_seconds: 0
2025/07/26-18:24:05.245331 36f8                        Options.default_temperature: kUnknown
2025/07/26-18:24:05.245333 36f8  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:24:05.245335 36f8    Options.preserve_internal_time_seconds: 0
2025/07/26-18:24:05.245336 36f8                       Options.enable_blob_files: false
2025/07/26-18:24:05.245338 36f8                           Options.min_blob_size: 0
2025/07/26-18:24:05.245340 36f8                          Options.blob_file_size: 268435456
2025/07/26-18:24:05.245341 36f8                   Options.blob_compression_type: NoCompression
2025/07/26-18:24:05.245343 36f8          Options.enable_blob_garbage_collection: false
2025/07/26-18:24:05.245345 36f8      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:24:05.245347 36f8 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:24:05.245349 36f8          Options.blob_compaction_readahead_size: 0
2025/07/26-18:24:05.245350 36f8                Options.blob_file_starting_level: 0
2025/07/26-18:24:05.245352 36f8         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:24:05.245354 36f8            Options.memtable_max_range_deletions: 0
2025/07/26-18:24:05.245487 36f8 [db/db_impl/db_impl.cc:3637] Created column family [task_index] (ID 4)
2025/07/26-18:24:05.252275 36f8 [db/db_impl/db_impl_open.cc:2157] SstFileManager instance 0000027BF5FDA280
2025/07/26-18:24:05.252505 36f8 DB pointer 0000027BF5FD8680
2025/07/26-18:24:05.252942 2dc0 [db/db_impl/db_impl.cc:1140] ------- DUMPING STATS -------
2025/07/26-18:24:05.252953 2dc0 [db/db_impl/db_impl.cc:1141] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000027BF5F7D7E0#10676 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 4e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** Compaction Stats [tasks] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [tasks] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000027BF5F7DBA0#10676 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2.2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [tasks] **

** Compaction Stats [raft_state] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [raft_state] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000027BF5F7D240#10676 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [raft_state] **

** Compaction Stats [task_versions] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_versions] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000027BF5F7CD90#10676 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 1.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_versions] **

** Compaction Stats [task_index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000027BF5F7CF70#10676 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 1.8e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_index] **
