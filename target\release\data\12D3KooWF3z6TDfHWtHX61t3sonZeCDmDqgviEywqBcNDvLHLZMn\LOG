2025/07/26-18:24:01.977918 5fcc RocksDB version: 8.10.0
2025/07/26-18:24:01.978538 5fcc Compile date 2023-12-15 13:01:14
2025/07/26-18:24:01.978549 5fcc DB SUMMARY
2025/07/26-18:24:01.978559 5fcc Host name (Env):  DESKTOP-RNBFHG8
2025/07/26-18:24:01.978565 5fcc DB Session ID:  Q6JK7ISKXOMDG7TNZS6L
2025/07/26-18:24:01.978727 5fcc SST files in ./data/12D3KooWF3z6TDfHWtHX61t3sonZeCDmDqgviEywqBcNDvLHLZMn dir, Total Num: 0, files: 
2025/07/26-18:24:01.978734 5fcc Write Ahead Log file in ./data/12D3KooWF3z6TDfHWtHX61t3sonZeCDmDqgviEywqBcNDvLHLZMn: 
2025/07/26-18:24:01.978740 5fcc                         Options.error_if_exists: 0
2025/07/26-18:24:01.978746 5fcc                       Options.create_if_missing: 1
2025/07/26-18:24:01.978750 5fcc                         Options.paranoid_checks: 1
2025/07/26-18:24:01.978882 5fcc             Options.flush_verify_memtable_count: 1
2025/07/26-18:24:01.978890 5fcc          Options.compaction_verify_record_count: 1
2025/07/26-18:24:01.978894 5fcc                               Options.track_and_verify_wals_in_manifest: 0
2025/07/26-18:24:01.978898 5fcc        Options.verify_sst_unique_id_in_manifest: 1
2025/07/26-18:24:01.978901 5fcc                                     Options.env: 000002824F431720
2025/07/26-18:24:01.978904 5fcc                                      Options.fs: WinFS
2025/07/26-18:24:01.978906 5fcc                                Options.info_log: 000002824F4E0F80
2025/07/26-18:24:01.978908 5fcc                Options.max_file_opening_threads: 16
2025/07/26-18:24:01.978910 5fcc                              Options.statistics: 0000000000000000
2025/07/26-18:24:01.978911 5fcc                               Options.use_fsync: 0
2025/07/26-18:24:01.978913 5fcc                       Options.max_log_file_size: 0
2025/07/26-18:24:01.978915 5fcc                  Options.max_manifest_file_size: 1073741824
2025/07/26-18:24:01.978917 5fcc                   Options.log_file_time_to_roll: 0
2025/07/26-18:24:01.978918 5fcc                       Options.keep_log_file_num: 1000
2025/07/26-18:24:01.978920 5fcc                    Options.recycle_log_file_num: 0
2025/07/26-18:24:01.978922 5fcc                         Options.allow_fallocate: 1
2025/07/26-18:24:01.978923 5fcc                        Options.allow_mmap_reads: 0
2025/07/26-18:24:01.978925 5fcc                       Options.allow_mmap_writes: 0
2025/07/26-18:24:01.978927 5fcc                        Options.use_direct_reads: 0
2025/07/26-18:24:01.978928 5fcc                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/26-18:24:01.978930 5fcc          Options.create_missing_column_families: 1
2025/07/26-18:24:01.978932 5fcc                              Options.db_log_dir: 
2025/07/26-18:24:01.978933 5fcc                                 Options.wal_dir: 
2025/07/26-18:24:01.978935 5fcc                Options.table_cache_numshardbits: 6
2025/07/26-18:24:01.978937 5fcc                         Options.WAL_ttl_seconds: 0
2025/07/26-18:24:01.978938 5fcc                       Options.WAL_size_limit_MB: 0
2025/07/26-18:24:01.978940 5fcc                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/26-18:24:01.978942 5fcc             Options.manifest_preallocation_size: 4194304
2025/07/26-18:24:01.978944 5fcc                     Options.is_fd_close_on_exec: 1
2025/07/26-18:24:01.978945 5fcc                   Options.advise_random_on_open: 1
2025/07/26-18:24:01.978947 5fcc                    Options.db_write_buffer_size: 0
2025/07/26-18:24:01.978949 5fcc                    Options.write_buffer_manager: 000002824F431EA0
2025/07/26-18:24:01.978950 5fcc         Options.access_hint_on_compaction_start: 1
2025/07/26-18:24:01.978952 5fcc           Options.random_access_max_buffer_size: 1048576
2025/07/26-18:24:01.978954 5fcc                      Options.use_adaptive_mutex: 0
2025/07/26-18:24:01.978955 5fcc                            Options.rate_limiter: 0000000000000000
2025/07/26-18:24:01.978957 5fcc     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/26-18:24:01.978959 5fcc                       Options.wal_recovery_mode: 2
2025/07/26-18:24:01.978979 5fcc                  Options.enable_thread_tracking: 0
2025/07/26-18:24:01.978982 5fcc                  Options.enable_pipelined_write: 0
2025/07/26-18:24:01.978984 5fcc                  Options.unordered_write: 0
2025/07/26-18:24:01.978986 5fcc         Options.allow_concurrent_memtable_write: 1
2025/07/26-18:24:01.978987 5fcc      Options.enable_write_thread_adaptive_yield: 1
2025/07/26-18:24:01.978989 5fcc             Options.write_thread_max_yield_usec: 100
2025/07/26-18:24:01.978991 5fcc            Options.write_thread_slow_yield_usec: 3
2025/07/26-18:24:01.978992 5fcc                               Options.row_cache: None
2025/07/26-18:24:01.978994 5fcc                              Options.wal_filter: None
2025/07/26-18:24:01.978996 5fcc             Options.avoid_flush_during_recovery: 0
2025/07/26-18:24:01.978998 5fcc             Options.allow_ingest_behind: 0
2025/07/26-18:24:01.978999 5fcc             Options.two_write_queues: 0
2025/07/26-18:24:01.979001 5fcc             Options.manual_wal_flush: 0
2025/07/26-18:24:01.979002 5fcc             Options.wal_compression: 0
2025/07/26-18:24:01.979004 5fcc             Options.atomic_flush: 0
2025/07/26-18:24:01.979006 5fcc             Options.avoid_unnecessary_blocking_io: 0
2025/07/26-18:24:01.979007 5fcc                 Options.persist_stats_to_disk: 0
2025/07/26-18:24:01.979009 5fcc                 Options.write_dbid_to_manifest: 0
2025/07/26-18:24:01.979011 5fcc                 Options.log_readahead_size: 0
2025/07/26-18:24:01.979013 5fcc                 Options.file_checksum_gen_factory: Unknown
2025/07/26-18:24:01.979014 5fcc                 Options.best_efforts_recovery: 0
2025/07/26-18:24:01.979016 5fcc                Options.max_bgerror_resume_count: 2147483647
2025/07/26-18:24:01.979018 5fcc            Options.bgerror_resume_retry_interval: 1000000
2025/07/26-18:24:01.979019 5fcc             Options.allow_data_in_errors: 0
2025/07/26-18:24:01.979021 5fcc             Options.db_host_id: __hostname__
2025/07/26-18:24:01.979023 5fcc             Options.enforce_single_del_contracts: true
2025/07/26-18:24:01.979025 5fcc             Options.max_background_jobs: 4
2025/07/26-18:24:01.979026 5fcc             Options.max_background_compactions: -1
2025/07/26-18:24:01.979028 5fcc             Options.max_subcompactions: 1
2025/07/26-18:24:01.979030 5fcc             Options.avoid_flush_during_shutdown: 0
2025/07/26-18:24:01.979031 5fcc           Options.writable_file_max_buffer_size: 1048576
2025/07/26-18:24:01.979033 5fcc             Options.delayed_write_rate : 16777216
2025/07/26-18:24:01.979035 5fcc             Options.max_total_wal_size: 0
2025/07/26-18:24:01.979036 5fcc             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/26-18:24:01.979038 5fcc                   Options.stats_dump_period_sec: 600
2025/07/26-18:24:01.979040 5fcc                 Options.stats_persist_period_sec: 600
2025/07/26-18:24:01.979042 5fcc                 Options.stats_history_buffer_size: 1048576
2025/07/26-18:24:01.979043 5fcc                          Options.max_open_files: 1000
2025/07/26-18:24:01.979045 5fcc                          Options.bytes_per_sync: 0
2025/07/26-18:24:01.979047 5fcc                      Options.wal_bytes_per_sync: 0
2025/07/26-18:24:01.979048 5fcc                   Options.strict_bytes_per_sync: 0
2025/07/26-18:24:01.979050 5fcc       Options.compaction_readahead_size: 2097152
2025/07/26-18:24:01.979052 5fcc                  Options.max_background_flushes: -1
2025/07/26-18:24:01.979053 5fcc Options.daily_offpeak_time_utc: 
2025/07/26-18:24:01.979055 5fcc Compression algorithms supported:
2025/07/26-18:24:01.979065 5fcc 	kZSTD supported: 1
2025/07/26-18:24:01.979067 5fcc 	kSnappyCompression supported: 1
2025/07/26-18:24:01.979069 5fcc 	kBZip2Compression supported: 1
2025/07/26-18:24:01.979071 5fcc 	kZlibCompression supported: 1
2025/07/26-18:24:01.979072 5fcc 	kLZ4Compression supported: 1
2025/07/26-18:24:01.979074 5fcc 	kXpressCompression supported: 0
2025/07/26-18:24:01.979076 5fcc 	kLZ4HCCompression supported: 1
2025/07/26-18:24:01.979086 5fcc 	kZSTDNotFinalCompression supported: 1
2025/07/26-18:24:01.979095 5fcc Fast CRC32 supported: Not supported on x86
2025/07/26-18:24:01.979097 5fcc DMutex implementation: std::mutex
2025/07/26-18:24:01.985484 5fcc [db/db_impl/db_impl_open.cc:325] Creating manifest 1 
2025/07/26-18:24:01.995359 5fcc [db/version_set.cc:5942] Recovering from manifest file: ./data/12D3KooWF3z6TDfHWtHX61t3sonZeCDmDqgviEywqBcNDvLHLZMn/MANIFEST-000001
2025/07/26-18:24:01.995523 5fcc [db/column_family.cc:618] --------------- Options for column family [default]:
2025/07/26-18:24:01.995531 5fcc               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:24:01.995534 5fcc           Options.merge_operator: None
2025/07/26-18:24:01.995535 5fcc        Options.compaction_filter: None
2025/07/26-18:24:01.995539 5fcc        Options.compaction_filter_factory: None
2025/07/26-18:24:01.995541 5fcc  Options.sst_partitioner_factory: None
2025/07/26-18:24:01.995542 5fcc         Options.memtable_factory: SkipListFactory
2025/07/26-18:24:01.995544 5fcc            Options.table_factory: BlockBasedTable
2025/07/26-18:24:01.995572 5fcc            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000002824F50C5C0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 000002824F431CD0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:24:01.995575 5fcc        Options.write_buffer_size: 67108864
2025/07/26-18:24:01.995577 5fcc  Options.max_write_buffer_number: 2
2025/07/26-18:24:01.995578 5fcc          Options.compression: Snappy
2025/07/26-18:24:01.995580 5fcc                  Options.bottommost_compression: Disabled
2025/07/26-18:24:01.995582 5fcc       Options.prefix_extractor: nullptr
2025/07/26-18:24:01.995583 5fcc   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:24:01.995585 5fcc             Options.num_levels: 7
2025/07/26-18:24:01.995587 5fcc        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:24:01.995588 5fcc     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:24:01.995590 5fcc     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:24:01.995592 5fcc            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:24:01.995594 5fcc                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:24:01.995595 5fcc               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:24:01.995597 5fcc         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:24:01.995599 5fcc         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:01.995601 5fcc         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:24:01.995602 5fcc                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:24:01.995604 5fcc         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:01.995613 5fcc         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:01.995615 5fcc            Options.compression_opts.window_bits: -14
2025/07/26-18:24:01.995619 5fcc                  Options.compression_opts.level: 32767
2025/07/26-18:24:01.995621 5fcc               Options.compression_opts.strategy: 0
2025/07/26-18:24:01.995623 5fcc         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:24:01.995625 5fcc         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:01.995626 5fcc         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:01.995628 5fcc         Options.compression_opts.parallel_threads: 1
2025/07/26-18:24:01.995630 5fcc                  Options.compression_opts.enabled: false
2025/07/26-18:24:01.995631 5fcc         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:01.995633 5fcc      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:24:01.995635 5fcc          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:24:01.995636 5fcc              Options.level0_stop_writes_trigger: 36
2025/07/26-18:24:01.995638 5fcc                   Options.target_file_size_base: 67108864
2025/07/26-18:24:01.995640 5fcc             Options.target_file_size_multiplier: 1
2025/07/26-18:24:01.995641 5fcc                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:24:01.995643 5fcc Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:24:01.995645 5fcc          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:24:01.995647 5fcc Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:24:01.995648 5fcc Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:24:01.995650 5fcc Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:24:01.995652 5fcc Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:24:01.995653 5fcc Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:24:01.995655 5fcc Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:24:01.995657 5fcc Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:24:01.995658 5fcc       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:24:01.995660 5fcc                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:24:01.995662 5fcc   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:24:01.995663 5fcc                        Options.arena_block_size: 1048576
2025/07/26-18:24:01.995665 5fcc   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:24:01.995667 5fcc   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:24:01.995668 5fcc                Options.disable_auto_compactions: 0
2025/07/26-18:24:01.995671 5fcc                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:24:01.995673 5fcc                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:24:01.995675 5fcc Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:24:01.995676 5fcc Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:24:01.995678 5fcc Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:24:01.995680 5fcc Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:24:01.995681 5fcc Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:24:01.995684 5fcc Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:24:01.995685 5fcc Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:24:01.995687 5fcc Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:24:01.995690 5fcc                   Options.table_properties_collectors: 
2025/07/26-18:24:01.995692 5fcc                   Options.inplace_update_support: 0
2025/07/26-18:24:01.995693 5fcc                 Options.inplace_update_num_locks: 10000
2025/07/26-18:24:01.995695 5fcc               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:24:01.995697 5fcc               Options.memtable_whole_key_filtering: 0
2025/07/26-18:24:01.995698 5fcc   Options.memtable_huge_page_size: 0
2025/07/26-18:24:01.995721 5fcc                           Options.bloom_locality: 0
2025/07/26-18:24:01.995723 5fcc                    Options.max_successive_merges: 0
2025/07/26-18:24:01.995725 5fcc                Options.optimize_filters_for_hits: 0
2025/07/26-18:24:01.995727 5fcc                Options.paranoid_file_checks: 0
2025/07/26-18:24:01.995728 5fcc                Options.force_consistency_checks: 1
2025/07/26-18:24:01.995730 5fcc                Options.report_bg_io_stats: 0
2025/07/26-18:24:01.995732 5fcc                               Options.ttl: 2592000
2025/07/26-18:24:01.995733 5fcc          Options.periodic_compaction_seconds: 0
2025/07/26-18:24:01.995735 5fcc                        Options.default_temperature: kUnknown
2025/07/26-18:24:01.995737 5fcc  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:24:01.995739 5fcc    Options.preserve_internal_time_seconds: 0
2025/07/26-18:24:01.995740 5fcc                       Options.enable_blob_files: false
2025/07/26-18:24:01.995742 5fcc                           Options.min_blob_size: 0
2025/07/26-18:24:01.995744 5fcc                          Options.blob_file_size: 268435456
2025/07/26-18:24:01.995745 5fcc                   Options.blob_compression_type: NoCompression
2025/07/26-18:24:01.995747 5fcc          Options.enable_blob_garbage_collection: false
2025/07/26-18:24:01.995749 5fcc      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:24:01.995751 5fcc Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:24:01.995752 5fcc          Options.blob_compaction_readahead_size: 0
2025/07/26-18:24:01.995754 5fcc                Options.blob_file_starting_level: 0
2025/07/26-18:24:01.995756 5fcc         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:24:01.995757 5fcc            Options.memtable_max_range_deletions: 0
2025/07/26-18:24:01.996537 5fcc [db/version_set.cc:5993] Recovered from manifest file:./data/12D3KooWF3z6TDfHWtHX61t3sonZeCDmDqgviEywqBcNDvLHLZMn/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/07/26-18:24:01.996546 5fcc [db/version_set.cc:6002] Column family [default] (ID 0), log number is 0
2025/07/26-18:24:01.996741 5fcc [db/db_impl/db_impl_open.cc:646] DB ID: a5a4b84c-6a0a-11f0-98b6-d4e98a1a402d
2025/07/26-18:24:01.997595 5fcc [db/version_set.cc:5439] Creating manifest 5
2025/07/26-18:24:02.007885 5fcc [db/column_family.cc:618] --------------- Options for column family [tasks]:
2025/07/26-18:24:02.007900 5fcc               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:24:02.007902 5fcc           Options.merge_operator: None
2025/07/26-18:24:02.007904 5fcc        Options.compaction_filter: None
2025/07/26-18:24:02.007906 5fcc        Options.compaction_filter_factory: None
2025/07/26-18:24:02.007907 5fcc  Options.sst_partitioner_factory: None
2025/07/26-18:24:02.007909 5fcc         Options.memtable_factory: SkipListFactory
2025/07/26-18:24:02.007911 5fcc            Options.table_factory: BlockBasedTable
2025/07/26-18:24:02.007941 5fcc            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000002824F4920B0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 000002824F431640
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:24:02.007945 5fcc        Options.write_buffer_size: 67108864
2025/07/26-18:24:02.007949 5fcc  Options.max_write_buffer_number: 2
2025/07/26-18:24:02.007951 5fcc          Options.compression: Snappy
2025/07/26-18:24:02.007953 5fcc                  Options.bottommost_compression: Disabled
2025/07/26-18:24:02.007954 5fcc       Options.prefix_extractor: nullptr
2025/07/26-18:24:02.007956 5fcc   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:24:02.007958 5fcc             Options.num_levels: 7
2025/07/26-18:24:02.007959 5fcc        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:24:02.007961 5fcc     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:24:02.007962 5fcc     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:24:02.007964 5fcc            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:24:02.007966 5fcc                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:24:02.007968 5fcc               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:24:02.007969 5fcc         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:24:02.007971 5fcc         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:02.007973 5fcc         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:24:02.007974 5fcc                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:24:02.007976 5fcc         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:02.007978 5fcc         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:02.007980 5fcc            Options.compression_opts.window_bits: -14
2025/07/26-18:24:02.007981 5fcc                  Options.compression_opts.level: 32767
2025/07/26-18:24:02.007983 5fcc               Options.compression_opts.strategy: 0
2025/07/26-18:24:02.007985 5fcc         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:24:02.007986 5fcc         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:02.007988 5fcc         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:02.007989 5fcc         Options.compression_opts.parallel_threads: 1
2025/07/26-18:24:02.007991 5fcc                  Options.compression_opts.enabled: false
2025/07/26-18:24:02.007993 5fcc         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:02.007994 5fcc      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:24:02.007996 5fcc          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:24:02.007998 5fcc              Options.level0_stop_writes_trigger: 36
2025/07/26-18:24:02.007999 5fcc                   Options.target_file_size_base: 67108864
2025/07/26-18:24:02.008001 5fcc             Options.target_file_size_multiplier: 1
2025/07/26-18:24:02.008003 5fcc                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:24:02.008005 5fcc Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:24:02.008006 5fcc          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:24:02.008008 5fcc Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:24:02.008010 5fcc Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:24:02.008012 5fcc Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:24:02.008014 5fcc Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:24:02.008015 5fcc Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:24:02.008017 5fcc Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:24:02.008019 5fcc Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:24:02.008020 5fcc       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:24:02.008022 5fcc                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:24:02.008025 5fcc   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:24:02.008027 5fcc                        Options.arena_block_size: 1048576
2025/07/26-18:24:02.008028 5fcc   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:24:02.008030 5fcc   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:24:02.008032 5fcc                Options.disable_auto_compactions: 0
2025/07/26-18:24:02.008034 5fcc                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:24:02.008036 5fcc                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:24:02.008038 5fcc Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:24:02.008040 5fcc Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:24:02.008041 5fcc Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:24:02.008043 5fcc Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:24:02.008045 5fcc Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:24:02.008047 5fcc Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:24:02.008049 5fcc Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:24:02.008050 5fcc Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:24:02.008055 5fcc                   Options.table_properties_collectors: 
2025/07/26-18:24:02.008057 5fcc                   Options.inplace_update_support: 0
2025/07/26-18:24:02.008058 5fcc                 Options.inplace_update_num_locks: 10000
2025/07/26-18:24:02.008060 5fcc               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:24:02.008062 5fcc               Options.memtable_whole_key_filtering: 0
2025/07/26-18:24:02.008064 5fcc   Options.memtable_huge_page_size: 0
2025/07/26-18:24:02.008065 5fcc                           Options.bloom_locality: 0
2025/07/26-18:24:02.008067 5fcc                    Options.max_successive_merges: 0
2025/07/26-18:24:02.008069 5fcc                Options.optimize_filters_for_hits: 0
2025/07/26-18:24:02.008070 5fcc                Options.paranoid_file_checks: 0
2025/07/26-18:24:02.008072 5fcc                Options.force_consistency_checks: 1
2025/07/26-18:24:02.008074 5fcc                Options.report_bg_io_stats: 0
2025/07/26-18:24:02.008075 5fcc                               Options.ttl: 2592000
2025/07/26-18:24:02.008077 5fcc          Options.periodic_compaction_seconds: 0
2025/07/26-18:24:02.008079 5fcc                        Options.default_temperature: kUnknown
2025/07/26-18:24:02.008081 5fcc  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:24:02.008082 5fcc    Options.preserve_internal_time_seconds: 0
2025/07/26-18:24:02.008084 5fcc                       Options.enable_blob_files: false
2025/07/26-18:24:02.008086 5fcc                           Options.min_blob_size: 0
2025/07/26-18:24:02.008087 5fcc                          Options.blob_file_size: 268435456
2025/07/26-18:24:02.008089 5fcc                   Options.blob_compression_type: NoCompression
2025/07/26-18:24:02.008091 5fcc          Options.enable_blob_garbage_collection: false
2025/07/26-18:24:02.008093 5fcc      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:24:02.008095 5fcc Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:24:02.008096 5fcc          Options.blob_compaction_readahead_size: 0
2025/07/26-18:24:02.008098 5fcc                Options.blob_file_starting_level: 0
2025/07/26-18:24:02.008100 5fcc         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:24:02.008101 5fcc            Options.memtable_max_range_deletions: 0
2025/07/26-18:24:02.008287 5fcc [db/db_impl/db_impl.cc:3637] Created column family [tasks] (ID 1)
2025/07/26-18:24:02.010834 5fcc [db/column_family.cc:618] --------------- Options for column family [raft_state]:
2025/07/26-18:24:02.010840 5fcc               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:24:02.010844 5fcc           Options.merge_operator: None
2025/07/26-18:24:02.010846 5fcc        Options.compaction_filter: None
2025/07/26-18:24:02.010848 5fcc        Options.compaction_filter_factory: None
2025/07/26-18:24:02.010849 5fcc  Options.sst_partitioner_factory: None
2025/07/26-18:24:02.010851 5fcc         Options.memtable_factory: SkipListFactory
2025/07/26-18:24:02.010853 5fcc            Options.table_factory: BlockBasedTable
2025/07/26-18:24:02.010868 5fcc            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000002824F4926B0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 000002824F431DC0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:24:02.010871 5fcc        Options.write_buffer_size: 67108864
2025/07/26-18:24:02.010872 5fcc  Options.max_write_buffer_number: 2
2025/07/26-18:24:02.010874 5fcc          Options.compression: Snappy
2025/07/26-18:24:02.010876 5fcc                  Options.bottommost_compression: Disabled
2025/07/26-18:24:02.010878 5fcc       Options.prefix_extractor: nullptr
2025/07/26-18:24:02.010879 5fcc   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:24:02.010881 5fcc             Options.num_levels: 7
2025/07/26-18:24:02.010883 5fcc        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:24:02.010884 5fcc     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:24:02.010886 5fcc     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:24:02.010888 5fcc            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:24:02.010889 5fcc                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:24:02.010891 5fcc               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:24:02.010893 5fcc         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:24:02.010894 5fcc         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:02.010896 5fcc         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:24:02.010898 5fcc                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:24:02.010900 5fcc         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:02.010901 5fcc         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:02.010903 5fcc            Options.compression_opts.window_bits: -14
2025/07/26-18:24:02.010905 5fcc                  Options.compression_opts.level: 32767
2025/07/26-18:24:02.010906 5fcc               Options.compression_opts.strategy: 0
2025/07/26-18:24:02.010908 5fcc         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:24:02.010910 5fcc         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:02.010911 5fcc         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:02.010913 5fcc         Options.compression_opts.parallel_threads: 1
2025/07/26-18:24:02.010915 5fcc                  Options.compression_opts.enabled: false
2025/07/26-18:24:02.010946 5fcc         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:02.010948 5fcc      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:24:02.010950 5fcc          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:24:02.010952 5fcc              Options.level0_stop_writes_trigger: 36
2025/07/26-18:24:02.010953 5fcc                   Options.target_file_size_base: 67108864
2025/07/26-18:24:02.010955 5fcc             Options.target_file_size_multiplier: 1
2025/07/26-18:24:02.010957 5fcc                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:24:02.010959 5fcc Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:24:02.010961 5fcc          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:24:02.010963 5fcc Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:24:02.010964 5fcc Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:24:02.010966 5fcc Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:24:02.010968 5fcc Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:24:02.010969 5fcc Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:24:02.010971 5fcc Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:24:02.010973 5fcc Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:24:02.010974 5fcc       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:24:02.010976 5fcc                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:24:02.010978 5fcc   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:24:02.010979 5fcc                        Options.arena_block_size: 1048576
2025/07/26-18:24:02.010981 5fcc   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:24:02.010983 5fcc   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:24:02.010985 5fcc                Options.disable_auto_compactions: 0
2025/07/26-18:24:02.010987 5fcc                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:24:02.010989 5fcc                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:24:02.010990 5fcc Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:24:02.010992 5fcc Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:24:02.010994 5fcc Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:24:02.010996 5fcc Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:24:02.010998 5fcc Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:24:02.011000 5fcc Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:24:02.011001 5fcc Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:24:02.011003 5fcc Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:24:02.011006 5fcc                   Options.table_properties_collectors: 
2025/07/26-18:24:02.011008 5fcc                   Options.inplace_update_support: 0
2025/07/26-18:24:02.011009 5fcc                 Options.inplace_update_num_locks: 10000
2025/07/26-18:24:02.011011 5fcc               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:24:02.011013 5fcc               Options.memtable_whole_key_filtering: 0
2025/07/26-18:24:02.011015 5fcc   Options.memtable_huge_page_size: 0
2025/07/26-18:24:02.011016 5fcc                           Options.bloom_locality: 0
2025/07/26-18:24:02.011018 5fcc                    Options.max_successive_merges: 0
2025/07/26-18:24:02.011020 5fcc                Options.optimize_filters_for_hits: 0
2025/07/26-18:24:02.011021 5fcc                Options.paranoid_file_checks: 0
2025/07/26-18:24:02.011024 5fcc                Options.force_consistency_checks: 1
2025/07/26-18:24:02.011025 5fcc                Options.report_bg_io_stats: 0
2025/07/26-18:24:02.011027 5fcc                               Options.ttl: 2592000
2025/07/26-18:24:02.011029 5fcc          Options.periodic_compaction_seconds: 0
2025/07/26-18:24:02.011032 5fcc                        Options.default_temperature: kUnknown
2025/07/26-18:24:02.011034 5fcc  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:24:02.011035 5fcc    Options.preserve_internal_time_seconds: 0
2025/07/26-18:24:02.011037 5fcc                       Options.enable_blob_files: false
2025/07/26-18:24:02.011039 5fcc                           Options.min_blob_size: 0
2025/07/26-18:24:02.011040 5fcc                          Options.blob_file_size: 268435456
2025/07/26-18:24:02.011042 5fcc                   Options.blob_compression_type: NoCompression
2025/07/26-18:24:02.011044 5fcc          Options.enable_blob_garbage_collection: false
2025/07/26-18:24:02.011045 5fcc      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:24:02.011047 5fcc Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:24:02.011049 5fcc          Options.blob_compaction_readahead_size: 0
2025/07/26-18:24:02.011051 5fcc                Options.blob_file_starting_level: 0
2025/07/26-18:24:02.011053 5fcc         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:24:02.011055 5fcc            Options.memtable_max_range_deletions: 0
2025/07/26-18:24:02.011183 5fcc [db/db_impl/db_impl.cc:3637] Created column family [raft_state] (ID 2)
2025/07/26-18:24:02.013773 5fcc [db/column_family.cc:618] --------------- Options for column family [task_versions]:
2025/07/26-18:24:02.013777 5fcc               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:24:02.013779 5fcc           Options.merge_operator: None
2025/07/26-18:24:02.013781 5fcc        Options.compaction_filter: None
2025/07/26-18:24:02.013782 5fcc        Options.compaction_filter_factory: None
2025/07/26-18:24:02.013784 5fcc  Options.sst_partitioner_factory: None
2025/07/26-18:24:02.013786 5fcc         Options.memtable_factory: SkipListFactory
2025/07/26-18:24:02.013787 5fcc            Options.table_factory: BlockBasedTable
2025/07/26-18:24:02.013806 5fcc            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000002824F492440)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 000002824F431AF0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:24:02.013808 5fcc        Options.write_buffer_size: 67108864
2025/07/26-18:24:02.013810 5fcc  Options.max_write_buffer_number: 2
2025/07/26-18:24:02.013812 5fcc          Options.compression: Snappy
2025/07/26-18:24:02.013813 5fcc                  Options.bottommost_compression: Disabled
2025/07/26-18:24:02.013815 5fcc       Options.prefix_extractor: nullptr
2025/07/26-18:24:02.013817 5fcc   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:24:02.013818 5fcc             Options.num_levels: 7
2025/07/26-18:24:02.013820 5fcc        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:24:02.013821 5fcc     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:24:02.013823 5fcc     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:24:02.013825 5fcc            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:24:02.013828 5fcc                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:24:02.013830 5fcc               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:24:02.013832 5fcc         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:24:02.013833 5fcc         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:02.013835 5fcc         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:24:02.013837 5fcc                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:24:02.013839 5fcc         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:02.013840 5fcc         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:02.013842 5fcc            Options.compression_opts.window_bits: -14
2025/07/26-18:24:02.013844 5fcc                  Options.compression_opts.level: 32767
2025/07/26-18:24:02.013845 5fcc               Options.compression_opts.strategy: 0
2025/07/26-18:24:02.013847 5fcc         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:24:02.013849 5fcc         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:02.013850 5fcc         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:02.013852 5fcc         Options.compression_opts.parallel_threads: 1
2025/07/26-18:24:02.013853 5fcc                  Options.compression_opts.enabled: false
2025/07/26-18:24:02.013855 5fcc         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:02.013857 5fcc      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:24:02.013858 5fcc          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:24:02.013860 5fcc              Options.level0_stop_writes_trigger: 36
2025/07/26-18:24:02.013862 5fcc                   Options.target_file_size_base: 67108864
2025/07/26-18:24:02.013863 5fcc             Options.target_file_size_multiplier: 1
2025/07/26-18:24:02.013865 5fcc                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:24:02.013867 5fcc Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:24:02.013868 5fcc          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:24:02.013870 5fcc Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:24:02.013872 5fcc Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:24:02.013874 5fcc Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:24:02.013875 5fcc Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:24:02.013877 5fcc Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:24:02.013879 5fcc Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:24:02.013880 5fcc Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:24:02.013882 5fcc       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:24:02.013884 5fcc                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:24:02.013885 5fcc   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:24:02.013887 5fcc                        Options.arena_block_size: 1048576
2025/07/26-18:24:02.013889 5fcc   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:24:02.013890 5fcc   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:24:02.013892 5fcc                Options.disable_auto_compactions: 0
2025/07/26-18:24:02.013894 5fcc                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:24:02.013896 5fcc                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:24:02.013897 5fcc Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:24:02.013899 5fcc Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:24:02.013901 5fcc Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:24:02.013902 5fcc Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:24:02.013904 5fcc Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:24:02.013907 5fcc Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:24:02.013909 5fcc Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:24:02.013910 5fcc Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:24:02.013913 5fcc                   Options.table_properties_collectors: 
2025/07/26-18:24:02.013915 5fcc                   Options.inplace_update_support: 0
2025/07/26-18:24:02.013916 5fcc                 Options.inplace_update_num_locks: 10000
2025/07/26-18:24:02.013918 5fcc               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:24:02.013920 5fcc               Options.memtable_whole_key_filtering: 0
2025/07/26-18:24:02.013921 5fcc   Options.memtable_huge_page_size: 0
2025/07/26-18:24:02.013923 5fcc                           Options.bloom_locality: 0
2025/07/26-18:24:02.013925 5fcc                    Options.max_successive_merges: 0
2025/07/26-18:24:02.013926 5fcc                Options.optimize_filters_for_hits: 0
2025/07/26-18:24:02.013928 5fcc                Options.paranoid_file_checks: 0
2025/07/26-18:24:02.013929 5fcc                Options.force_consistency_checks: 1
2025/07/26-18:24:02.013931 5fcc                Options.report_bg_io_stats: 0
2025/07/26-18:24:02.013933 5fcc                               Options.ttl: 2592000
2025/07/26-18:24:02.013934 5fcc          Options.periodic_compaction_seconds: 0
2025/07/26-18:24:02.013936 5fcc                        Options.default_temperature: kUnknown
2025/07/26-18:24:02.013938 5fcc  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:24:02.013939 5fcc    Options.preserve_internal_time_seconds: 0
2025/07/26-18:24:02.013941 5fcc                       Options.enable_blob_files: false
2025/07/26-18:24:02.013943 5fcc                           Options.min_blob_size: 0
2025/07/26-18:24:02.013944 5fcc                          Options.blob_file_size: 268435456
2025/07/26-18:24:02.013946 5fcc                   Options.blob_compression_type: NoCompression
2025/07/26-18:24:02.013948 5fcc          Options.enable_blob_garbage_collection: false
2025/07/26-18:24:02.013949 5fcc      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:24:02.013951 5fcc Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:24:02.013953 5fcc          Options.blob_compaction_readahead_size: 0
2025/07/26-18:24:02.013955 5fcc                Options.blob_file_starting_level: 0
2025/07/26-18:24:02.013956 5fcc         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:24:02.013958 5fcc            Options.memtable_max_range_deletions: 0
2025/07/26-18:24:02.014077 5fcc [db/db_impl/db_impl.cc:3637] Created column family [task_versions] (ID 3)
2025/07/26-18:24:02.015763 5fcc [db/column_family.cc:618] --------------- Options for column family [task_index]:
2025/07/26-18:24:02.015767 5fcc               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:24:02.015769 5fcc           Options.merge_operator: None
2025/07/26-18:24:02.015770 5fcc        Options.compaction_filter: None
2025/07/26-18:24:02.015772 5fcc        Options.compaction_filter_factory: None
2025/07/26-18:24:02.015773 5fcc  Options.sst_partitioner_factory: None
2025/07/26-18:24:02.015775 5fcc         Options.memtable_factory: SkipListFactory
2025/07/26-18:24:02.015777 5fcc            Options.table_factory: BlockBasedTable
2025/07/26-18:24:02.015794 5fcc            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000002824F452A10)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 000002824F431A00
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:24:02.015798 5fcc        Options.write_buffer_size: 67108864
2025/07/26-18:24:02.015801 5fcc  Options.max_write_buffer_number: 2
2025/07/26-18:24:02.015802 5fcc          Options.compression: Snappy
2025/07/26-18:24:02.015804 5fcc                  Options.bottommost_compression: Disabled
2025/07/26-18:24:02.015806 5fcc       Options.prefix_extractor: nullptr
2025/07/26-18:24:02.015807 5fcc   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:24:02.015809 5fcc             Options.num_levels: 7
2025/07/26-18:24:02.015811 5fcc        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:24:02.015812 5fcc     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:24:02.015814 5fcc     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:24:02.015815 5fcc            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:24:02.015817 5fcc                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:24:02.015819 5fcc               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:24:02.015821 5fcc         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:24:02.015822 5fcc         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:02.015824 5fcc         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:24:02.015826 5fcc                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:24:02.015827 5fcc         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:02.015829 5fcc         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:02.015831 5fcc            Options.compression_opts.window_bits: -14
2025/07/26-18:24:02.015832 5fcc                  Options.compression_opts.level: 32767
2025/07/26-18:24:02.015834 5fcc               Options.compression_opts.strategy: 0
2025/07/26-18:24:02.015836 5fcc         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:24:02.015837 5fcc         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:02.015839 5fcc         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:02.015841 5fcc         Options.compression_opts.parallel_threads: 1
2025/07/26-18:24:02.015842 5fcc                  Options.compression_opts.enabled: false
2025/07/26-18:24:02.015844 5fcc         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:02.015846 5fcc      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:24:02.015847 5fcc          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:24:02.015849 5fcc              Options.level0_stop_writes_trigger: 36
2025/07/26-18:24:02.015850 5fcc                   Options.target_file_size_base: 67108864
2025/07/26-18:24:02.015852 5fcc             Options.target_file_size_multiplier: 1
2025/07/26-18:24:02.015854 5fcc                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:24:02.015856 5fcc Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:24:02.015857 5fcc          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:24:02.015859 5fcc Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:24:02.015861 5fcc Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:24:02.015863 5fcc Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:24:02.015864 5fcc Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:24:02.015867 5fcc Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:24:02.015869 5fcc Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:24:02.015870 5fcc Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:24:02.015872 5fcc       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:24:02.015874 5fcc                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:24:02.015875 5fcc   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:24:02.015877 5fcc                        Options.arena_block_size: 1048576
2025/07/26-18:24:02.015879 5fcc   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:24:02.015880 5fcc   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:24:02.015882 5fcc                Options.disable_auto_compactions: 0
2025/07/26-18:24:02.015884 5fcc                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:24:02.015886 5fcc                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:24:02.015887 5fcc Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:24:02.015889 5fcc Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:24:02.015891 5fcc Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:24:02.015892 5fcc Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:24:02.015894 5fcc Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:24:02.015896 5fcc Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:24:02.015898 5fcc Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:24:02.015899 5fcc Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:24:02.015901 5fcc                   Options.table_properties_collectors: 
2025/07/26-18:24:02.015903 5fcc                   Options.inplace_update_support: 0
2025/07/26-18:24:02.015905 5fcc                 Options.inplace_update_num_locks: 10000
2025/07/26-18:24:02.015907 5fcc               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:24:02.015908 5fcc               Options.memtable_whole_key_filtering: 0
2025/07/26-18:24:02.015910 5fcc   Options.memtable_huge_page_size: 0
2025/07/26-18:24:02.015912 5fcc                           Options.bloom_locality: 0
2025/07/26-18:24:02.015913 5fcc                    Options.max_successive_merges: 0
2025/07/26-18:24:02.015915 5fcc                Options.optimize_filters_for_hits: 0
2025/07/26-18:24:02.015917 5fcc                Options.paranoid_file_checks: 0
2025/07/26-18:24:02.015918 5fcc                Options.force_consistency_checks: 1
2025/07/26-18:24:02.015920 5fcc                Options.report_bg_io_stats: 0
2025/07/26-18:24:02.015921 5fcc                               Options.ttl: 2592000
2025/07/26-18:24:02.015923 5fcc          Options.periodic_compaction_seconds: 0
2025/07/26-18:24:02.015925 5fcc                        Options.default_temperature: kUnknown
2025/07/26-18:24:02.015926 5fcc  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:24:02.015928 5fcc    Options.preserve_internal_time_seconds: 0
2025/07/26-18:24:02.015930 5fcc                       Options.enable_blob_files: false
2025/07/26-18:24:02.015931 5fcc                           Options.min_blob_size: 0
2025/07/26-18:24:02.015933 5fcc                          Options.blob_file_size: 268435456
2025/07/26-18:24:02.015935 5fcc                   Options.blob_compression_type: NoCompression
2025/07/26-18:24:02.015936 5fcc          Options.enable_blob_garbage_collection: false
2025/07/26-18:24:02.015938 5fcc      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:24:02.015940 5fcc Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:24:02.015942 5fcc          Options.blob_compaction_readahead_size: 0
2025/07/26-18:24:02.015943 5fcc                Options.blob_file_starting_level: 0
2025/07/26-18:24:02.015945 5fcc         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:24:02.015947 5fcc            Options.memtable_max_range_deletions: 0
2025/07/26-18:24:02.016059 5fcc [db/db_impl/db_impl.cc:3637] Created column family [task_index] (ID 4)
2025/07/26-18:24:02.022456 5fcc [db/db_impl/db_impl_open.cc:2157] SstFileManager instance 0000028250FEB7B0
2025/07/26-18:24:02.022665 5fcc DB pointer 0000028250FE9100
2025/07/26-18:24:02.023148 44c0 [db/db_impl/db_impl.cc:1140] ------- DUMPING STATS -------
2025/07/26-18:24:02.023158 44c0 [db/db_impl/db_impl.cc:1141] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000002824F431CD0#16712 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 4e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** Compaction Stats [tasks] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [tasks] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000002824F431640#16712 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2.2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [tasks] **

** Compaction Stats [raft_state] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [raft_state] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000002824F431DC0#16712 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 1.8e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [raft_state] **

** Compaction Stats [task_versions] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_versions] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000002824F431AF0#16712 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 1.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_versions] **

** Compaction Stats [task_index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000002824F431A00#16712 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_index] **
