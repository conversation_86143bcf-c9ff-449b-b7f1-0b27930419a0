2025/07/26-18:06:40.084628 6f34 RocksDB version: 8.10.0
2025/07/26-18:06:40.085255 6f34 Compile date 2023-12-15 13:01:14
2025/07/26-18:06:40.085271 6f34 DB SUMMARY
2025/07/26-18:06:40.085278 6f34 Host name (Env):  DESKTOP-RNBFHG8
2025/07/26-18:06:40.085284 6f34 DB Session ID:  5BS3ERO4XEPF99Q1B096
2025/07/26-18:06:40.085433 6f34 SST files in ./data/12D3KooWB24XMPe6aHgkhNobqqxarRYRrG1ndLvHaPDSSk6SipSm dir, Total Num: 0, files: 
2025/07/26-18:06:40.085440 6f34 Write Ahead Log file in ./data/12D3KooWB24XMPe6aHgkhNobqqxarRYRrG1ndLvHaPDSSk6SipSm: 
2025/07/26-18:06:40.085445 6f34                         Options.error_if_exists: 0
2025/07/26-18:06:40.085451 6f34                       Options.create_if_missing: 1
2025/07/26-18:06:40.085456 6f34                         Options.paranoid_checks: 1
2025/07/26-18:06:40.085545 6f34             Options.flush_verify_memtable_count: 1
2025/07/26-18:06:40.085548 6f34          Options.compaction_verify_record_count: 1
2025/07/26-18:06:40.085550 6f34                               Options.track_and_verify_wals_in_manifest: 0
2025/07/26-18:06:40.085552 6f34        Options.verify_sst_unique_id_in_manifest: 1
2025/07/26-18:06:40.085554 6f34                                     Options.env: 000001902C381750
2025/07/26-18:06:40.085556 6f34                                      Options.fs: WinFS
2025/07/26-18:06:40.085558 6f34                                Options.info_log: 000001902C318270
2025/07/26-18:06:40.085559 6f34                Options.max_file_opening_threads: 16
2025/07/26-18:06:40.085561 6f34                              Options.statistics: 0000000000000000
2025/07/26-18:06:40.085563 6f34                               Options.use_fsync: 0
2025/07/26-18:06:40.085565 6f34                       Options.max_log_file_size: 0
2025/07/26-18:06:40.085573 6f34                  Options.max_manifest_file_size: 1073741824
2025/07/26-18:06:40.085575 6f34                   Options.log_file_time_to_roll: 0
2025/07/26-18:06:40.085576 6f34                       Options.keep_log_file_num: 1000
2025/07/26-18:06:40.085578 6f34                    Options.recycle_log_file_num: 0
2025/07/26-18:06:40.085580 6f34                         Options.allow_fallocate: 1
2025/07/26-18:06:40.085582 6f34                        Options.allow_mmap_reads: 0
2025/07/26-18:06:40.085584 6f34                       Options.allow_mmap_writes: 0
2025/07/26-18:06:40.085586 6f34                        Options.use_direct_reads: 0
2025/07/26-18:06:40.085587 6f34                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/26-18:06:40.085589 6f34          Options.create_missing_column_families: 1
2025/07/26-18:06:40.085591 6f34                              Options.db_log_dir: 
2025/07/26-18:06:40.085593 6f34                                 Options.wal_dir: 
2025/07/26-18:06:40.085594 6f34                Options.table_cache_numshardbits: 6
2025/07/26-18:06:40.085596 6f34                         Options.WAL_ttl_seconds: 0
2025/07/26-18:06:40.085598 6f34                       Options.WAL_size_limit_MB: 0
2025/07/26-18:06:40.085600 6f34                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/26-18:06:40.085602 6f34             Options.manifest_preallocation_size: 4194304
2025/07/26-18:06:40.085603 6f34                     Options.is_fd_close_on_exec: 1
2025/07/26-18:06:40.085605 6f34                   Options.advise_random_on_open: 1
2025/07/26-18:06:40.085607 6f34                    Options.db_write_buffer_size: 0
2025/07/26-18:06:40.085609 6f34                    Options.write_buffer_manager: 000001902C381DE0
2025/07/26-18:06:40.085610 6f34         Options.access_hint_on_compaction_start: 1
2025/07/26-18:06:40.085612 6f34           Options.random_access_max_buffer_size: 1048576
2025/07/26-18:06:40.085614 6f34                      Options.use_adaptive_mutex: 0
2025/07/26-18:06:40.085616 6f34                            Options.rate_limiter: 0000000000000000
2025/07/26-18:06:40.085619 6f34     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/26-18:06:40.085620 6f34                       Options.wal_recovery_mode: 2
2025/07/26-18:06:40.085635 6f34                  Options.enable_thread_tracking: 0
2025/07/26-18:06:40.085637 6f34                  Options.enable_pipelined_write: 0
2025/07/26-18:06:40.085639 6f34                  Options.unordered_write: 0
2025/07/26-18:06:40.085641 6f34         Options.allow_concurrent_memtable_write: 1
2025/07/26-18:06:40.085643 6f34      Options.enable_write_thread_adaptive_yield: 1
2025/07/26-18:06:40.085645 6f34             Options.write_thread_max_yield_usec: 100
2025/07/26-18:06:40.085646 6f34            Options.write_thread_slow_yield_usec: 3
2025/07/26-18:06:40.085648 6f34                               Options.row_cache: None
2025/07/26-18:06:40.085650 6f34                              Options.wal_filter: None
2025/07/26-18:06:40.085652 6f34             Options.avoid_flush_during_recovery: 0
2025/07/26-18:06:40.085654 6f34             Options.allow_ingest_behind: 0
2025/07/26-18:06:40.085656 6f34             Options.two_write_queues: 0
2025/07/26-18:06:40.085657 6f34             Options.manual_wal_flush: 0
2025/07/26-18:06:40.085659 6f34             Options.wal_compression: 0
2025/07/26-18:06:40.085661 6f34             Options.atomic_flush: 0
2025/07/26-18:06:40.085662 6f34             Options.avoid_unnecessary_blocking_io: 0
2025/07/26-18:06:40.085664 6f34                 Options.persist_stats_to_disk: 0
2025/07/26-18:06:40.085666 6f34                 Options.write_dbid_to_manifest: 0
2025/07/26-18:06:40.085668 6f34                 Options.log_readahead_size: 0
2025/07/26-18:06:40.085669 6f34                 Options.file_checksum_gen_factory: Unknown
2025/07/26-18:06:40.085671 6f34                 Options.best_efforts_recovery: 0
2025/07/26-18:06:40.085673 6f34                Options.max_bgerror_resume_count: 2147483647
2025/07/26-18:06:40.085675 6f34            Options.bgerror_resume_retry_interval: 1000000
2025/07/26-18:06:40.085677 6f34             Options.allow_data_in_errors: 0
2025/07/26-18:06:40.085678 6f34             Options.db_host_id: __hostname__
2025/07/26-18:06:40.085680 6f34             Options.enforce_single_del_contracts: true
2025/07/26-18:06:40.085682 6f34             Options.max_background_jobs: 4
2025/07/26-18:06:40.085684 6f34             Options.max_background_compactions: -1
2025/07/26-18:06:40.085685 6f34             Options.max_subcompactions: 1
2025/07/26-18:06:40.085687 6f34             Options.avoid_flush_during_shutdown: 0
2025/07/26-18:06:40.085689 6f34           Options.writable_file_max_buffer_size: 1048576
2025/07/26-18:06:40.085691 6f34             Options.delayed_write_rate : 16777216
2025/07/26-18:06:40.085693 6f34             Options.max_total_wal_size: 0
2025/07/26-18:06:40.085695 6f34             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/26-18:06:40.085697 6f34                   Options.stats_dump_period_sec: 600
2025/07/26-18:06:40.085698 6f34                 Options.stats_persist_period_sec: 600
2025/07/26-18:06:40.085700 6f34                 Options.stats_history_buffer_size: 1048576
2025/07/26-18:06:40.085702 6f34                          Options.max_open_files: 1000
2025/07/26-18:06:40.085704 6f34                          Options.bytes_per_sync: 0
2025/07/26-18:06:40.085705 6f34                      Options.wal_bytes_per_sync: 0
2025/07/26-18:06:40.085707 6f34                   Options.strict_bytes_per_sync: 0
2025/07/26-18:06:40.085709 6f34       Options.compaction_readahead_size: 2097152
2025/07/26-18:06:40.085711 6f34                  Options.max_background_flushes: -1
2025/07/26-18:06:40.085712 6f34 Options.daily_offpeak_time_utc: 
2025/07/26-18:06:40.085714 6f34 Compression algorithms supported:
2025/07/26-18:06:40.085723 6f34 	kZSTD supported: 1
2025/07/26-18:06:40.085725 6f34 	kSnappyCompression supported: 1
2025/07/26-18:06:40.085727 6f34 	kBZip2Compression supported: 1
2025/07/26-18:06:40.085729 6f34 	kZlibCompression supported: 1
2025/07/26-18:06:40.085730 6f34 	kLZ4Compression supported: 1
2025/07/26-18:06:40.085732 6f34 	kXpressCompression supported: 0
2025/07/26-18:06:40.085734 6f34 	kLZ4HCCompression supported: 1
2025/07/26-18:06:40.085743 6f34 	kZSTDNotFinalCompression supported: 1
2025/07/26-18:06:40.085752 6f34 Fast CRC32 supported: Not supported on x86
2025/07/26-18:06:40.085754 6f34 DMutex implementation: std::mutex
2025/07/26-18:06:40.091699 6f34 [db/db_impl/db_impl_open.cc:325] Creating manifest 1 
2025/07/26-18:06:40.104173 6f34 [db/version_set.cc:5942] Recovering from manifest file: ./data/12D3KooWB24XMPe6aHgkhNobqqxarRYRrG1ndLvHaPDSSk6SipSm/MANIFEST-000001
2025/07/26-18:06:40.104520 6f34 [db/column_family.cc:618] --------------- Options for column family [default]:
2025/07/26-18:06:40.104530 6f34               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:06:40.104535 6f34           Options.merge_operator: None
2025/07/26-18:06:40.104537 6f34        Options.compaction_filter: None
2025/07/26-18:06:40.104538 6f34        Options.compaction_filter_factory: None
2025/07/26-18:06:40.104540 6f34  Options.sst_partitioner_factory: None
2025/07/26-18:06:40.104542 6f34         Options.memtable_factory: SkipListFactory
2025/07/26-18:06:40.104544 6f34            Options.table_factory: BlockBasedTable
2025/07/26-18:06:40.104574 6f34            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001902C353070)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 000001902C381EE0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:06:40.104576 6f34        Options.write_buffer_size: 67108864
2025/07/26-18:06:40.104578 6f34  Options.max_write_buffer_number: 2
2025/07/26-18:06:40.104580 6f34          Options.compression: Snappy
2025/07/26-18:06:40.104617 6f34                  Options.bottommost_compression: Disabled
2025/07/26-18:06:40.104619 6f34       Options.prefix_extractor: nullptr
2025/07/26-18:06:40.104621 6f34   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:06:40.104623 6f34             Options.num_levels: 7
2025/07/26-18:06:40.104651 6f34        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:06:40.104653 6f34     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:06:40.104655 6f34     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:06:40.104657 6f34            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:06:40.104658 6f34                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:06:40.104660 6f34               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:06:40.104662 6f34         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:06:40.104664 6f34         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:06:40.104666 6f34         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:06:40.104668 6f34                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:06:40.104670 6f34         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:06:40.104671 6f34         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:06:40.104673 6f34            Options.compression_opts.window_bits: -14
2025/07/26-18:06:40.104677 6f34                  Options.compression_opts.level: 32767
2025/07/26-18:06:40.104679 6f34               Options.compression_opts.strategy: 0
2025/07/26-18:06:40.104681 6f34         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:06:40.104683 6f34         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:06:40.104685 6f34         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:06:40.104686 6f34         Options.compression_opts.parallel_threads: 1
2025/07/26-18:06:40.104688 6f34                  Options.compression_opts.enabled: false
2025/07/26-18:06:40.104690 6f34         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:06:40.104692 6f34      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:06:40.104693 6f34          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:06:40.104695 6f34              Options.level0_stop_writes_trigger: 36
2025/07/26-18:06:40.104697 6f34                   Options.target_file_size_base: 67108864
2025/07/26-18:06:40.104699 6f34             Options.target_file_size_multiplier: 1
2025/07/26-18:06:40.104700 6f34                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:06:40.104702 6f34 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:06:40.104704 6f34          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:06:40.104706 6f34 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:06:40.104708 6f34 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:06:40.104714 6f34 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:06:40.104716 6f34 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:06:40.104718 6f34 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:06:40.104719 6f34 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:06:40.104729 6f34 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:06:40.104731 6f34       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:06:40.104732 6f34                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:06:40.104734 6f34   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:06:40.104736 6f34                        Options.arena_block_size: 1048576
2025/07/26-18:06:40.104738 6f34   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:06:40.104740 6f34   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:06:40.104741 6f34                Options.disable_auto_compactions: 0
2025/07/26-18:06:40.104768 6f34                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:06:40.104771 6f34                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:06:40.104772 6f34 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:06:40.104774 6f34 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:06:40.104776 6f34 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:06:40.104778 6f34 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:06:40.104779 6f34 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:06:40.104782 6f34 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:06:40.104784 6f34 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:06:40.105041 6f34 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:06:40.105046 6f34                   Options.table_properties_collectors: 
2025/07/26-18:06:40.105048 6f34                   Options.inplace_update_support: 0
2025/07/26-18:06:40.105049 6f34                 Options.inplace_update_num_locks: 10000
2025/07/26-18:06:40.105051 6f34               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:06:40.105053 6f34               Options.memtable_whole_key_filtering: 0
2025/07/26-18:06:40.105055 6f34   Options.memtable_huge_page_size: 0
2025/07/26-18:06:40.105083 6f34                           Options.bloom_locality: 0
2025/07/26-18:06:40.105086 6f34                    Options.max_successive_merges: 0
2025/07/26-18:06:40.105088 6f34                Options.optimize_filters_for_hits: 0
2025/07/26-18:06:40.105089 6f34                Options.paranoid_file_checks: 0
2025/07/26-18:06:40.105091 6f34                Options.force_consistency_checks: 1
2025/07/26-18:06:40.105093 6f34                Options.report_bg_io_stats: 0
2025/07/26-18:06:40.105095 6f34                               Options.ttl: 2592000
2025/07/26-18:06:40.105096 6f34          Options.periodic_compaction_seconds: 0
2025/07/26-18:06:40.105101 6f34                        Options.default_temperature: kUnknown
2025/07/26-18:06:40.105103 6f34  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:06:40.105105 6f34    Options.preserve_internal_time_seconds: 0
2025/07/26-18:06:40.105107 6f34                       Options.enable_blob_files: false
2025/07/26-18:06:40.105108 6f34                           Options.min_blob_size: 0
2025/07/26-18:06:40.105110 6f34                          Options.blob_file_size: 268435456
2025/07/26-18:06:40.105112 6f34                   Options.blob_compression_type: NoCompression
2025/07/26-18:06:40.105114 6f34          Options.enable_blob_garbage_collection: false
2025/07/26-18:06:40.105116 6f34      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:06:40.105118 6f34 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:06:40.105120 6f34          Options.blob_compaction_readahead_size: 0
2025/07/26-18:06:40.105121 6f34                Options.blob_file_starting_level: 0
2025/07/26-18:06:40.105123 6f34         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:06:40.105125 6f34            Options.memtable_max_range_deletions: 0
2025/07/26-18:06:40.106123 6f34 [db/version_set.cc:5993] Recovered from manifest file:./data/12D3KooWB24XMPe6aHgkhNobqqxarRYRrG1ndLvHaPDSSk6SipSm/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/07/26-18:06:40.106131 6f34 [db/version_set.cc:6002] Column family [default] (ID 0), log number is 0
2025/07/26-18:06:40.106322 6f34 [db/db_impl/db_impl_open.cc:646] DB ID: 38a00c6c-6a08-11f0-98b6-d4e98a1a402d
2025/07/26-18:06:40.107351 6f34 [db/version_set.cc:5439] Creating manifest 5
2025/07/26-18:06:40.118076 6f34 [db/column_family.cc:618] --------------- Options for column family [tasks]:
2025/07/26-18:06:40.118087 6f34               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:06:40.118089 6f34           Options.merge_operator: None
2025/07/26-18:06:40.118091 6f34        Options.compaction_filter: None
2025/07/26-18:06:40.118092 6f34        Options.compaction_filter_factory: None
2025/07/26-18:06:40.118094 6f34  Options.sst_partitioner_factory: None
2025/07/26-18:06:40.118096 6f34         Options.memtable_factory: SkipListFactory
2025/07/26-18:06:40.118098 6f34            Options.table_factory: BlockBasedTable
2025/07/26-18:06:40.118118 6f34            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001902C359D60)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 000001902C381940
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:06:40.118122 6f34        Options.write_buffer_size: 67108864
2025/07/26-18:06:40.118126 6f34  Options.max_write_buffer_number: 2
2025/07/26-18:06:40.118127 6f34          Options.compression: Snappy
2025/07/26-18:06:40.118129 6f34                  Options.bottommost_compression: Disabled
2025/07/26-18:06:40.118131 6f34       Options.prefix_extractor: nullptr
2025/07/26-18:06:40.118133 6f34   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:06:40.118134 6f34             Options.num_levels: 7
2025/07/26-18:06:40.118136 6f34        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:06:40.118138 6f34     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:06:40.118139 6f34     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:06:40.118141 6f34            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:06:40.118143 6f34                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:06:40.118145 6f34               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:06:40.118147 6f34         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:06:40.118148 6f34         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:06:40.118150 6f34         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:06:40.118152 6f34                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:06:40.118154 6f34         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:06:40.118156 6f34         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:06:40.118158 6f34            Options.compression_opts.window_bits: -14
2025/07/26-18:06:40.118159 6f34                  Options.compression_opts.level: 32767
2025/07/26-18:06:40.118161 6f34               Options.compression_opts.strategy: 0
2025/07/26-18:06:40.118163 6f34         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:06:40.118164 6f34         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:06:40.118166 6f34         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:06:40.118168 6f34         Options.compression_opts.parallel_threads: 1
2025/07/26-18:06:40.118170 6f34                  Options.compression_opts.enabled: false
2025/07/26-18:06:40.118171 6f34         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:06:40.118173 6f34      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:06:40.118175 6f34          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:06:40.118177 6f34              Options.level0_stop_writes_trigger: 36
2025/07/26-18:06:40.118178 6f34                   Options.target_file_size_base: 67108864
2025/07/26-18:06:40.118180 6f34             Options.target_file_size_multiplier: 1
2025/07/26-18:06:40.118182 6f34                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:06:40.118184 6f34 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:06:40.118185 6f34          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:06:40.118191 6f34 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:06:40.118193 6f34 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:06:40.118195 6f34 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:06:40.118197 6f34 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:06:40.118198 6f34 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:06:40.118200 6f34 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:06:40.118202 6f34 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:06:40.118204 6f34       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:06:40.118205 6f34                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:06:40.118208 6f34   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:06:40.118210 6f34                        Options.arena_block_size: 1048576
2025/07/26-18:06:40.118212 6f34   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:06:40.118214 6f34   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:06:40.118216 6f34                Options.disable_auto_compactions: 0
2025/07/26-18:06:40.118218 6f34                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:06:40.118220 6f34                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:06:40.118222 6f34 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:06:40.118224 6f34 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:06:40.118225 6f34 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:06:40.118227 6f34 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:06:40.118229 6f34 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:06:40.118232 6f34 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:06:40.118234 6f34 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:06:40.118235 6f34 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:06:40.118240 6f34                   Options.table_properties_collectors: 
2025/07/26-18:06:40.118242 6f34                   Options.inplace_update_support: 0
2025/07/26-18:06:40.118244 6f34                 Options.inplace_update_num_locks: 10000
2025/07/26-18:06:40.118246 6f34               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:06:40.118248 6f34               Options.memtable_whole_key_filtering: 0
2025/07/26-18:06:40.118250 6f34   Options.memtable_huge_page_size: 0
2025/07/26-18:06:40.118252 6f34                           Options.bloom_locality: 0
2025/07/26-18:06:40.118253 6f34                    Options.max_successive_merges: 0
2025/07/26-18:06:40.118255 6f34                Options.optimize_filters_for_hits: 0
2025/07/26-18:06:40.118257 6f34                Options.paranoid_file_checks: 0
2025/07/26-18:06:40.118259 6f34                Options.force_consistency_checks: 1
2025/07/26-18:06:40.118260 6f34                Options.report_bg_io_stats: 0
2025/07/26-18:06:40.118262 6f34                               Options.ttl: 2592000
2025/07/26-18:06:40.118264 6f34          Options.periodic_compaction_seconds: 0
2025/07/26-18:06:40.118266 6f34                        Options.default_temperature: kUnknown
2025/07/26-18:06:40.118267 6f34  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:06:40.118269 6f34    Options.preserve_internal_time_seconds: 0
2025/07/26-18:06:40.118271 6f34                       Options.enable_blob_files: false
2025/07/26-18:06:40.118273 6f34                           Options.min_blob_size: 0
2025/07/26-18:06:40.118274 6f34                          Options.blob_file_size: 268435456
2025/07/26-18:06:40.118276 6f34                   Options.blob_compression_type: NoCompression
2025/07/26-18:06:40.118278 6f34          Options.enable_blob_garbage_collection: false
2025/07/26-18:06:40.118280 6f34      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:06:40.118282 6f34 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:06:40.118284 6f34          Options.blob_compaction_readahead_size: 0
2025/07/26-18:06:40.118285 6f34                Options.blob_file_starting_level: 0
2025/07/26-18:06:40.118287 6f34         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:06:40.118289 6f34            Options.memtable_max_range_deletions: 0
2025/07/26-18:06:40.118440 6f34 [db/db_impl/db_impl.cc:3637] Created column family [tasks] (ID 1)
2025/07/26-18:06:40.120061 6f34 [db/column_family.cc:618] --------------- Options for column family [raft_state]:
2025/07/26-18:06:40.120066 6f34               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:06:40.120070 6f34           Options.merge_operator: None
2025/07/26-18:06:40.120072 6f34        Options.compaction_filter: None
2025/07/26-18:06:40.120074 6f34        Options.compaction_filter_factory: None
2025/07/26-18:06:40.120076 6f34  Options.sst_partitioner_factory: None
2025/07/26-18:06:40.120077 6f34         Options.memtable_factory: SkipListFactory
2025/07/26-18:06:40.120079 6f34            Options.table_factory: BlockBasedTable
2025/07/26-18:06:40.120099 6f34            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001902C3581A0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 000001902C382480
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:06:40.120102 6f34        Options.write_buffer_size: 67108864
2025/07/26-18:06:40.120103 6f34  Options.max_write_buffer_number: 2
2025/07/26-18:06:40.120105 6f34          Options.compression: Snappy
2025/07/26-18:06:40.120107 6f34                  Options.bottommost_compression: Disabled
2025/07/26-18:06:40.120109 6f34       Options.prefix_extractor: nullptr
2025/07/26-18:06:40.120111 6f34   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:06:40.120112 6f34             Options.num_levels: 7
2025/07/26-18:06:40.120114 6f34        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:06:40.120116 6f34     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:06:40.120117 6f34     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:06:40.120119 6f34            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:06:40.120121 6f34                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:06:40.120123 6f34               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:06:40.120125 6f34         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:06:40.120126 6f34         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:06:40.120128 6f34         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:06:40.120130 6f34                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:06:40.120132 6f34         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:06:40.120134 6f34         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:06:40.120135 6f34            Options.compression_opts.window_bits: -14
2025/07/26-18:06:40.120137 6f34                  Options.compression_opts.level: 32767
2025/07/26-18:06:40.120139 6f34               Options.compression_opts.strategy: 0
2025/07/26-18:06:40.120140 6f34         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:06:40.120142 6f34         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:06:40.120144 6f34         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:06:40.120146 6f34         Options.compression_opts.parallel_threads: 1
2025/07/26-18:06:40.120147 6f34                  Options.compression_opts.enabled: false
2025/07/26-18:06:40.120175 6f34         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:06:40.120177 6f34      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:06:40.120179 6f34          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:06:40.120180 6f34              Options.level0_stop_writes_trigger: 36
2025/07/26-18:06:40.120182 6f34                   Options.target_file_size_base: 67108864
2025/07/26-18:06:40.120184 6f34             Options.target_file_size_multiplier: 1
2025/07/26-18:06:40.120186 6f34                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:06:40.120187 6f34 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:06:40.120189 6f34          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:06:40.120191 6f34 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:06:40.120193 6f34 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:06:40.120195 6f34 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:06:40.120197 6f34 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:06:40.120198 6f34 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:06:40.120200 6f34 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:06:40.120202 6f34 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:06:40.120204 6f34       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:06:40.120205 6f34                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:06:40.120207 6f34   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:06:40.120209 6f34                        Options.arena_block_size: 1048576
2025/07/26-18:06:40.120211 6f34   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:06:40.120213 6f34   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:06:40.120214 6f34                Options.disable_auto_compactions: 0
2025/07/26-18:06:40.120216 6f34                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:06:40.120218 6f34                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:06:40.120220 6f34 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:06:40.120222 6f34 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:06:40.120224 6f34 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:06:40.120226 6f34 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:06:40.120227 6f34 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:06:40.120229 6f34 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:06:40.120231 6f34 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:06:40.120233 6f34 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:06:40.120235 6f34                   Options.table_properties_collectors: 
2025/07/26-18:06:40.120237 6f34                   Options.inplace_update_support: 0
2025/07/26-18:06:40.120239 6f34                 Options.inplace_update_num_locks: 10000
2025/07/26-18:06:40.120241 6f34               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:06:40.120243 6f34               Options.memtable_whole_key_filtering: 0
2025/07/26-18:06:40.120244 6f34   Options.memtable_huge_page_size: 0
2025/07/26-18:06:40.120246 6f34                           Options.bloom_locality: 0
2025/07/26-18:06:40.120248 6f34                    Options.max_successive_merges: 0
2025/07/26-18:06:40.120250 6f34                Options.optimize_filters_for_hits: 0
2025/07/26-18:06:40.120251 6f34                Options.paranoid_file_checks: 0
2025/07/26-18:06:40.120253 6f34                Options.force_consistency_checks: 1
2025/07/26-18:06:40.120255 6f34                Options.report_bg_io_stats: 0
2025/07/26-18:06:40.120257 6f34                               Options.ttl: 2592000
2025/07/26-18:06:40.120258 6f34          Options.periodic_compaction_seconds: 0
2025/07/26-18:06:40.120261 6f34                        Options.default_temperature: kUnknown
2025/07/26-18:06:40.120263 6f34  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:06:40.120265 6f34    Options.preserve_internal_time_seconds: 0
2025/07/26-18:06:40.120267 6f34                       Options.enable_blob_files: false
2025/07/26-18:06:40.120269 6f34                           Options.min_blob_size: 0
2025/07/26-18:06:40.120271 6f34                          Options.blob_file_size: 268435456
2025/07/26-18:06:40.120272 6f34                   Options.blob_compression_type: NoCompression
2025/07/26-18:06:40.120274 6f34          Options.enable_blob_garbage_collection: false
2025/07/26-18:06:40.120276 6f34      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:06:40.120278 6f34 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:06:40.120280 6f34          Options.blob_compaction_readahead_size: 0
2025/07/26-18:06:40.120281 6f34                Options.blob_file_starting_level: 0
2025/07/26-18:06:40.120283 6f34         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:06:40.120285 6f34            Options.memtable_max_range_deletions: 0
2025/07/26-18:06:40.120396 6f34 [db/db_impl/db_impl.cc:3637] Created column family [raft_state] (ID 2)
2025/07/26-18:06:40.122178 6f34 [db/column_family.cc:618] --------------- Options for column family [task_versions]:
2025/07/26-18:06:40.122183 6f34               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:06:40.122186 6f34           Options.merge_operator: None
2025/07/26-18:06:40.122188 6f34        Options.compaction_filter: None
2025/07/26-18:06:40.122189 6f34        Options.compaction_filter_factory: None
2025/07/26-18:06:40.122191 6f34  Options.sst_partitioner_factory: None
2025/07/26-18:06:40.122193 6f34         Options.memtable_factory: SkipListFactory
2025/07/26-18:06:40.122194 6f34            Options.table_factory: BlockBasedTable
2025/07/26-18:06:40.122210 6f34            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001902C358C20)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 000001902C381670
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:06:40.122212 6f34        Options.write_buffer_size: 67108864
2025/07/26-18:06:40.122214 6f34  Options.max_write_buffer_number: 2
2025/07/26-18:06:40.122216 6f34          Options.compression: Snappy
2025/07/26-18:06:40.122218 6f34                  Options.bottommost_compression: Disabled
2025/07/26-18:06:40.122219 6f34       Options.prefix_extractor: nullptr
2025/07/26-18:06:40.122221 6f34   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:06:40.122223 6f34             Options.num_levels: 7
2025/07/26-18:06:40.122225 6f34        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:06:40.122226 6f34     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:06:40.122228 6f34     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:06:40.122230 6f34            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:06:40.122232 6f34                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:06:40.122235 6f34               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:06:40.122236 6f34         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:06:40.122238 6f34         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:06:40.122240 6f34         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:06:40.122242 6f34                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:06:40.122244 6f34         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:06:40.122245 6f34         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:06:40.122247 6f34            Options.compression_opts.window_bits: -14
2025/07/26-18:06:40.122249 6f34                  Options.compression_opts.level: 32767
2025/07/26-18:06:40.122250 6f34               Options.compression_opts.strategy: 0
2025/07/26-18:06:40.122252 6f34         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:06:40.122254 6f34         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:06:40.122256 6f34         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:06:40.122257 6f34         Options.compression_opts.parallel_threads: 1
2025/07/26-18:06:40.122259 6f34                  Options.compression_opts.enabled: false
2025/07/26-18:06:40.122261 6f34         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:06:40.122263 6f34      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:06:40.122264 6f34          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:06:40.122267 6f34              Options.level0_stop_writes_trigger: 36
2025/07/26-18:06:40.122268 6f34                   Options.target_file_size_base: 67108864
2025/07/26-18:06:40.122270 6f34             Options.target_file_size_multiplier: 1
2025/07/26-18:06:40.122272 6f34                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:06:40.122274 6f34 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:06:40.122275 6f34          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:06:40.122277 6f34 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:06:40.122279 6f34 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:06:40.122281 6f34 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:06:40.122283 6f34 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:06:40.122285 6f34 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:06:40.122286 6f34 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:06:40.122288 6f34 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:06:40.122290 6f34       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:06:40.122291 6f34                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:06:40.122293 6f34   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:06:40.122295 6f34                        Options.arena_block_size: 1048576
2025/07/26-18:06:40.122297 6f34   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:06:40.122298 6f34   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:06:40.122300 6f34                Options.disable_auto_compactions: 0
2025/07/26-18:06:40.122302 6f34                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:06:40.122304 6f34                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:06:40.122306 6f34 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:06:40.122308 6f34 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:06:40.122309 6f34 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:06:40.122311 6f34 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:06:40.122313 6f34 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:06:40.122316 6f34 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:06:40.122318 6f34 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:06:40.122320 6f34 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:06:40.122323 6f34                   Options.table_properties_collectors: 
2025/07/26-18:06:40.122324 6f34                   Options.inplace_update_support: 0
2025/07/26-18:06:40.122326 6f34                 Options.inplace_update_num_locks: 10000
2025/07/26-18:06:40.122328 6f34               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:06:40.122330 6f34               Options.memtable_whole_key_filtering: 0
2025/07/26-18:06:40.122331 6f34   Options.memtable_huge_page_size: 0
2025/07/26-18:06:40.122333 6f34                           Options.bloom_locality: 0
2025/07/26-18:06:40.122335 6f34                    Options.max_successive_merges: 0
2025/07/26-18:06:40.122337 6f34                Options.optimize_filters_for_hits: 0
2025/07/26-18:06:40.122338 6f34                Options.paranoid_file_checks: 0
2025/07/26-18:06:40.122340 6f34                Options.force_consistency_checks: 1
2025/07/26-18:06:40.122342 6f34                Options.report_bg_io_stats: 0
2025/07/26-18:06:40.122344 6f34                               Options.ttl: 2592000
2025/07/26-18:06:40.122345 6f34          Options.periodic_compaction_seconds: 0
2025/07/26-18:06:40.122347 6f34                        Options.default_temperature: kUnknown
2025/07/26-18:06:40.122349 6f34  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:06:40.122351 6f34    Options.preserve_internal_time_seconds: 0
2025/07/26-18:06:40.122352 6f34                       Options.enable_blob_files: false
2025/07/26-18:06:40.122354 6f34                           Options.min_blob_size: 0
2025/07/26-18:06:40.122356 6f34                          Options.blob_file_size: 268435456
2025/07/26-18:06:40.122358 6f34                   Options.blob_compression_type: NoCompression
2025/07/26-18:06:40.122359 6f34          Options.enable_blob_garbage_collection: false
2025/07/26-18:06:40.122361 6f34      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:06:40.122363 6f34 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:06:40.122365 6f34          Options.blob_compaction_readahead_size: 0
2025/07/26-18:06:40.122367 6f34                Options.blob_file_starting_level: 0
2025/07/26-18:06:40.122369 6f34         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:06:40.122370 6f34            Options.memtable_max_range_deletions: 0
2025/07/26-18:06:40.122477 6f34 [db/db_impl/db_impl.cc:3637] Created column family [task_versions] (ID 3)
2025/07/26-18:06:40.125115 6f34 [db/column_family.cc:618] --------------- Options for column family [task_index]:
2025/07/26-18:06:40.125122 6f34               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:06:40.125124 6f34           Options.merge_operator: None
2025/07/26-18:06:40.125126 6f34        Options.compaction_filter: None
2025/07/26-18:06:40.125127 6f34        Options.compaction_filter_factory: None
2025/07/26-18:06:40.125129 6f34  Options.sst_partitioner_factory: None
2025/07/26-18:06:40.125131 6f34         Options.memtable_factory: SkipListFactory
2025/07/26-18:06:40.125133 6f34            Options.table_factory: BlockBasedTable
2025/07/26-18:06:40.125148 6f34            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001902C3588C0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 000001902C381A30
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:06:40.125152 6f34        Options.write_buffer_size: 67108864
2025/07/26-18:06:40.125154 6f34  Options.max_write_buffer_number: 2
2025/07/26-18:06:40.125156 6f34          Options.compression: Snappy
2025/07/26-18:06:40.125157 6f34                  Options.bottommost_compression: Disabled
2025/07/26-18:06:40.125159 6f34       Options.prefix_extractor: nullptr
2025/07/26-18:06:40.125161 6f34   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:06:40.125163 6f34             Options.num_levels: 7
2025/07/26-18:06:40.125164 6f34        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:06:40.125166 6f34     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:06:40.125168 6f34     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:06:40.125170 6f34            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:06:40.125171 6f34                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:06:40.125173 6f34               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:06:40.125175 6f34         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:06:40.125177 6f34         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:06:40.125179 6f34         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:06:40.125180 6f34                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:06:40.125182 6f34         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:06:40.125184 6f34         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:06:40.125186 6f34            Options.compression_opts.window_bits: -14
2025/07/26-18:06:40.125187 6f34                  Options.compression_opts.level: 32767
2025/07/26-18:06:40.125189 6f34               Options.compression_opts.strategy: 0
2025/07/26-18:06:40.125191 6f34         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:06:40.125193 6f34         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:06:40.125194 6f34         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:06:40.125196 6f34         Options.compression_opts.parallel_threads: 1
2025/07/26-18:06:40.125198 6f34                  Options.compression_opts.enabled: false
2025/07/26-18:06:40.125200 6f34         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:06:40.125201 6f34      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:06:40.125203 6f34          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:06:40.125205 6f34              Options.level0_stop_writes_trigger: 36
2025/07/26-18:06:40.125207 6f34                   Options.target_file_size_base: 67108864
2025/07/26-18:06:40.125208 6f34             Options.target_file_size_multiplier: 1
2025/07/26-18:06:40.125210 6f34                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:06:40.125212 6f34 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:06:40.125214 6f34          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:06:40.125216 6f34 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:06:40.125217 6f34 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:06:40.125219 6f34 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:06:40.125221 6f34 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:06:40.125223 6f34 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:06:40.125225 6f34 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:06:40.125227 6f34 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:06:40.125229 6f34       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:06:40.125230 6f34                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:06:40.125232 6f34   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:06:40.125234 6f34                        Options.arena_block_size: 1048576
2025/07/26-18:06:40.125236 6f34   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:06:40.125237 6f34   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:06:40.125239 6f34                Options.disable_auto_compactions: 0
2025/07/26-18:06:40.125241 6f34                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:06:40.125243 6f34                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:06:40.125245 6f34 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:06:40.125246 6f34 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:06:40.125248 6f34 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:06:40.125250 6f34 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:06:40.125252 6f34 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:06:40.125253 6f34 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:06:40.125255 6f34 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:06:40.125257 6f34 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:06:40.125259 6f34                   Options.table_properties_collectors: 
2025/07/26-18:06:40.125261 6f34                   Options.inplace_update_support: 0
2025/07/26-18:06:40.125263 6f34                 Options.inplace_update_num_locks: 10000
2025/07/26-18:06:40.125265 6f34               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:06:40.125266 6f34               Options.memtable_whole_key_filtering: 0
2025/07/26-18:06:40.125268 6f34   Options.memtable_huge_page_size: 0
2025/07/26-18:06:40.125270 6f34                           Options.bloom_locality: 0
2025/07/26-18:06:40.125272 6f34                    Options.max_successive_merges: 0
2025/07/26-18:06:40.125274 6f34                Options.optimize_filters_for_hits: 0
2025/07/26-18:06:40.125275 6f34                Options.paranoid_file_checks: 0
2025/07/26-18:06:40.125277 6f34                Options.force_consistency_checks: 1
2025/07/26-18:06:40.125279 6f34                Options.report_bg_io_stats: 0
2025/07/26-18:06:40.125280 6f34                               Options.ttl: 2592000
2025/07/26-18:06:40.125282 6f34          Options.periodic_compaction_seconds: 0
2025/07/26-18:06:40.125284 6f34                        Options.default_temperature: kUnknown
2025/07/26-18:06:40.125286 6f34  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:06:40.125288 6f34    Options.preserve_internal_time_seconds: 0
2025/07/26-18:06:40.125290 6f34                       Options.enable_blob_files: false
2025/07/26-18:06:40.125292 6f34                           Options.min_blob_size: 0
2025/07/26-18:06:40.125293 6f34                          Options.blob_file_size: 268435456
2025/07/26-18:06:40.125295 6f34                   Options.blob_compression_type: NoCompression
2025/07/26-18:06:40.125297 6f34          Options.enable_blob_garbage_collection: false
2025/07/26-18:06:40.125299 6f34      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:06:40.125301 6f34 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:06:40.125302 6f34          Options.blob_compaction_readahead_size: 0
2025/07/26-18:06:40.125304 6f34                Options.blob_file_starting_level: 0
2025/07/26-18:06:40.125306 6f34         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:06:40.125308 6f34            Options.memtable_max_range_deletions: 0
2025/07/26-18:06:40.125417 6f34 [db/db_impl/db_impl.cc:3637] Created column family [task_index] (ID 4)
2025/07/26-18:06:40.131947 6f34 [db/db_impl/db_impl_open.cc:2157] SstFileManager instance 000001902DFC9B00
2025/07/26-18:06:40.132250 6f34 DB pointer 000001902C3D7480
2025/07/26-18:06:40.132758 62b4 [db/db_impl/db_impl.cc:1140] ------- DUMPING STATS -------
2025/07/26-18:06:40.132767 62b4 [db/db_impl/db_impl.cc:1141] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000001902C381EE0#14164 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 4.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** Compaction Stats [tasks] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [tasks] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000001902C381940#14164 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2.1e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [tasks] **

** Compaction Stats [raft_state] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [raft_state] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000001902C382480#14164 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2.2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [raft_state] **

** Compaction Stats [task_versions] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_versions] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000001902C381670#14164 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_versions] **

** Compaction Stats [task_index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000001902C381A30#14164 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 1.8e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_index] **
