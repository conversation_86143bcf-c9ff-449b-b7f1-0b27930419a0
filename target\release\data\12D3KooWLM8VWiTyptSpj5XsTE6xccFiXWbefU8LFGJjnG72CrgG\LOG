2025/07/26-17:57:07.420665 64b0 RocksDB version: 8.10.0
2025/07/26-17:57:07.421275 64b0 Compile date 2023-12-15 13:01:14
2025/07/26-17:57:07.421285 64b0 DB SUMMARY
2025/07/26-17:57:07.421292 64b0 Host name (Env):  DESKTOP-RNBFHG8
2025/07/26-17:57:07.421297 64b0 DB Session ID:  DLRFQTCKJQE2MWBHZ9AT
2025/07/26-17:57:07.421474 64b0 SST files in ./data/12D3KooWLM8VWiTyptSpj5XsTE6xccFiXWbefU8LFGJjnG72CrgG dir, Total Num: 0, files: 
2025/07/26-17:57:07.421490 64b0 Write Ahead Log file in ./data/12D3KooWLM8VWiTyptSpj5XsTE6xccFiXWbefU8LFGJjnG72CrgG: 
2025/07/26-17:57:07.421496 64b0                         Options.error_if_exists: 0
2025/07/26-17:57:07.421501 64b0                       Options.create_if_missing: 1
2025/07/26-17:57:07.421506 64b0                         Options.paranoid_checks: 1
2025/07/26-17:57:07.431359 64b0             Options.flush_verify_memtable_count: 1
2025/07/26-17:57:07.431371 64b0          Options.compaction_verify_record_count: 1
2025/07/26-17:57:07.431373 64b0                               Options.track_and_verify_wals_in_manifest: 0
2025/07/26-17:57:07.431375 64b0        Options.verify_sst_unique_id_in_manifest: 1
2025/07/26-17:57:07.431377 64b0                                     Options.env: 00000232937ED1E0
2025/07/26-17:57:07.431380 64b0                                      Options.fs: WinFS
2025/07/26-17:57:07.431382 64b0                                Options.info_log: 000002329375E1E0
2025/07/26-17:57:07.431384 64b0                Options.max_file_opening_threads: 16
2025/07/26-17:57:07.431386 64b0                              Options.statistics: 0000000000000000
2025/07/26-17:57:07.431387 64b0                               Options.use_fsync: 0
2025/07/26-17:57:07.431389 64b0                       Options.max_log_file_size: 0
2025/07/26-17:57:07.431391 64b0                  Options.max_manifest_file_size: 1073741824
2025/07/26-17:57:07.431393 64b0                   Options.log_file_time_to_roll: 0
2025/07/26-17:57:07.431395 64b0                       Options.keep_log_file_num: 1000
2025/07/26-17:57:07.431396 64b0                    Options.recycle_log_file_num: 0
2025/07/26-17:57:07.431398 64b0                         Options.allow_fallocate: 1
2025/07/26-17:57:07.431400 64b0                        Options.allow_mmap_reads: 0
2025/07/26-17:57:07.431402 64b0                       Options.allow_mmap_writes: 0
2025/07/26-17:57:07.431403 64b0                        Options.use_direct_reads: 0
2025/07/26-17:57:07.431405 64b0                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/26-17:57:07.431407 64b0          Options.create_missing_column_families: 1
2025/07/26-17:57:07.431409 64b0                              Options.db_log_dir: 
2025/07/26-17:57:07.431410 64b0                                 Options.wal_dir: 
2025/07/26-17:57:07.431412 64b0                Options.table_cache_numshardbits: 6
2025/07/26-17:57:07.431414 64b0                         Options.WAL_ttl_seconds: 0
2025/07/26-17:57:07.431416 64b0                       Options.WAL_size_limit_MB: 0
2025/07/26-17:57:07.431417 64b0                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/26-17:57:07.431419 64b0             Options.manifest_preallocation_size: 4194304
2025/07/26-17:57:07.431421 64b0                     Options.is_fd_close_on_exec: 1
2025/07/26-17:57:07.431423 64b0                   Options.advise_random_on_open: 1
2025/07/26-17:57:07.431424 64b0                    Options.db_write_buffer_size: 0
2025/07/26-17:57:07.431426 64b0                    Options.write_buffer_manager: 00000232937ED960
2025/07/26-17:57:07.431428 64b0         Options.access_hint_on_compaction_start: 1
2025/07/26-17:57:07.431430 64b0           Options.random_access_max_buffer_size: 1048576
2025/07/26-17:57:07.431431 64b0                      Options.use_adaptive_mutex: 0
2025/07/26-17:57:07.431433 64b0                            Options.rate_limiter: 0000000000000000
2025/07/26-17:57:07.431436 64b0     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/26-17:57:07.431437 64b0                       Options.wal_recovery_mode: 2
2025/07/26-17:57:07.431464 64b0                  Options.enable_thread_tracking: 0
2025/07/26-17:57:07.431468 64b0                  Options.enable_pipelined_write: 0
2025/07/26-17:57:07.431470 64b0                  Options.unordered_write: 0
2025/07/26-17:57:07.431471 64b0         Options.allow_concurrent_memtable_write: 1
2025/07/26-17:57:07.431473 64b0      Options.enable_write_thread_adaptive_yield: 1
2025/07/26-17:57:07.431475 64b0             Options.write_thread_max_yield_usec: 100
2025/07/26-17:57:07.431477 64b0            Options.write_thread_slow_yield_usec: 3
2025/07/26-17:57:07.431478 64b0                               Options.row_cache: None
2025/07/26-17:57:07.431480 64b0                              Options.wal_filter: None
2025/07/26-17:57:07.431482 64b0             Options.avoid_flush_during_recovery: 0
2025/07/26-17:57:07.431484 64b0             Options.allow_ingest_behind: 0
2025/07/26-17:57:07.431486 64b0             Options.two_write_queues: 0
2025/07/26-17:57:07.431487 64b0             Options.manual_wal_flush: 0
2025/07/26-17:57:07.431489 64b0             Options.wal_compression: 0
2025/07/26-17:57:07.431491 64b0             Options.atomic_flush: 0
2025/07/26-17:57:07.431492 64b0             Options.avoid_unnecessary_blocking_io: 0
2025/07/26-17:57:07.431494 64b0                 Options.persist_stats_to_disk: 0
2025/07/26-17:57:07.431496 64b0                 Options.write_dbid_to_manifest: 0
2025/07/26-17:57:07.431498 64b0                 Options.log_readahead_size: 0
2025/07/26-17:57:07.431500 64b0                 Options.file_checksum_gen_factory: Unknown
2025/07/26-17:57:07.431501 64b0                 Options.best_efforts_recovery: 0
2025/07/26-17:57:07.431503 64b0                Options.max_bgerror_resume_count: 2147483647
2025/07/26-17:57:07.431505 64b0            Options.bgerror_resume_retry_interval: 1000000
2025/07/26-17:57:07.431507 64b0             Options.allow_data_in_errors: 0
2025/07/26-17:57:07.431508 64b0             Options.db_host_id: __hostname__
2025/07/26-17:57:07.431510 64b0             Options.enforce_single_del_contracts: true
2025/07/26-17:57:07.431512 64b0             Options.max_background_jobs: 4
2025/07/26-17:57:07.431514 64b0             Options.max_background_compactions: -1
2025/07/26-17:57:07.431516 64b0             Options.max_subcompactions: 1
2025/07/26-17:57:07.431518 64b0             Options.avoid_flush_during_shutdown: 0
2025/07/26-17:57:07.431519 64b0           Options.writable_file_max_buffer_size: 1048576
2025/07/26-17:57:07.431521 64b0             Options.delayed_write_rate : 16777216
2025/07/26-17:57:07.431523 64b0             Options.max_total_wal_size: 0
2025/07/26-17:57:07.431525 64b0             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/26-17:57:07.431527 64b0                   Options.stats_dump_period_sec: 600
2025/07/26-17:57:07.431528 64b0                 Options.stats_persist_period_sec: 600
2025/07/26-17:57:07.431530 64b0                 Options.stats_history_buffer_size: 1048576
2025/07/26-17:57:07.431532 64b0                          Options.max_open_files: 1000
2025/07/26-17:57:07.431534 64b0                          Options.bytes_per_sync: 0
2025/07/26-17:57:07.431535 64b0                      Options.wal_bytes_per_sync: 0
2025/07/26-17:57:07.431566 64b0                   Options.strict_bytes_per_sync: 0
2025/07/26-17:57:07.431592 64b0       Options.compaction_readahead_size: 2097152
2025/07/26-17:57:07.431595 64b0                  Options.max_background_flushes: -1
2025/07/26-17:57:07.431596 64b0 Options.daily_offpeak_time_utc: 
2025/07/26-17:57:07.431598 64b0 Compression algorithms supported:
2025/07/26-17:57:07.431601 64b0 	kZSTD supported: 1
2025/07/26-17:57:07.431603 64b0 	kSnappyCompression supported: 1
2025/07/26-17:57:07.431605 64b0 	kBZip2Compression supported: 1
2025/07/26-17:57:07.431607 64b0 	kZlibCompression supported: 1
2025/07/26-17:57:07.431608 64b0 	kLZ4Compression supported: 1
2025/07/26-17:57:07.431610 64b0 	kXpressCompression supported: 0
2025/07/26-17:57:07.431612 64b0 	kLZ4HCCompression supported: 1
2025/07/26-17:57:07.431622 64b0 	kZSTDNotFinalCompression supported: 1
2025/07/26-17:57:07.431627 64b0 Fast CRC32 supported: Not supported on x86
2025/07/26-17:57:07.431629 64b0 DMutex implementation: std::mutex
2025/07/26-17:57:07.438323 64b0 [db/db_impl/db_impl_open.cc:325] Creating manifest 1 
2025/07/26-17:57:07.447234 64b0 [db/version_set.cc:5942] Recovering from manifest file: ./data/12D3KooWLM8VWiTyptSpj5XsTE6xccFiXWbefU8LFGJjnG72CrgG/MANIFEST-000001
2025/07/26-17:57:07.447355 64b0 [db/column_family.cc:618] --------------- Options for column family [default]:
2025/07/26-17:57:07.447360 64b0               Options.comparator: leveldb.BytewiseComparator
2025/07/26-17:57:07.447363 64b0           Options.merge_operator: None
2025/07/26-17:57:07.447365 64b0        Options.compaction_filter: None
2025/07/26-17:57:07.447367 64b0        Options.compaction_filter_factory: None
2025/07/26-17:57:07.447368 64b0  Options.sst_partitioner_factory: None
2025/07/26-17:57:07.447370 64b0         Options.memtable_factory: SkipListFactory
2025/07/26-17:57:07.447372 64b0            Options.table_factory: BlockBasedTable
2025/07/26-17:57:07.447403 64b0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000232937C10A0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000232937EDB50
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-17:57:07.447406 64b0        Options.write_buffer_size: 67108864
2025/07/26-17:57:07.447408 64b0  Options.max_write_buffer_number: 2
2025/07/26-17:57:07.447410 64b0          Options.compression: Snappy
2025/07/26-17:57:07.447412 64b0                  Options.bottommost_compression: Disabled
2025/07/26-17:57:07.447413 64b0       Options.prefix_extractor: nullptr
2025/07/26-17:57:07.447415 64b0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-17:57:07.447417 64b0             Options.num_levels: 7
2025/07/26-17:57:07.447419 64b0        Options.min_write_buffer_number_to_merge: 1
2025/07/26-17:57:07.447420 64b0     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-17:57:07.447422 64b0     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-17:57:07.447424 64b0            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-17:57:07.447426 64b0                  Options.bottommost_compression_opts.level: 32767
2025/07/26-17:57:07.447428 64b0               Options.bottommost_compression_opts.strategy: 0
2025/07/26-17:57:07.447429 64b0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-17:57:07.447431 64b0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:07.447433 64b0         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-17:57:07.447435 64b0                  Options.bottommost_compression_opts.enabled: false
2025/07/26-17:57:07.447437 64b0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:07.447438 64b0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:07.447440 64b0            Options.compression_opts.window_bits: -14
2025/07/26-17:57:07.447443 64b0                  Options.compression_opts.level: 32767
2025/07/26-17:57:07.447446 64b0               Options.compression_opts.strategy: 0
2025/07/26-17:57:07.447448 64b0         Options.compression_opts.max_dict_bytes: 0
2025/07/26-17:57:07.447449 64b0         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:07.447451 64b0         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:07.447453 64b0         Options.compression_opts.parallel_threads: 1
2025/07/26-17:57:07.447455 64b0                  Options.compression_opts.enabled: false
2025/07/26-17:57:07.447456 64b0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:07.447458 64b0      Options.level0_file_num_compaction_trigger: 4
2025/07/26-17:57:07.447460 64b0          Options.level0_slowdown_writes_trigger: 20
2025/07/26-17:57:07.447462 64b0              Options.level0_stop_writes_trigger: 36
2025/07/26-17:57:07.447463 64b0                   Options.target_file_size_base: 67108864
2025/07/26-17:57:07.447465 64b0             Options.target_file_size_multiplier: 1
2025/07/26-17:57:07.447467 64b0                Options.max_bytes_for_level_base: 268435456
2025/07/26-17:57:07.447469 64b0 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-17:57:07.447470 64b0          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-17:57:07.447473 64b0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-17:57:07.447474 64b0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-17:57:07.447476 64b0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-17:57:07.447478 64b0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-17:57:07.447480 64b0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-17:57:07.447482 64b0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-17:57:07.447483 64b0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-17:57:07.447485 64b0       Options.max_sequential_skip_in_iterations: 8
2025/07/26-17:57:07.447487 64b0                    Options.max_compaction_bytes: 1677721600
2025/07/26-17:57:07.447489 64b0   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-17:57:07.447490 64b0                        Options.arena_block_size: 1048576
2025/07/26-17:57:07.447497 64b0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-17:57:07.447498 64b0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-17:57:07.447500 64b0                Options.disable_auto_compactions: 0
2025/07/26-17:57:07.447503 64b0                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-17:57:07.447505 64b0                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-17:57:07.447507 64b0 Options.compaction_options_universal.size_ratio: 1
2025/07/26-17:57:07.447508 64b0 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-17:57:07.447510 64b0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-17:57:07.447512 64b0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-17:57:07.447514 64b0 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-17:57:07.447516 64b0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-17:57:07.447518 64b0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-17:57:07.447519 64b0 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-17:57:07.447522 64b0                   Options.table_properties_collectors: 
2025/07/26-17:57:07.447524 64b0                   Options.inplace_update_support: 0
2025/07/26-17:57:07.447526 64b0                 Options.inplace_update_num_locks: 10000
2025/07/26-17:57:07.447528 64b0               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-17:57:07.447530 64b0               Options.memtable_whole_key_filtering: 0
2025/07/26-17:57:07.447531 64b0   Options.memtable_huge_page_size: 0
2025/07/26-17:57:07.447552 64b0                           Options.bloom_locality: 0
2025/07/26-17:57:07.447555 64b0                    Options.max_successive_merges: 0
2025/07/26-17:57:07.447556 64b0                Options.optimize_filters_for_hits: 0
2025/07/26-17:57:07.447558 64b0                Options.paranoid_file_checks: 0
2025/07/26-17:57:07.447560 64b0                Options.force_consistency_checks: 1
2025/07/26-17:57:07.447561 64b0                Options.report_bg_io_stats: 0
2025/07/26-17:57:07.447563 64b0                               Options.ttl: 2592000
2025/07/26-17:57:07.447565 64b0          Options.periodic_compaction_seconds: 0
2025/07/26-17:57:07.447567 64b0                        Options.default_temperature: kUnknown
2025/07/26-17:57:07.447569 64b0  Options.preclude_last_level_data_seconds: 0
2025/07/26-17:57:07.447571 64b0    Options.preserve_internal_time_seconds: 0
2025/07/26-17:57:07.447572 64b0                       Options.enable_blob_files: false
2025/07/26-17:57:07.447574 64b0                           Options.min_blob_size: 0
2025/07/26-17:57:07.447576 64b0                          Options.blob_file_size: 268435456
2025/07/26-17:57:07.447578 64b0                   Options.blob_compression_type: NoCompression
2025/07/26-17:57:07.447580 64b0          Options.enable_blob_garbage_collection: false
2025/07/26-17:57:07.447581 64b0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-17:57:07.447583 64b0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-17:57:07.447585 64b0          Options.blob_compaction_readahead_size: 0
2025/07/26-17:57:07.447587 64b0                Options.blob_file_starting_level: 0
2025/07/26-17:57:07.447589 64b0         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-17:57:07.447591 64b0            Options.memtable_max_range_deletions: 0
2025/07/26-17:57:07.448379 64b0 [db/version_set.cc:5993] Recovered from manifest file:./data/12D3KooWLM8VWiTyptSpj5XsTE6xccFiXWbefU8LFGJjnG72CrgG/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/07/26-17:57:07.448386 64b0 [db/version_set.cc:6002] Column family [default] (ID 0), log number is 0
2025/07/26-17:57:07.448586 64b0 [db/db_impl/db_impl_open.cc:646] DB ID: e34b1985-6a06-11f0-98b6-d4e98a1a402d
2025/07/26-17:57:07.449825 64b0 [db/version_set.cc:5439] Creating manifest 5
2025/07/26-17:57:07.460649 64b0 [db/column_family.cc:618] --------------- Options for column family [tasks]:
2025/07/26-17:57:07.460658 64b0               Options.comparator: leveldb.BytewiseComparator
2025/07/26-17:57:07.460661 64b0           Options.merge_operator: None
2025/07/26-17:57:07.460663 64b0        Options.compaction_filter: None
2025/07/26-17:57:07.460664 64b0        Options.compaction_filter_factory: None
2025/07/26-17:57:07.460666 64b0  Options.sst_partitioner_factory: None
2025/07/26-17:57:07.460668 64b0         Options.memtable_factory: SkipListFactory
2025/07/26-17:57:07.460670 64b0            Options.table_factory: BlockBasedTable
2025/07/26-17:57:07.460689 64b0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000232937C8E20)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000232937ED6A0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-17:57:07.460694 64b0        Options.write_buffer_size: 67108864
2025/07/26-17:57:07.460697 64b0  Options.max_write_buffer_number: 2
2025/07/26-17:57:07.460699 64b0          Options.compression: Snappy
2025/07/26-17:57:07.460701 64b0                  Options.bottommost_compression: Disabled
2025/07/26-17:57:07.460703 64b0       Options.prefix_extractor: nullptr
2025/07/26-17:57:07.460705 64b0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-17:57:07.460706 64b0             Options.num_levels: 7
2025/07/26-17:57:07.460708 64b0        Options.min_write_buffer_number_to_merge: 1
2025/07/26-17:57:07.460710 64b0     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-17:57:07.460712 64b0     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-17:57:07.460713 64b0            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-17:57:07.460715 64b0                  Options.bottommost_compression_opts.level: 32767
2025/07/26-17:57:07.460717 64b0               Options.bottommost_compression_opts.strategy: 0
2025/07/26-17:57:07.460719 64b0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-17:57:07.460721 64b0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:07.460722 64b0         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-17:57:07.460724 64b0                  Options.bottommost_compression_opts.enabled: false
2025/07/26-17:57:07.460726 64b0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:07.460728 64b0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:07.460730 64b0            Options.compression_opts.window_bits: -14
2025/07/26-17:57:07.460732 64b0                  Options.compression_opts.level: 32767
2025/07/26-17:57:07.460733 64b0               Options.compression_opts.strategy: 0
2025/07/26-17:57:07.460735 64b0         Options.compression_opts.max_dict_bytes: 0
2025/07/26-17:57:07.460737 64b0         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:07.460781 64b0         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:07.460783 64b0         Options.compression_opts.parallel_threads: 1
2025/07/26-17:57:07.460785 64b0                  Options.compression_opts.enabled: false
2025/07/26-17:57:07.460787 64b0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:07.460789 64b0      Options.level0_file_num_compaction_trigger: 4
2025/07/26-17:57:07.460790 64b0          Options.level0_slowdown_writes_trigger: 20
2025/07/26-17:57:07.460792 64b0              Options.level0_stop_writes_trigger: 36
2025/07/26-17:57:07.460794 64b0                   Options.target_file_size_base: 67108864
2025/07/26-17:57:07.460796 64b0             Options.target_file_size_multiplier: 1
2025/07/26-17:57:07.460797 64b0                Options.max_bytes_for_level_base: 268435456
2025/07/26-17:57:07.460799 64b0 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-17:57:07.460801 64b0          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-17:57:07.460807 64b0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-17:57:07.460809 64b0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-17:57:07.460811 64b0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-17:57:07.460812 64b0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-17:57:07.460814 64b0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-17:57:07.460816 64b0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-17:57:07.460818 64b0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-17:57:07.460819 64b0       Options.max_sequential_skip_in_iterations: 8
2025/07/26-17:57:07.460821 64b0                    Options.max_compaction_bytes: 1677721600
2025/07/26-17:57:07.460823 64b0   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-17:57:07.460826 64b0                        Options.arena_block_size: 1048576
2025/07/26-17:57:07.460827 64b0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-17:57:07.460829 64b0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-17:57:07.460831 64b0                Options.disable_auto_compactions: 0
2025/07/26-17:57:07.460833 64b0                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-17:57:07.460836 64b0                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-17:57:07.460837 64b0 Options.compaction_options_universal.size_ratio: 1
2025/07/26-17:57:07.460839 64b0 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-17:57:07.460841 64b0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-17:57:07.460843 64b0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-17:57:07.460845 64b0 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-17:57:07.460847 64b0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-17:57:07.460849 64b0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-17:57:07.460851 64b0 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-17:57:07.460854 64b0                   Options.table_properties_collectors: 
2025/07/26-17:57:07.460856 64b0                   Options.inplace_update_support: 0
2025/07/26-17:57:07.460857 64b0                 Options.inplace_update_num_locks: 10000
2025/07/26-17:57:07.460859 64b0               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-17:57:07.460861 64b0               Options.memtable_whole_key_filtering: 0
2025/07/26-17:57:07.460863 64b0   Options.memtable_huge_page_size: 0
2025/07/26-17:57:07.460865 64b0                           Options.bloom_locality: 0
2025/07/26-17:57:07.460867 64b0                    Options.max_successive_merges: 0
2025/07/26-17:57:07.460868 64b0                Options.optimize_filters_for_hits: 0
2025/07/26-17:57:07.460870 64b0                Options.paranoid_file_checks: 0
2025/07/26-17:57:07.460872 64b0                Options.force_consistency_checks: 1
2025/07/26-17:57:07.460874 64b0                Options.report_bg_io_stats: 0
2025/07/26-17:57:07.460875 64b0                               Options.ttl: 2592000
2025/07/26-17:57:07.460877 64b0          Options.periodic_compaction_seconds: 0
2025/07/26-17:57:07.460879 64b0                        Options.default_temperature: kUnknown
2025/07/26-17:57:07.460881 64b0  Options.preclude_last_level_data_seconds: 0
2025/07/26-17:57:07.460882 64b0    Options.preserve_internal_time_seconds: 0
2025/07/26-17:57:07.460884 64b0                       Options.enable_blob_files: false
2025/07/26-17:57:07.460886 64b0                           Options.min_blob_size: 0
2025/07/26-17:57:07.460888 64b0                          Options.blob_file_size: 268435456
2025/07/26-17:57:07.460889 64b0                   Options.blob_compression_type: NoCompression
2025/07/26-17:57:07.460891 64b0          Options.enable_blob_garbage_collection: false
2025/07/26-17:57:07.460893 64b0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-17:57:07.460895 64b0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-17:57:07.460897 64b0          Options.blob_compaction_readahead_size: 0
2025/07/26-17:57:07.460899 64b0                Options.blob_file_starting_level: 0
2025/07/26-17:57:07.460901 64b0         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-17:57:07.460902 64b0            Options.memtable_max_range_deletions: 0
2025/07/26-17:57:07.461056 64b0 [db/db_impl/db_impl.cc:3637] Created column family [tasks] (ID 1)
2025/07/26-17:57:07.463643 64b0 [db/column_family.cc:618] --------------- Options for column family [raft_state]:
2025/07/26-17:57:07.463649 64b0               Options.comparator: leveldb.BytewiseComparator
2025/07/26-17:57:07.463652 64b0           Options.merge_operator: None
2025/07/26-17:57:07.463655 64b0        Options.compaction_filter: None
2025/07/26-17:57:07.463656 64b0        Options.compaction_filter_factory: None
2025/07/26-17:57:07.463658 64b0  Options.sst_partitioner_factory: None
2025/07/26-17:57:07.463660 64b0         Options.memtable_factory: SkipListFactory
2025/07/26-17:57:07.463662 64b0            Options.table_factory: BlockBasedTable
2025/07/26-17:57:07.463679 64b0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000232937C7470)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000232937ED3D0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-17:57:07.463682 64b0        Options.write_buffer_size: 67108864
2025/07/26-17:57:07.463684 64b0  Options.max_write_buffer_number: 2
2025/07/26-17:57:07.463686 64b0          Options.compression: Snappy
2025/07/26-17:57:07.463687 64b0                  Options.bottommost_compression: Disabled
2025/07/26-17:57:07.463689 64b0       Options.prefix_extractor: nullptr
2025/07/26-17:57:07.463691 64b0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-17:57:07.463693 64b0             Options.num_levels: 7
2025/07/26-17:57:07.463694 64b0        Options.min_write_buffer_number_to_merge: 1
2025/07/26-17:57:07.463696 64b0     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-17:57:07.463698 64b0     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-17:57:07.463700 64b0            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-17:57:07.463702 64b0                  Options.bottommost_compression_opts.level: 32767
2025/07/26-17:57:07.463703 64b0               Options.bottommost_compression_opts.strategy: 0
2025/07/26-17:57:07.463705 64b0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-17:57:07.463707 64b0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:07.463709 64b0         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-17:57:07.463710 64b0                  Options.bottommost_compression_opts.enabled: false
2025/07/26-17:57:07.463712 64b0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:07.463714 64b0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:07.463716 64b0            Options.compression_opts.window_bits: -14
2025/07/26-17:57:07.463718 64b0                  Options.compression_opts.level: 32767
2025/07/26-17:57:07.463719 64b0               Options.compression_opts.strategy: 0
2025/07/26-17:57:07.463721 64b0         Options.compression_opts.max_dict_bytes: 0
2025/07/26-17:57:07.463723 64b0         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:07.463725 64b0         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:07.463726 64b0         Options.compression_opts.parallel_threads: 1
2025/07/26-17:57:07.463728 64b0                  Options.compression_opts.enabled: false
2025/07/26-17:57:07.463751 64b0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:07.463753 64b0      Options.level0_file_num_compaction_trigger: 4
2025/07/26-17:57:07.463755 64b0          Options.level0_slowdown_writes_trigger: 20
2025/07/26-17:57:07.463757 64b0              Options.level0_stop_writes_trigger: 36
2025/07/26-17:57:07.463758 64b0                   Options.target_file_size_base: 67108864
2025/07/26-17:57:07.463760 64b0             Options.target_file_size_multiplier: 1
2025/07/26-17:57:07.463762 64b0                Options.max_bytes_for_level_base: 268435456
2025/07/26-17:57:07.463764 64b0 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-17:57:07.463765 64b0          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-17:57:07.463767 64b0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-17:57:07.463769 64b0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-17:57:07.463771 64b0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-17:57:07.463773 64b0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-17:57:07.463774 64b0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-17:57:07.463776 64b0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-17:57:07.463778 64b0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-17:57:07.463780 64b0       Options.max_sequential_skip_in_iterations: 8
2025/07/26-17:57:07.463781 64b0                    Options.max_compaction_bytes: 1677721600
2025/07/26-17:57:07.463783 64b0   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-17:57:07.463785 64b0                        Options.arena_block_size: 1048576
2025/07/26-17:57:07.463787 64b0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-17:57:07.463788 64b0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-17:57:07.463790 64b0                Options.disable_auto_compactions: 0
2025/07/26-17:57:07.463792 64b0                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-17:57:07.463794 64b0                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-17:57:07.463796 64b0 Options.compaction_options_universal.size_ratio: 1
2025/07/26-17:57:07.463797 64b0 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-17:57:07.463799 64b0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-17:57:07.463801 64b0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-17:57:07.463803 64b0 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-17:57:07.463805 64b0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-17:57:07.463806 64b0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-17:57:07.463808 64b0 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-17:57:07.463812 64b0                   Options.table_properties_collectors: 
2025/07/26-17:57:07.463817 64b0                   Options.inplace_update_support: 0
2025/07/26-17:57:07.463819 64b0                 Options.inplace_update_num_locks: 10000
2025/07/26-17:57:07.463821 64b0               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-17:57:07.463823 64b0               Options.memtable_whole_key_filtering: 0
2025/07/26-17:57:07.463824 64b0   Options.memtable_huge_page_size: 0
2025/07/26-17:57:07.463826 64b0                           Options.bloom_locality: 0
2025/07/26-17:57:07.463828 64b0                    Options.max_successive_merges: 0
2025/07/26-17:57:07.463830 64b0                Options.optimize_filters_for_hits: 0
2025/07/26-17:57:07.463832 64b0                Options.paranoid_file_checks: 0
2025/07/26-17:57:07.463833 64b0                Options.force_consistency_checks: 1
2025/07/26-17:57:07.463835 64b0                Options.report_bg_io_stats: 0
2025/07/26-17:57:07.463837 64b0                               Options.ttl: 2592000
2025/07/26-17:57:07.463839 64b0          Options.periodic_compaction_seconds: 0
2025/07/26-17:57:07.463841 64b0                        Options.default_temperature: kUnknown
2025/07/26-17:57:07.463843 64b0  Options.preclude_last_level_data_seconds: 0
2025/07/26-17:57:07.463845 64b0    Options.preserve_internal_time_seconds: 0
2025/07/26-17:57:07.463847 64b0                       Options.enable_blob_files: false
2025/07/26-17:57:07.463849 64b0                           Options.min_blob_size: 0
2025/07/26-17:57:07.463850 64b0                          Options.blob_file_size: 268435456
2025/07/26-17:57:07.463852 64b0                   Options.blob_compression_type: NoCompression
2025/07/26-17:57:07.463854 64b0          Options.enable_blob_garbage_collection: false
2025/07/26-17:57:07.463856 64b0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-17:57:07.463858 64b0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-17:57:07.463860 64b0          Options.blob_compaction_readahead_size: 0
2025/07/26-17:57:07.463862 64b0                Options.blob_file_starting_level: 0
2025/07/26-17:57:07.463863 64b0         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-17:57:07.463865 64b0            Options.memtable_max_range_deletions: 0
2025/07/26-17:57:07.463986 64b0 [db/db_impl/db_impl.cc:3637] Created column family [raft_state] (ID 2)
2025/07/26-17:57:07.465648 64b0 [db/column_family.cc:618] --------------- Options for column family [task_versions]:
2025/07/26-17:57:07.465658 64b0               Options.comparator: leveldb.BytewiseComparator
2025/07/26-17:57:07.465660 64b0           Options.merge_operator: None
2025/07/26-17:57:07.465662 64b0        Options.compaction_filter: None
2025/07/26-17:57:07.465664 64b0        Options.compaction_filter_factory: None
2025/07/26-17:57:07.465665 64b0  Options.sst_partitioner_factory: None
2025/07/26-17:57:07.465667 64b0         Options.memtable_factory: SkipListFactory
2025/07/26-17:57:07.465669 64b0            Options.table_factory: BlockBasedTable
2025/07/26-17:57:07.465693 64b0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000232937C7890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000232937ED4C0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-17:57:07.465696 64b0        Options.write_buffer_size: 67108864
2025/07/26-17:57:07.465698 64b0  Options.max_write_buffer_number: 2
2025/07/26-17:57:07.465700 64b0          Options.compression: Snappy
2025/07/26-17:57:07.465702 64b0                  Options.bottommost_compression: Disabled
2025/07/26-17:57:07.465704 64b0       Options.prefix_extractor: nullptr
2025/07/26-17:57:07.465705 64b0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-17:57:07.465707 64b0             Options.num_levels: 7
2025/07/26-17:57:07.465709 64b0        Options.min_write_buffer_number_to_merge: 1
2025/07/26-17:57:07.465711 64b0     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-17:57:07.465712 64b0     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-17:57:07.465714 64b0            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-17:57:07.465717 64b0                  Options.bottommost_compression_opts.level: 32767
2025/07/26-17:57:07.465720 64b0               Options.bottommost_compression_opts.strategy: 0
2025/07/26-17:57:07.465722 64b0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-17:57:07.465723 64b0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:07.465725 64b0         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-17:57:07.465727 64b0                  Options.bottommost_compression_opts.enabled: false
2025/07/26-17:57:07.465729 64b0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:07.465731 64b0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:07.465732 64b0            Options.compression_opts.window_bits: -14
2025/07/26-17:57:07.465734 64b0                  Options.compression_opts.level: 32767
2025/07/26-17:57:07.465736 64b0               Options.compression_opts.strategy: 0
2025/07/26-17:57:07.465738 64b0         Options.compression_opts.max_dict_bytes: 0
2025/07/26-17:57:07.465739 64b0         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:07.465741 64b0         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:07.465743 64b0         Options.compression_opts.parallel_threads: 1
2025/07/26-17:57:07.465745 64b0                  Options.compression_opts.enabled: false
2025/07/26-17:57:07.465746 64b0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:07.465748 64b0      Options.level0_file_num_compaction_trigger: 4
2025/07/26-17:57:07.465750 64b0          Options.level0_slowdown_writes_trigger: 20
2025/07/26-17:57:07.465752 64b0              Options.level0_stop_writes_trigger: 36
2025/07/26-17:57:07.465885 64b0                   Options.target_file_size_base: 67108864
2025/07/26-17:57:07.465887 64b0             Options.target_file_size_multiplier: 1
2025/07/26-17:57:07.465889 64b0                Options.max_bytes_for_level_base: 268435456
2025/07/26-17:57:07.465891 64b0 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-17:57:07.465893 64b0          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-17:57:07.465895 64b0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-17:57:07.465897 64b0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-17:57:07.465899 64b0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-17:57:07.465901 64b0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-17:57:07.465903 64b0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-17:57:07.465904 64b0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-17:57:07.465906 64b0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-17:57:07.465908 64b0       Options.max_sequential_skip_in_iterations: 8
2025/07/26-17:57:07.465910 64b0                    Options.max_compaction_bytes: 1677721600
2025/07/26-17:57:07.465911 64b0   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-17:57:07.465913 64b0                        Options.arena_block_size: 1048576
2025/07/26-17:57:07.465915 64b0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-17:57:07.465917 64b0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-17:57:07.465919 64b0                Options.disable_auto_compactions: 0
2025/07/26-17:57:07.465921 64b0                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-17:57:07.465923 64b0                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-17:57:07.465925 64b0 Options.compaction_options_universal.size_ratio: 1
2025/07/26-17:57:07.465927 64b0 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-17:57:07.465929 64b0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-17:57:07.465931 64b0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-17:57:07.465932 64b0 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-17:57:07.465935 64b0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-17:57:07.465938 64b0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-17:57:07.465940 64b0 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-17:57:07.465943 64b0                   Options.table_properties_collectors: 
2025/07/26-17:57:07.465945 64b0                   Options.inplace_update_support: 0
2025/07/26-17:57:07.465947 64b0                 Options.inplace_update_num_locks: 10000
2025/07/26-17:57:07.465948 64b0               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-17:57:07.465950 64b0               Options.memtable_whole_key_filtering: 0
2025/07/26-17:57:07.465952 64b0   Options.memtable_huge_page_size: 0
2025/07/26-17:57:07.465954 64b0                           Options.bloom_locality: 0
2025/07/26-17:57:07.465956 64b0                    Options.max_successive_merges: 0
2025/07/26-17:57:07.465958 64b0                Options.optimize_filters_for_hits: 0
2025/07/26-17:57:07.465959 64b0                Options.paranoid_file_checks: 0
2025/07/26-17:57:07.465961 64b0                Options.force_consistency_checks: 1
2025/07/26-17:57:07.465963 64b0                Options.report_bg_io_stats: 0
2025/07/26-17:57:07.465964 64b0                               Options.ttl: 2592000
2025/07/26-17:57:07.465966 64b0          Options.periodic_compaction_seconds: 0
2025/07/26-17:57:07.465968 64b0                        Options.default_temperature: kUnknown
2025/07/26-17:57:07.465970 64b0  Options.preclude_last_level_data_seconds: 0
2025/07/26-17:57:07.465972 64b0    Options.preserve_internal_time_seconds: 0
2025/07/26-17:57:07.465973 64b0                       Options.enable_blob_files: false
2025/07/26-17:57:07.465975 64b0                           Options.min_blob_size: 0
2025/07/26-17:57:07.465977 64b0                          Options.blob_file_size: 268435456
2025/07/26-17:57:07.465979 64b0                   Options.blob_compression_type: NoCompression
2025/07/26-17:57:07.465981 64b0          Options.enable_blob_garbage_collection: false
2025/07/26-17:57:07.465982 64b0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-17:57:07.465984 64b0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-17:57:07.465986 64b0          Options.blob_compaction_readahead_size: 0
2025/07/26-17:57:07.465988 64b0                Options.blob_file_starting_level: 0
2025/07/26-17:57:07.465990 64b0         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-17:57:07.465992 64b0            Options.memtable_max_range_deletions: 0
2025/07/26-17:57:07.466141 64b0 [db/db_impl/db_impl.cc:3637] Created column family [task_versions] (ID 3)
2025/07/26-17:57:07.468763 64b0 [db/column_family.cc:618] --------------- Options for column family [task_index]:
2025/07/26-17:57:07.468780 64b0               Options.comparator: leveldb.BytewiseComparator
2025/07/26-17:57:07.468783 64b0           Options.merge_operator: None
2025/07/26-17:57:07.468784 64b0        Options.compaction_filter: None
2025/07/26-17:57:07.468786 64b0        Options.compaction_filter_factory: None
2025/07/26-17:57:07.468788 64b0  Options.sst_partitioner_factory: None
2025/07/26-17:57:07.468790 64b0         Options.memtable_factory: SkipListFactory
2025/07/26-17:57:07.468792 64b0            Options.table_factory: BlockBasedTable
2025/07/26-17:57:07.468813 64b0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000232937C7BF0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000232937ED790
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-17:57:07.468818 64b0        Options.write_buffer_size: 67108864
2025/07/26-17:57:07.468821 64b0  Options.max_write_buffer_number: 2
2025/07/26-17:57:07.468823 64b0          Options.compression: Snappy
2025/07/26-17:57:07.468825 64b0                  Options.bottommost_compression: Disabled
2025/07/26-17:57:07.468827 64b0       Options.prefix_extractor: nullptr
2025/07/26-17:57:07.468828 64b0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-17:57:07.468830 64b0             Options.num_levels: 7
2025/07/26-17:57:07.468832 64b0        Options.min_write_buffer_number_to_merge: 1
2025/07/26-17:57:07.468834 64b0     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-17:57:07.468835 64b0     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-17:57:07.468874 64b0            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-17:57:07.468876 64b0                  Options.bottommost_compression_opts.level: 32767
2025/07/26-17:57:07.468878 64b0               Options.bottommost_compression_opts.strategy: 0
2025/07/26-17:57:07.468880 64b0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-17:57:07.468882 64b0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:07.468883 64b0         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-17:57:07.468885 64b0                  Options.bottommost_compression_opts.enabled: false
2025/07/26-17:57:07.468887 64b0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:07.468889 64b0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:07.468891 64b0            Options.compression_opts.window_bits: -14
2025/07/26-17:57:07.468892 64b0                  Options.compression_opts.level: 32767
2025/07/26-17:57:07.468894 64b0               Options.compression_opts.strategy: 0
2025/07/26-17:57:07.468896 64b0         Options.compression_opts.max_dict_bytes: 0
2025/07/26-17:57:07.468898 64b0         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:07.468899 64b0         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:07.468901 64b0         Options.compression_opts.parallel_threads: 1
2025/07/26-17:57:07.468903 64b0                  Options.compression_opts.enabled: false
2025/07/26-17:57:07.468905 64b0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:07.468906 64b0      Options.level0_file_num_compaction_trigger: 4
2025/07/26-17:57:07.468908 64b0          Options.level0_slowdown_writes_trigger: 20
2025/07/26-17:57:07.468910 64b0              Options.level0_stop_writes_trigger: 36
2025/07/26-17:57:07.468912 64b0                   Options.target_file_size_base: 67108864
2025/07/26-17:57:07.468913 64b0             Options.target_file_size_multiplier: 1
2025/07/26-17:57:07.468915 64b0                Options.max_bytes_for_level_base: 268435456
2025/07/26-17:57:07.468917 64b0 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-17:57:07.468919 64b0          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-17:57:07.468921 64b0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-17:57:07.468923 64b0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-17:57:07.468925 64b0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-17:57:07.468926 64b0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-17:57:07.468929 64b0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-17:57:07.468931 64b0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-17:57:07.468933 64b0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-17:57:07.468934 64b0       Options.max_sequential_skip_in_iterations: 8
2025/07/26-17:57:07.468936 64b0                    Options.max_compaction_bytes: 1677721600
2025/07/26-17:57:07.468938 64b0   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-17:57:07.468940 64b0                        Options.arena_block_size: 1048576
2025/07/26-17:57:07.468941 64b0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-17:57:07.468943 64b0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-17:57:07.468945 64b0                Options.disable_auto_compactions: 0
2025/07/26-17:57:07.468948 64b0                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-17:57:07.468950 64b0                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-17:57:07.468952 64b0 Options.compaction_options_universal.size_ratio: 1
2025/07/26-17:57:07.468953 64b0 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-17:57:07.468955 64b0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-17:57:07.468957 64b0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-17:57:07.468959 64b0 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-17:57:07.468961 64b0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-17:57:07.468963 64b0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-17:57:07.468964 64b0 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-17:57:07.468968 64b0                   Options.table_properties_collectors: 
2025/07/26-17:57:07.468970 64b0                   Options.inplace_update_support: 0
2025/07/26-17:57:07.468972 64b0                 Options.inplace_update_num_locks: 10000
2025/07/26-17:57:07.468973 64b0               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-17:57:07.468975 64b0               Options.memtable_whole_key_filtering: 0
2025/07/26-17:57:07.468977 64b0   Options.memtable_huge_page_size: 0
2025/07/26-17:57:07.468979 64b0                           Options.bloom_locality: 0
2025/07/26-17:57:07.468980 64b0                    Options.max_successive_merges: 0
2025/07/26-17:57:07.468982 64b0                Options.optimize_filters_for_hits: 0
2025/07/26-17:57:07.468984 64b0                Options.paranoid_file_checks: 0
2025/07/26-17:57:07.468986 64b0                Options.force_consistency_checks: 1
2025/07/26-17:57:07.468987 64b0                Options.report_bg_io_stats: 0
2025/07/26-17:57:07.468989 64b0                               Options.ttl: 2592000
2025/07/26-17:57:07.468991 64b0          Options.periodic_compaction_seconds: 0
2025/07/26-17:57:07.468993 64b0                        Options.default_temperature: kUnknown
2025/07/26-17:57:07.468995 64b0  Options.preclude_last_level_data_seconds: 0
2025/07/26-17:57:07.468996 64b0    Options.preserve_internal_time_seconds: 0
2025/07/26-17:57:07.468998 64b0                       Options.enable_blob_files: false
2025/07/26-17:57:07.469000 64b0                           Options.min_blob_size: 0
2025/07/26-17:57:07.469001 64b0                          Options.blob_file_size: 268435456
2025/07/26-17:57:07.469003 64b0                   Options.blob_compression_type: NoCompression
2025/07/26-17:57:07.469005 64b0          Options.enable_blob_garbage_collection: false
2025/07/26-17:57:07.469007 64b0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-17:57:07.469009 64b0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-17:57:07.469011 64b0          Options.blob_compaction_readahead_size: 0
2025/07/26-17:57:07.469013 64b0                Options.blob_file_starting_level: 0
2025/07/26-17:57:07.469014 64b0         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-17:57:07.469017 64b0            Options.memtable_max_range_deletions: 0
2025/07/26-17:57:07.469190 64b0 [db/db_impl/db_impl.cc:3637] Created column family [task_index] (ID 4)
2025/07/26-17:57:07.476370 64b0 [db/db_impl/db_impl_open.cc:2157] SstFileManager instance 00000232952AD2D0
2025/07/26-17:57:07.476578 64b0 DB pointer 00000232952A00C0
2025/07/26-17:57:07.476963 5d3c [db/db_impl/db_impl.cc:1140] ------- DUMPING STATS -------
2025/07/26-17:57:07.476973 5d3c [db/db_impl/db_impl.cc:1141] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000232937EDB50#2604 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 3.6e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** Compaction Stats [tasks] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [tasks] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000232937ED6A0#2604 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2.2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [tasks] **

** Compaction Stats [raft_state] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [raft_state] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000232937ED3D0#2604 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 1.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [raft_state] **

** Compaction Stats [task_versions] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_versions] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000232937ED4C0#2604 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 1.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_versions] **

** Compaction Stats [task_index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000232937ED790#2604 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 1.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_index] **
