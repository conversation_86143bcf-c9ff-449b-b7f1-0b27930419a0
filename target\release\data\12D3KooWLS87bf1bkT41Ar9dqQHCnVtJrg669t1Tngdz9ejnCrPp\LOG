2025/07/26-18:24:31.923237 5054 RocksDB version: 8.10.0
2025/07/26-18:24:31.923988 5054 Compile date 2023-12-15 13:01:14
2025/07/26-18:24:31.923998 5054 DB SUMMARY
2025/07/26-18:24:31.924005 5054 Host name (Env):  DESKTOP-RNBFHG8
2025/07/26-18:24:31.924009 5054 DB Session ID:  H0B3QI504CL79RULX2GZ
2025/07/26-18:24:31.924140 5054 SST files in ./data/12D3KooWLS87bf1bkT41Ar9dqQHCnVtJrg669t1Tngdz9ejnCrPp dir, Total Num: 0, files: 
2025/07/26-18:24:31.924147 5054 Write Ahead Log file in ./data/12D3KooWLS87bf1bkT41Ar9dqQHCnVtJrg669t1Tngdz9ejnCrPp: 
2025/07/26-18:24:31.924153 5054                         Options.error_if_exists: 0
2025/07/26-18:24:31.924159 5054                       Options.create_if_missing: 1
2025/07/26-18:24:31.924163 5054                         Options.paranoid_checks: 1
2025/07/26-18:24:31.934179 5054             Options.flush_verify_memtable_count: 1
2025/07/26-18:24:31.934196 5054          Options.compaction_verify_record_count: 1
2025/07/26-18:24:31.934198 5054                               Options.track_and_verify_wals_in_manifest: 0
2025/07/26-18:24:31.934200 5054        Options.verify_sst_unique_id_in_manifest: 1
2025/07/26-18:24:31.934201 5054                                     Options.env: 0000023DB96EF160
2025/07/26-18:24:31.934203 5054                                      Options.fs: WinFS
2025/07/26-18:24:31.934206 5054                                Options.info_log: 0000023DB965E520
2025/07/26-18:24:31.934208 5054                Options.max_file_opening_threads: 16
2025/07/26-18:24:31.934209 5054                              Options.statistics: 0000000000000000
2025/07/26-18:24:31.934211 5054                               Options.use_fsync: 0
2025/07/26-18:24:31.934213 5054                       Options.max_log_file_size: 0
2025/07/26-18:24:31.934215 5054                  Options.max_manifest_file_size: 1073741824
2025/07/26-18:24:31.934216 5054                   Options.log_file_time_to_roll: 0
2025/07/26-18:24:31.934218 5054                       Options.keep_log_file_num: 1000
2025/07/26-18:24:31.934220 5054                    Options.recycle_log_file_num: 0
2025/07/26-18:24:31.934221 5054                         Options.allow_fallocate: 1
2025/07/26-18:24:31.934223 5054                        Options.allow_mmap_reads: 0
2025/07/26-18:24:31.934225 5054                       Options.allow_mmap_writes: 0
2025/07/26-18:24:31.934226 5054                        Options.use_direct_reads: 0
2025/07/26-18:24:31.934228 5054                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/26-18:24:31.934230 5054          Options.create_missing_column_families: 1
2025/07/26-18:24:31.934231 5054                              Options.db_log_dir: 
2025/07/26-18:24:31.934233 5054                                 Options.wal_dir: 
2025/07/26-18:24:31.934235 5054                Options.table_cache_numshardbits: 6
2025/07/26-18:24:31.934236 5054                         Options.WAL_ttl_seconds: 0
2025/07/26-18:24:31.934238 5054                       Options.WAL_size_limit_MB: 0
2025/07/26-18:24:31.934240 5054                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/26-18:24:31.934242 5054             Options.manifest_preallocation_size: 4194304
2025/07/26-18:24:31.934243 5054                     Options.is_fd_close_on_exec: 1
2025/07/26-18:24:31.934245 5054                   Options.advise_random_on_open: 1
2025/07/26-18:24:31.934247 5054                    Options.db_write_buffer_size: 0
2025/07/26-18:24:31.934248 5054                    Options.write_buffer_manager: 0000023DB96EFCA0
2025/07/26-18:24:31.934250 5054         Options.access_hint_on_compaction_start: 1
2025/07/26-18:24:31.934252 5054           Options.random_access_max_buffer_size: 1048576
2025/07/26-18:24:31.934253 5054                      Options.use_adaptive_mutex: 0
2025/07/26-18:24:31.934255 5054                            Options.rate_limiter: 0000000000000000
2025/07/26-18:24:31.934257 5054     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/26-18:24:31.934259 5054                       Options.wal_recovery_mode: 2
2025/07/26-18:24:31.934288 5054                  Options.enable_thread_tracking: 0
2025/07/26-18:24:31.934293 5054                  Options.enable_pipelined_write: 0
2025/07/26-18:24:31.934295 5054                  Options.unordered_write: 0
2025/07/26-18:24:31.934296 5054         Options.allow_concurrent_memtable_write: 1
2025/07/26-18:24:31.934298 5054      Options.enable_write_thread_adaptive_yield: 1
2025/07/26-18:24:31.934300 5054             Options.write_thread_max_yield_usec: 100
2025/07/26-18:24:31.934301 5054            Options.write_thread_slow_yield_usec: 3
2025/07/26-18:24:31.934303 5054                               Options.row_cache: None
2025/07/26-18:24:31.934305 5054                              Options.wal_filter: None
2025/07/26-18:24:31.934306 5054             Options.avoid_flush_during_recovery: 0
2025/07/26-18:24:31.934308 5054             Options.allow_ingest_behind: 0
2025/07/26-18:24:31.934310 5054             Options.two_write_queues: 0
2025/07/26-18:24:31.934311 5054             Options.manual_wal_flush: 0
2025/07/26-18:24:31.934313 5054             Options.wal_compression: 0
2025/07/26-18:24:31.934315 5054             Options.atomic_flush: 0
2025/07/26-18:24:31.934316 5054             Options.avoid_unnecessary_blocking_io: 0
2025/07/26-18:24:31.934318 5054                 Options.persist_stats_to_disk: 0
2025/07/26-18:24:31.934320 5054                 Options.write_dbid_to_manifest: 0
2025/07/26-18:24:31.934321 5054                 Options.log_readahead_size: 0
2025/07/26-18:24:31.934323 5054                 Options.file_checksum_gen_factory: Unknown
2025/07/26-18:24:31.934325 5054                 Options.best_efforts_recovery: 0
2025/07/26-18:24:31.934326 5054                Options.max_bgerror_resume_count: 2147483647
2025/07/26-18:24:31.934328 5054            Options.bgerror_resume_retry_interval: 1000000
2025/07/26-18:24:31.934330 5054             Options.allow_data_in_errors: 0
2025/07/26-18:24:31.934331 5054             Options.db_host_id: __hostname__
2025/07/26-18:24:31.934333 5054             Options.enforce_single_del_contracts: true
2025/07/26-18:24:31.934335 5054             Options.max_background_jobs: 4
2025/07/26-18:24:31.934337 5054             Options.max_background_compactions: -1
2025/07/26-18:24:31.934338 5054             Options.max_subcompactions: 1
2025/07/26-18:24:31.934340 5054             Options.avoid_flush_during_shutdown: 0
2025/07/26-18:24:31.934342 5054           Options.writable_file_max_buffer_size: 1048576
2025/07/26-18:24:31.934343 5054             Options.delayed_write_rate : 16777216
2025/07/26-18:24:31.934345 5054             Options.max_total_wal_size: 0
2025/07/26-18:24:31.934347 5054             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/26-18:24:31.934348 5054                   Options.stats_dump_period_sec: 600
2025/07/26-18:24:31.934350 5054                 Options.stats_persist_period_sec: 600
2025/07/26-18:24:31.934352 5054                 Options.stats_history_buffer_size: 1048576
2025/07/26-18:24:31.934353 5054                          Options.max_open_files: 1000
2025/07/26-18:24:31.934355 5054                          Options.bytes_per_sync: 0
2025/07/26-18:24:31.934357 5054                      Options.wal_bytes_per_sync: 0
2025/07/26-18:24:31.934358 5054                   Options.strict_bytes_per_sync: 0
2025/07/26-18:24:31.934360 5054       Options.compaction_readahead_size: 2097152
2025/07/26-18:24:31.934362 5054                  Options.max_background_flushes: -1
2025/07/26-18:24:31.934363 5054 Options.daily_offpeak_time_utc: 
2025/07/26-18:24:31.934365 5054 Compression algorithms supported:
2025/07/26-18:24:31.934368 5054 	kZSTD supported: 1
2025/07/26-18:24:31.934370 5054 	kSnappyCompression supported: 1
2025/07/26-18:24:31.934371 5054 	kBZip2Compression supported: 1
2025/07/26-18:24:31.934373 5054 	kZlibCompression supported: 1
2025/07/26-18:24:31.934375 5054 	kLZ4Compression supported: 1
2025/07/26-18:24:31.934376 5054 	kXpressCompression supported: 0
2025/07/26-18:24:31.934378 5054 	kLZ4HCCompression supported: 1
2025/07/26-18:24:31.934388 5054 	kZSTDNotFinalCompression supported: 1
2025/07/26-18:24:31.934395 5054 Fast CRC32 supported: Not supported on x86
2025/07/26-18:24:31.934396 5054 DMutex implementation: std::mutex
2025/07/26-18:24:31.940111 5054 [db/db_impl/db_impl_open.cc:325] Creating manifest 1 
2025/07/26-18:24:31.950500 5054 [db/version_set.cc:5942] Recovering from manifest file: ./data/12D3KooWLS87bf1bkT41Ar9dqQHCnVtJrg669t1Tngdz9ejnCrPp/MANIFEST-000001
2025/07/26-18:24:31.950641 5054 [db/column_family.cc:618] --------------- Options for column family [default]:
2025/07/26-18:24:31.950646 5054               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:24:31.950649 5054           Options.merge_operator: None
2025/07/26-18:24:31.950650 5054        Options.compaction_filter: None
2025/07/26-18:24:31.950653 5054        Options.compaction_filter_factory: None
2025/07/26-18:24:31.950654 5054  Options.sst_partitioner_factory: None
2025/07/26-18:24:31.950656 5054         Options.memtable_factory: SkipListFactory
2025/07/26-18:24:31.950658 5054            Options.table_factory: BlockBasedTable
2025/07/26-18:24:31.950693 5054            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000023DB96C0E80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000023DB96EFBC0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:24:31.950695 5054        Options.write_buffer_size: 67108864
2025/07/26-18:24:31.950697 5054  Options.max_write_buffer_number: 2
2025/07/26-18:24:31.950699 5054          Options.compression: Snappy
2025/07/26-18:24:31.950701 5054                  Options.bottommost_compression: Disabled
2025/07/26-18:24:31.950703 5054       Options.prefix_extractor: nullptr
2025/07/26-18:24:31.950704 5054   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:24:31.950706 5054             Options.num_levels: 7
2025/07/26-18:24:31.950707 5054        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:24:31.950709 5054     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:24:31.950711 5054     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:24:31.950713 5054            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:24:31.950715 5054                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:24:31.950717 5054               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:24:31.950718 5054         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:24:31.950720 5054         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:31.950722 5054         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:24:31.950724 5054                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:24:31.950726 5054         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:31.950727 5054         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:31.950729 5054            Options.compression_opts.window_bits: -14
2025/07/26-18:24:31.950737 5054                  Options.compression_opts.level: 32767
2025/07/26-18:24:31.950740 5054               Options.compression_opts.strategy: 0
2025/07/26-18:24:31.950741 5054         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:24:31.950743 5054         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:31.950745 5054         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:31.950746 5054         Options.compression_opts.parallel_threads: 1
2025/07/26-18:24:31.950748 5054                  Options.compression_opts.enabled: false
2025/07/26-18:24:31.950750 5054         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:31.950752 5054      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:24:31.950753 5054          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:24:31.950755 5054              Options.level0_stop_writes_trigger: 36
2025/07/26-18:24:31.950757 5054                   Options.target_file_size_base: 67108864
2025/07/26-18:24:31.950758 5054             Options.target_file_size_multiplier: 1
2025/07/26-18:24:31.950761 5054                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:24:31.950762 5054 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:24:31.950764 5054          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:24:31.950766 5054 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:24:31.950768 5054 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:24:31.950770 5054 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:24:31.950771 5054 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:24:31.950773 5054 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:24:31.950775 5054 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:24:31.950776 5054 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:24:31.950778 5054       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:24:31.950780 5054                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:24:31.950781 5054   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:24:31.950783 5054                        Options.arena_block_size: 1048576
2025/07/26-18:24:31.950785 5054   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:24:31.950787 5054   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:24:31.950788 5054                Options.disable_auto_compactions: 0
2025/07/26-18:24:31.950791 5054                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:24:31.950793 5054                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:24:31.950795 5054 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:24:31.950796 5054 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:24:31.950798 5054 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:24:31.950800 5054 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:24:31.950802 5054 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:24:31.950804 5054 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:24:31.950805 5054 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:24:31.950807 5054 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:24:31.950810 5054                   Options.table_properties_collectors: 
2025/07/26-18:24:31.950811 5054                   Options.inplace_update_support: 0
2025/07/26-18:24:31.950813 5054                 Options.inplace_update_num_locks: 10000
2025/07/26-18:24:31.950815 5054               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:24:31.950817 5054               Options.memtable_whole_key_filtering: 0
2025/07/26-18:24:31.950818 5054   Options.memtable_huge_page_size: 0
2025/07/26-18:24:31.950849 5054                           Options.bloom_locality: 0
2025/07/26-18:24:31.950851 5054                    Options.max_successive_merges: 0
2025/07/26-18:24:31.950853 5054                Options.optimize_filters_for_hits: 0
2025/07/26-18:24:31.950854 5054                Options.paranoid_file_checks: 0
2025/07/26-18:24:31.950856 5054                Options.force_consistency_checks: 1
2025/07/26-18:24:31.950858 5054                Options.report_bg_io_stats: 0
2025/07/26-18:24:31.950859 5054                               Options.ttl: 2592000
2025/07/26-18:24:31.950861 5054          Options.periodic_compaction_seconds: 0
2025/07/26-18:24:31.950863 5054                        Options.default_temperature: kUnknown
2025/07/26-18:24:31.950865 5054  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:24:31.950866 5054    Options.preserve_internal_time_seconds: 0
2025/07/26-18:24:31.950868 5054                       Options.enable_blob_files: false
2025/07/26-18:24:31.950870 5054                           Options.min_blob_size: 0
2025/07/26-18:24:31.950871 5054                          Options.blob_file_size: 268435456
2025/07/26-18:24:31.950873 5054                   Options.blob_compression_type: NoCompression
2025/07/26-18:24:31.950875 5054          Options.enable_blob_garbage_collection: false
2025/07/26-18:24:31.950877 5054      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:24:31.950879 5054 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:24:31.950880 5054          Options.blob_compaction_readahead_size: 0
2025/07/26-18:24:31.950882 5054                Options.blob_file_starting_level: 0
2025/07/26-18:24:31.950884 5054         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:24:31.950886 5054            Options.memtable_max_range_deletions: 0
2025/07/26-18:24:31.951742 5054 [db/version_set.cc:5993] Recovered from manifest file:./data/12D3KooWLS87bf1bkT41Ar9dqQHCnVtJrg669t1Tngdz9ejnCrPp/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/07/26-18:24:31.951751 5054 [db/version_set.cc:6002] Column family [default] (ID 0), log number is 0
2025/07/26-18:24:31.951994 5054 [db/db_impl/db_impl_open.cc:646] DB ID: b77e5c16-6a0a-11f0-98b6-d4e98a1a402d
2025/07/26-18:24:31.952965 5054 [db/version_set.cc:5439] Creating manifest 5
2025/07/26-18:24:31.963605 5054 [db/column_family.cc:618] --------------- Options for column family [tasks]:
2025/07/26-18:24:31.963616 5054               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:24:31.963618 5054           Options.merge_operator: None
2025/07/26-18:24:31.963620 5054        Options.compaction_filter: None
2025/07/26-18:24:31.963622 5054        Options.compaction_filter_factory: None
2025/07/26-18:24:31.963624 5054  Options.sst_partitioner_factory: None
2025/07/26-18:24:31.963625 5054         Options.memtable_factory: SkipListFactory
2025/07/26-18:24:31.963627 5054            Options.table_factory: BlockBasedTable
2025/07/26-18:24:31.963653 5054            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000023DB96C7DE0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000023DB96EF710
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:24:31.963658 5054        Options.write_buffer_size: 67108864
2025/07/26-18:24:31.963663 5054  Options.max_write_buffer_number: 2
2025/07/26-18:24:31.963665 5054          Options.compression: Snappy
2025/07/26-18:24:31.963667 5054                  Options.bottommost_compression: Disabled
2025/07/26-18:24:31.963669 5054       Options.prefix_extractor: nullptr
2025/07/26-18:24:31.963670 5054   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:24:31.963672 5054             Options.num_levels: 7
2025/07/26-18:24:31.963674 5054        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:24:31.963675 5054     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:24:31.963677 5054     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:24:31.963679 5054            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:24:31.963680 5054                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:24:31.963682 5054               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:24:31.963684 5054         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:24:31.963686 5054         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:31.963687 5054         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:24:31.963689 5054                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:24:31.963691 5054         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:31.963693 5054         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:31.963694 5054            Options.compression_opts.window_bits: -14
2025/07/26-18:24:31.963696 5054                  Options.compression_opts.level: 32767
2025/07/26-18:24:31.963698 5054               Options.compression_opts.strategy: 0
2025/07/26-18:24:31.963699 5054         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:24:31.963701 5054         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:31.963703 5054         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:31.963704 5054         Options.compression_opts.parallel_threads: 1
2025/07/26-18:24:31.963706 5054                  Options.compression_opts.enabled: false
2025/07/26-18:24:31.963708 5054         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:31.963709 5054      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:24:31.963711 5054          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:24:31.963713 5054              Options.level0_stop_writes_trigger: 36
2025/07/26-18:24:31.963714 5054                   Options.target_file_size_base: 67108864
2025/07/26-18:24:31.963716 5054             Options.target_file_size_multiplier: 1
2025/07/26-18:24:31.963718 5054                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:24:31.963719 5054 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:24:31.963721 5054          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:24:31.963728 5054 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:24:31.963730 5054 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:24:31.963732 5054 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:24:31.963733 5054 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:24:31.963735 5054 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:24:31.963736 5054 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:24:31.963738 5054 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:24:31.963740 5054       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:24:31.963741 5054                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:24:31.963744 5054   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:24:31.963746 5054                        Options.arena_block_size: 1048576
2025/07/26-18:24:31.963747 5054   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:24:31.963749 5054   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:24:31.963751 5054                Options.disable_auto_compactions: 0
2025/07/26-18:24:31.963754 5054                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:24:31.963756 5054                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:24:31.963758 5054 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:24:31.963759 5054 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:24:31.963761 5054 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:24:31.963763 5054 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:24:31.963764 5054 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:24:31.963766 5054 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:24:31.963769 5054 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:24:31.963770 5054 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:24:31.963774 5054                   Options.table_properties_collectors: 
2025/07/26-18:24:31.963776 5054                   Options.inplace_update_support: 0
2025/07/26-18:24:31.963778 5054                 Options.inplace_update_num_locks: 10000
2025/07/26-18:24:31.963780 5054               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:24:31.963781 5054               Options.memtable_whole_key_filtering: 0
2025/07/26-18:24:31.963783 5054   Options.memtable_huge_page_size: 0
2025/07/26-18:24:31.963785 5054                           Options.bloom_locality: 0
2025/07/26-18:24:31.963787 5054                    Options.max_successive_merges: 0
2025/07/26-18:24:31.963788 5054                Options.optimize_filters_for_hits: 0
2025/07/26-18:24:31.963790 5054                Options.paranoid_file_checks: 0
2025/07/26-18:24:31.963791 5054                Options.force_consistency_checks: 1
2025/07/26-18:24:31.963793 5054                Options.report_bg_io_stats: 0
2025/07/26-18:24:31.963795 5054                               Options.ttl: 2592000
2025/07/26-18:24:31.963796 5054          Options.periodic_compaction_seconds: 0
2025/07/26-18:24:31.963798 5054                        Options.default_temperature: kUnknown
2025/07/26-18:24:31.963800 5054  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:24:31.963802 5054    Options.preserve_internal_time_seconds: 0
2025/07/26-18:24:31.963803 5054                       Options.enable_blob_files: false
2025/07/26-18:24:31.963805 5054                           Options.min_blob_size: 0
2025/07/26-18:24:31.963807 5054                          Options.blob_file_size: 268435456
2025/07/26-18:24:31.963808 5054                   Options.blob_compression_type: NoCompression
2025/07/26-18:24:31.963810 5054          Options.enable_blob_garbage_collection: false
2025/07/26-18:24:31.963812 5054      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:24:31.963814 5054 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:24:31.963816 5054          Options.blob_compaction_readahead_size: 0
2025/07/26-18:24:31.963817 5054                Options.blob_file_starting_level: 0
2025/07/26-18:24:31.963819 5054         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:24:31.963821 5054            Options.memtable_max_range_deletions: 0
2025/07/26-18:24:31.963980 5054 [db/db_impl/db_impl.cc:3637] Created column family [tasks] (ID 1)
2025/07/26-18:24:31.966687 5054 [db/column_family.cc:618] --------------- Options for column family [raft_state]:
2025/07/26-18:24:31.966706 5054               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:24:31.966711 5054           Options.merge_operator: None
2025/07/26-18:24:31.966716 5054        Options.compaction_filter: None
2025/07/26-18:24:31.966718 5054        Options.compaction_filter_factory: None
2025/07/26-18:24:31.966719 5054  Options.sst_partitioner_factory: None
2025/07/26-18:24:31.966721 5054         Options.memtable_factory: SkipListFactory
2025/07/26-18:24:31.966723 5054            Options.table_factory: BlockBasedTable
2025/07/26-18:24:31.966744 5054            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000023DB96C80B0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000023DB96EF260
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:24:31.966747 5054        Options.write_buffer_size: 67108864
2025/07/26-18:24:31.966749 5054  Options.max_write_buffer_number: 2
2025/07/26-18:24:31.966750 5054          Options.compression: Snappy
2025/07/26-18:24:31.966752 5054                  Options.bottommost_compression: Disabled
2025/07/26-18:24:31.966754 5054       Options.prefix_extractor: nullptr
2025/07/26-18:24:31.966756 5054   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:24:31.966757 5054             Options.num_levels: 7
2025/07/26-18:24:31.966759 5054        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:24:31.966761 5054     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:24:31.966762 5054     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:24:31.966764 5054            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:24:31.966766 5054                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:24:31.966768 5054               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:24:31.966769 5054         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:24:31.966771 5054         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:31.966773 5054         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:24:31.966775 5054                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:24:31.966777 5054         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:31.966778 5054         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:31.966780 5054            Options.compression_opts.window_bits: -14
2025/07/26-18:24:31.966782 5054                  Options.compression_opts.level: 32767
2025/07/26-18:24:31.966783 5054               Options.compression_opts.strategy: 0
2025/07/26-18:24:31.966785 5054         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:24:31.966787 5054         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:31.966788 5054         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:31.966790 5054         Options.compression_opts.parallel_threads: 1
2025/07/26-18:24:31.966792 5054                  Options.compression_opts.enabled: false
2025/07/26-18:24:31.966832 5054         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:31.966834 5054      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:24:31.966836 5054          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:24:31.966838 5054              Options.level0_stop_writes_trigger: 36
2025/07/26-18:24:31.966840 5054                   Options.target_file_size_base: 67108864
2025/07/26-18:24:31.966842 5054             Options.target_file_size_multiplier: 1
2025/07/26-18:24:31.966843 5054                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:24:31.966845 5054 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:24:31.966847 5054          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:24:31.966849 5054 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:24:31.966851 5054 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:24:31.966852 5054 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:24:31.966854 5054 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:24:31.966856 5054 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:24:31.966857 5054 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:24:31.966859 5054 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:24:31.966861 5054       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:24:31.966862 5054                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:24:31.966864 5054   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:24:31.966866 5054                        Options.arena_block_size: 1048576
2025/07/26-18:24:31.966868 5054   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:24:31.966869 5054   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:24:31.966871 5054                Options.disable_auto_compactions: 0
2025/07/26-18:24:31.966874 5054                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:24:31.966876 5054                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:24:31.966878 5054 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:24:31.966879 5054 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:24:31.966881 5054 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:24:31.966883 5054 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:24:31.966885 5054 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:24:31.966887 5054 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:24:31.966889 5054 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:24:31.966890 5054 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:24:31.966896 5054                   Options.table_properties_collectors: 
2025/07/26-18:24:31.966898 5054                   Options.inplace_update_support: 0
2025/07/26-18:24:31.966900 5054                 Options.inplace_update_num_locks: 10000
2025/07/26-18:24:31.966902 5054               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:24:31.966904 5054               Options.memtable_whole_key_filtering: 0
2025/07/26-18:24:31.966905 5054   Options.memtable_huge_page_size: 0
2025/07/26-18:24:31.966907 5054                           Options.bloom_locality: 0
2025/07/26-18:24:31.966909 5054                    Options.max_successive_merges: 0
2025/07/26-18:24:31.966910 5054                Options.optimize_filters_for_hits: 0
2025/07/26-18:24:31.966912 5054                Options.paranoid_file_checks: 0
2025/07/26-18:24:31.966914 5054                Options.force_consistency_checks: 1
2025/07/26-18:24:31.966915 5054                Options.report_bg_io_stats: 0
2025/07/26-18:24:31.966917 5054                               Options.ttl: 2592000
2025/07/26-18:24:31.966919 5054          Options.periodic_compaction_seconds: 0
2025/07/26-18:24:31.966921 5054                        Options.default_temperature: kUnknown
2025/07/26-18:24:31.966924 5054  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:24:31.966925 5054    Options.preserve_internal_time_seconds: 0
2025/07/26-18:24:31.966927 5054                       Options.enable_blob_files: false
2025/07/26-18:24:31.966929 5054                           Options.min_blob_size: 0
2025/07/26-18:24:31.966931 5054                          Options.blob_file_size: 268435456
2025/07/26-18:24:31.966932 5054                   Options.blob_compression_type: NoCompression
2025/07/26-18:24:31.966934 5054          Options.enable_blob_garbage_collection: false
2025/07/26-18:24:31.966936 5054      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:24:31.966938 5054 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:24:31.966940 5054          Options.blob_compaction_readahead_size: 0
2025/07/26-18:24:31.966941 5054                Options.blob_file_starting_level: 0
2025/07/26-18:24:31.966943 5054         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:24:31.966945 5054            Options.memtable_max_range_deletions: 0
2025/07/26-18:24:31.967142 5054 [db/db_impl/db_impl.cc:3637] Created column family [raft_state] (ID 2)
2025/07/26-18:24:31.969690 5054 [db/column_family.cc:618] --------------- Options for column family [task_versions]:
2025/07/26-18:24:31.969710 5054               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:24:31.969712 5054           Options.merge_operator: None
2025/07/26-18:24:31.969714 5054        Options.compaction_filter: None
2025/07/26-18:24:31.969716 5054        Options.compaction_filter_factory: None
2025/07/26-18:24:31.969717 5054  Options.sst_partitioner_factory: None
2025/07/26-18:24:31.969719 5054         Options.memtable_factory: SkipListFactory
2025/07/26-18:24:31.969721 5054            Options.table_factory: BlockBasedTable
2025/07/26-18:24:31.969741 5054            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000023DB96C8C50)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000023DB96EF8F0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:24:31.969744 5054        Options.write_buffer_size: 67108864
2025/07/26-18:24:31.969746 5054  Options.max_write_buffer_number: 2
2025/07/26-18:24:31.969748 5054          Options.compression: Snappy
2025/07/26-18:24:31.969749 5054                  Options.bottommost_compression: Disabled
2025/07/26-18:24:31.969751 5054       Options.prefix_extractor: nullptr
2025/07/26-18:24:31.969753 5054   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:24:31.969754 5054             Options.num_levels: 7
2025/07/26-18:24:31.969756 5054        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:24:31.969758 5054     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:24:31.969759 5054     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:24:31.969761 5054            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:24:31.969765 5054                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:24:31.969769 5054               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:24:31.969771 5054         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:24:31.969772 5054         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:31.969774 5054         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:24:31.969776 5054                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:24:31.969778 5054         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:31.969779 5054         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:31.969781 5054            Options.compression_opts.window_bits: -14
2025/07/26-18:24:31.969783 5054                  Options.compression_opts.level: 32767
2025/07/26-18:24:31.969784 5054               Options.compression_opts.strategy: 0
2025/07/26-18:24:31.969786 5054         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:24:31.969788 5054         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:31.969789 5054         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:31.969791 5054         Options.compression_opts.parallel_threads: 1
2025/07/26-18:24:31.969793 5054                  Options.compression_opts.enabled: false
2025/07/26-18:24:31.969794 5054         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:31.969796 5054      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:24:31.969798 5054          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:24:31.969800 5054              Options.level0_stop_writes_trigger: 36
2025/07/26-18:24:31.969801 5054                   Options.target_file_size_base: 67108864
2025/07/26-18:24:31.969803 5054             Options.target_file_size_multiplier: 1
2025/07/26-18:24:31.969805 5054                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:24:31.969806 5054 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:24:31.969808 5054          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:24:31.969810 5054 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:24:31.969812 5054 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:24:31.969814 5054 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:24:31.969815 5054 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:24:31.969817 5054 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:24:31.969819 5054 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:24:31.969820 5054 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:24:31.969822 5054       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:24:31.969824 5054                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:24:31.969825 5054   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:24:31.969827 5054                        Options.arena_block_size: 1048576
2025/07/26-18:24:31.969829 5054   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:24:31.969830 5054   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:24:31.969832 5054                Options.disable_auto_compactions: 0
2025/07/26-18:24:31.969835 5054                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:24:31.969837 5054                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:24:31.969839 5054 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:24:31.969841 5054 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:24:31.969842 5054 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:24:31.969844 5054 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:24:31.969846 5054 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:24:31.969848 5054 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:24:31.969850 5054 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:24:31.969852 5054 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:24:31.969857 5054                   Options.table_properties_collectors: 
2025/07/26-18:24:31.969859 5054                   Options.inplace_update_support: 0
2025/07/26-18:24:31.969861 5054                 Options.inplace_update_num_locks: 10000
2025/07/26-18:24:31.969863 5054               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:24:31.969865 5054               Options.memtable_whole_key_filtering: 0
2025/07/26-18:24:31.969867 5054   Options.memtable_huge_page_size: 0
2025/07/26-18:24:31.969868 5054                           Options.bloom_locality: 0
2025/07/26-18:24:31.969870 5054                    Options.max_successive_merges: 0
2025/07/26-18:24:31.969872 5054                Options.optimize_filters_for_hits: 0
2025/07/26-18:24:31.969873 5054                Options.paranoid_file_checks: 0
2025/07/26-18:24:31.969875 5054                Options.force_consistency_checks: 1
2025/07/26-18:24:31.969877 5054                Options.report_bg_io_stats: 0
2025/07/26-18:24:31.969878 5054                               Options.ttl: 2592000
2025/07/26-18:24:31.969880 5054          Options.periodic_compaction_seconds: 0
2025/07/26-18:24:31.969882 5054                        Options.default_temperature: kUnknown
2025/07/26-18:24:31.969884 5054  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:24:31.969885 5054    Options.preserve_internal_time_seconds: 0
2025/07/26-18:24:31.969887 5054                       Options.enable_blob_files: false
2025/07/26-18:24:31.969889 5054                           Options.min_blob_size: 0
2025/07/26-18:24:31.969890 5054                          Options.blob_file_size: 268435456
2025/07/26-18:24:31.969892 5054                   Options.blob_compression_type: NoCompression
2025/07/26-18:24:31.969894 5054          Options.enable_blob_garbage_collection: false
2025/07/26-18:24:31.969895 5054      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:24:31.969897 5054 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:24:31.969899 5054          Options.blob_compaction_readahead_size: 0
2025/07/26-18:24:31.969901 5054                Options.blob_file_starting_level: 0
2025/07/26-18:24:31.969903 5054         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:24:31.969904 5054            Options.memtable_max_range_deletions: 0
2025/07/26-18:24:31.970089 5054 [db/db_impl/db_impl.cc:3637] Created column family [task_versions] (ID 3)
2025/07/26-18:24:31.972615 5054 [db/column_family.cc:618] --------------- Options for column family [task_index]:
2025/07/26-18:24:31.972626 5054               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:24:31.972628 5054           Options.merge_operator: None
2025/07/26-18:24:31.972629 5054        Options.compaction_filter: None
2025/07/26-18:24:31.972631 5054        Options.compaction_filter_factory: None
2025/07/26-18:24:31.972633 5054  Options.sst_partitioner_factory: None
2025/07/26-18:24:31.972634 5054         Options.memtable_factory: SkipListFactory
2025/07/26-18:24:31.972636 5054            Options.table_factory: BlockBasedTable
2025/07/26-18:24:31.972656 5054            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000023DB96C8EF0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000023DB96EF350
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:24:31.972661 5054        Options.write_buffer_size: 67108864
2025/07/26-18:24:31.972664 5054  Options.max_write_buffer_number: 2
2025/07/26-18:24:31.972665 5054          Options.compression: Snappy
2025/07/26-18:24:31.972667 5054                  Options.bottommost_compression: Disabled
2025/07/26-18:24:31.972669 5054       Options.prefix_extractor: nullptr
2025/07/26-18:24:31.972671 5054   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:24:31.972672 5054             Options.num_levels: 7
2025/07/26-18:24:31.972674 5054        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:24:31.972676 5054     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:24:31.972678 5054     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:24:31.972679 5054            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:24:31.972681 5054                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:24:31.972683 5054               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:24:31.972684 5054         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:24:31.972686 5054         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:31.972688 5054         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:24:31.972690 5054                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:24:31.972691 5054         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:31.972693 5054         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:31.972695 5054            Options.compression_opts.window_bits: -14
2025/07/26-18:24:31.972697 5054                  Options.compression_opts.level: 32767
2025/07/26-18:24:31.972698 5054               Options.compression_opts.strategy: 0
2025/07/26-18:24:31.972700 5054         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:24:31.972702 5054         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:24:31.972703 5054         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:24:31.972705 5054         Options.compression_opts.parallel_threads: 1
2025/07/26-18:24:31.972707 5054                  Options.compression_opts.enabled: false
2025/07/26-18:24:31.972709 5054         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:24:31.972710 5054      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:24:31.972712 5054          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:24:31.972714 5054              Options.level0_stop_writes_trigger: 36
2025/07/26-18:24:31.972715 5054                   Options.target_file_size_base: 67108864
2025/07/26-18:24:31.972717 5054             Options.target_file_size_multiplier: 1
2025/07/26-18:24:31.972719 5054                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:24:31.972720 5054 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:24:31.972722 5054          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:24:31.972724 5054 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:24:31.972726 5054 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:24:31.972728 5054 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:24:31.972729 5054 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:24:31.972732 5054 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:24:31.972734 5054 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:24:31.972736 5054 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:24:31.972737 5054       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:24:31.972739 5054                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:24:31.972741 5054   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:24:31.972742 5054                        Options.arena_block_size: 1048576
2025/07/26-18:24:31.972744 5054   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:24:31.972746 5054   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:24:31.972748 5054                Options.disable_auto_compactions: 0
2025/07/26-18:24:31.972750 5054                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:24:31.972752 5054                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:24:31.972754 5054 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:24:31.972756 5054 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:24:31.972757 5054 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:24:31.972759 5054 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:24:31.972761 5054 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:24:31.972763 5054 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:24:31.972764 5054 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:24:31.972766 5054 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:24:31.972770 5054                   Options.table_properties_collectors: 
2025/07/26-18:24:31.972772 5054                   Options.inplace_update_support: 0
2025/07/26-18:24:31.972774 5054                 Options.inplace_update_num_locks: 10000
2025/07/26-18:24:31.972776 5054               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:24:31.972778 5054               Options.memtable_whole_key_filtering: 0
2025/07/26-18:24:31.972779 5054   Options.memtable_huge_page_size: 0
2025/07/26-18:24:31.972781 5054                           Options.bloom_locality: 0
2025/07/26-18:24:31.972783 5054                    Options.max_successive_merges: 0
2025/07/26-18:24:31.972784 5054                Options.optimize_filters_for_hits: 0
2025/07/26-18:24:31.972786 5054                Options.paranoid_file_checks: 0
2025/07/26-18:24:31.972788 5054                Options.force_consistency_checks: 1
2025/07/26-18:24:31.972789 5054                Options.report_bg_io_stats: 0
2025/07/26-18:24:31.972791 5054                               Options.ttl: 2592000
2025/07/26-18:24:31.972793 5054          Options.periodic_compaction_seconds: 0
2025/07/26-18:24:31.972794 5054                        Options.default_temperature: kUnknown
2025/07/26-18:24:31.972796 5054  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:24:31.972798 5054    Options.preserve_internal_time_seconds: 0
2025/07/26-18:24:31.972800 5054                       Options.enable_blob_files: false
2025/07/26-18:24:31.972801 5054                           Options.min_blob_size: 0
2025/07/26-18:24:31.972803 5054                          Options.blob_file_size: 268435456
2025/07/26-18:24:31.972805 5054                   Options.blob_compression_type: NoCompression
2025/07/26-18:24:31.972806 5054          Options.enable_blob_garbage_collection: false
2025/07/26-18:24:31.972808 5054      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:24:31.972810 5054 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:24:31.972812 5054          Options.blob_compaction_readahead_size: 0
2025/07/26-18:24:31.972814 5054                Options.blob_file_starting_level: 0
2025/07/26-18:24:31.972815 5054         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:24:31.972817 5054            Options.memtable_max_range_deletions: 0
2025/07/26-18:24:31.972978 5054 [db/db_impl/db_impl.cc:3637] Created column family [task_index] (ID 4)
2025/07/26-18:24:31.979279 5054 [db/db_impl/db_impl_open.cc:2157] SstFileManager instance 0000023DB974E900
2025/07/26-18:24:31.979480 5054 DB pointer 0000023DBB2940C0
2025/07/26-18:24:31.979949 c38 [db/db_impl/db_impl.cc:1140] ------- DUMPING STATS -------
2025/07/26-18:24:31.979958 c38 [db/db_impl/db_impl.cc:1141] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000023DB96EFBC0#2416 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 4e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** Compaction Stats [tasks] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [tasks] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000023DB96EF710#2416 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [tasks] **

** Compaction Stats [raft_state] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [raft_state] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000023DB96EF260#2416 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2.3e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [raft_state] **

** Compaction Stats [task_versions] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_versions] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000023DB96EF8F0#2416 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_versions] **

** Compaction Stats [task_index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000023DB96EF350#2416 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 1.8e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_index] **
