use rkyv::{Archive, Deserialize as RkyvDeserialize, Serialize as RkyvSerialize};
use std::collections::HashMap;
use crate::{
    raft::{AppendEntries, VoteRequest, VoteResponse, PrepareMessage, PromiseMessage, AcceptMessage, AcceptedMessage},
    types::{Task, DeviceInfo}
};

#[derive(Debug, Clone, Archive, RkyvDeserialize, RkyvSerialize)]
pub enum QueueMessage {
    // Task messages
    NewTask(Task),
    UpdateTask { task: Task },
    DeleteTask { task_id: [u8; 16], version: u64 },
    AssignTask { task_id: [u8; 16], worker: Vec<u8>, version: u64 },
    CompleteTask { task_id: [u8; 16], worker: Vec<u8>, version: u64 },
    FailTask { task_id: [u8; 16], worker: Vec<u8>, reason: String, version: u64 },
    RequestTask { worker: Vec<u8> },

    // Multi-Paxos messages
    Prepare(PrepareMessage),
    Promise(PromiseMessage),
    Accept(AcceptMessage),
    Accepted(AcceptedMessage),

    // Legacy Raft messages (for backward compatibility)
    VoteRequest(VoteRequest),
    VoteResponse(VoteResponse),
    AppendEntries(AppendEntries),
    AppendEntriesResponse { term: u64, success: bool, follower_id: Vec<u8> },

    // Sync messages
    Heartbeat { leader_id: Vec<u8>, term: u64 },
    PaxosHeartbeat { proposer_id: Vec<u8>, ballot: u64 },
    QueryTaskStatus { task_id: [u8; 16] },
    TaskStatusResponse { task: Task },
    SyncRequest { from: Vec<u8>, request_id: [u8; 16] },
    SyncResponse { tasks: Vec<Task>, request_id: [u8; 16] },
    TaskStatusBroadcast { task: Task },
    HashRequest { from: Vec<u8> },
    HashResponse { from: Vec<u8>, task_hashes: HashMap<[u8; 16], u64> },

    // 设备信息消息
    DeviceInfoRequest { from: Vec<u8> },
    DeviceInfoResponse { peer_id: Vec<u8>, device_info: DeviceInfo },
}

impl QueueMessage {
    pub fn serialize(&self) -> Result<Vec<u8>, rkyv::rancor::Error> {
        rkyv::to_bytes(self).map(|aligned_vec| aligned_vec.into_vec())
    }
    
    pub fn deserialize(data: &[u8]) -> Result<QueueMessage, Box<dyn std::error::Error>> {
        let archived = rkyv::from_bytes::<QueueMessage, rkyv::rancor::Error>(data)?;
        Ok(archived)
    }
}

