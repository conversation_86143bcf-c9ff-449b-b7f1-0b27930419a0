2025/07/26-17:57:39.420650 5048 RocksDB version: 8.10.0
2025/07/26-17:57:39.421149 5048 Compile date 2023-12-15 13:01:14
2025/07/26-17:57:39.421197 5048 DB SUMMARY
2025/07/26-17:57:39.421215 5048 Host name (Env):  DESKTOP-RNBFHG8
2025/07/26-17:57:39.421221 5048 DB Session ID:  KYBGOZ1AX9A0L8F0S3R9
2025/07/26-17:57:39.421363 5048 SST files in ./data/12D3KooWNuUDuxPw8LMSR79jkAkZtSaq4od4T2ze5bnrkR3Z2Mdo dir, Total Num: 0, files: 
2025/07/26-17:57:39.421371 5048 Write Ahead Log file in ./data/12D3KooWNuUDuxPw8LMSR79jkAkZtSaq4od4T2ze5bnrkR3Z2Mdo: 
2025/07/26-17:57:39.421376 5048                         Options.error_if_exists: 0
2025/07/26-17:57:39.421381 5048                       Options.create_if_missing: 1
2025/07/26-17:57:39.421386 5048                         Options.paranoid_checks: 1
2025/07/26-17:57:39.431341 5048             Options.flush_verify_memtable_count: 1
2025/07/26-17:57:39.431367 5048          Options.compaction_verify_record_count: 1
2025/07/26-17:57:39.431369 5048                               Options.track_and_verify_wals_in_manifest: 0
2025/07/26-17:57:39.431371 5048        Options.verify_sst_unique_id_in_manifest: 1
2025/07/26-17:57:39.431389 5048                                     Options.env: 00000285585B52F0
2025/07/26-17:57:39.431394 5048                                      Options.fs: WinFS
2025/07/26-17:57:39.431397 5048                                Options.info_log: 000002855851E160
2025/07/26-17:57:39.431399 5048                Options.max_file_opening_threads: 16
2025/07/26-17:57:39.431400 5048                              Options.statistics: 0000000000000000
2025/07/26-17:57:39.431402 5048                               Options.use_fsync: 0
2025/07/26-17:57:39.431404 5048                       Options.max_log_file_size: 0
2025/07/26-17:57:39.431406 5048                  Options.max_manifest_file_size: 1073741824
2025/07/26-17:57:39.431408 5048                   Options.log_file_time_to_roll: 0
2025/07/26-17:57:39.431410 5048                       Options.keep_log_file_num: 1000
2025/07/26-17:57:39.431411 5048                    Options.recycle_log_file_num: 0
2025/07/26-17:57:39.431413 5048                         Options.allow_fallocate: 1
2025/07/26-17:57:39.431415 5048                        Options.allow_mmap_reads: 0
2025/07/26-17:57:39.431417 5048                       Options.allow_mmap_writes: 0
2025/07/26-17:57:39.431419 5048                        Options.use_direct_reads: 0
2025/07/26-17:57:39.431420 5048                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/26-17:57:39.431422 5048          Options.create_missing_column_families: 1
2025/07/26-17:57:39.431424 5048                              Options.db_log_dir: 
2025/07/26-17:57:39.431426 5048                                 Options.wal_dir: 
2025/07/26-17:57:39.431427 5048                Options.table_cache_numshardbits: 6
2025/07/26-17:57:39.431429 5048                         Options.WAL_ttl_seconds: 0
2025/07/26-17:57:39.431431 5048                       Options.WAL_size_limit_MB: 0
2025/07/26-17:57:39.431433 5048                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/26-17:57:39.431435 5048             Options.manifest_preallocation_size: 4194304
2025/07/26-17:57:39.431436 5048                     Options.is_fd_close_on_exec: 1
2025/07/26-17:57:39.431438 5048                   Options.advise_random_on_open: 1
2025/07/26-17:57:39.431440 5048                    Options.db_write_buffer_size: 0
2025/07/26-17:57:39.431442 5048                    Options.write_buffer_manager: 00000285585B55C0
2025/07/26-17:57:39.431444 5048         Options.access_hint_on_compaction_start: 1
2025/07/26-17:57:39.431445 5048           Options.random_access_max_buffer_size: 1048576
2025/07/26-17:57:39.431447 5048                      Options.use_adaptive_mutex: 0
2025/07/26-17:57:39.431449 5048                            Options.rate_limiter: 0000000000000000
2025/07/26-17:57:39.431452 5048     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/26-17:57:39.431453 5048                       Options.wal_recovery_mode: 2
2025/07/26-17:57:39.431507 5048                  Options.enable_thread_tracking: 0
2025/07/26-17:57:39.431514 5048                  Options.enable_pipelined_write: 0
2025/07/26-17:57:39.431516 5048                  Options.unordered_write: 0
2025/07/26-17:57:39.431518 5048         Options.allow_concurrent_memtable_write: 1
2025/07/26-17:57:39.431520 5048      Options.enable_write_thread_adaptive_yield: 1
2025/07/26-17:57:39.431522 5048             Options.write_thread_max_yield_usec: 100
2025/07/26-17:57:39.431524 5048            Options.write_thread_slow_yield_usec: 3
2025/07/26-17:57:39.431526 5048                               Options.row_cache: None
2025/07/26-17:57:39.431528 5048                              Options.wal_filter: None
2025/07/26-17:57:39.431530 5048             Options.avoid_flush_during_recovery: 0
2025/07/26-17:57:39.431531 5048             Options.allow_ingest_behind: 0
2025/07/26-17:57:39.431533 5048             Options.two_write_queues: 0
2025/07/26-17:57:39.431535 5048             Options.manual_wal_flush: 0
2025/07/26-17:57:39.431537 5048             Options.wal_compression: 0
2025/07/26-17:57:39.431538 5048             Options.atomic_flush: 0
2025/07/26-17:57:39.431540 5048             Options.avoid_unnecessary_blocking_io: 0
2025/07/26-17:57:39.431542 5048                 Options.persist_stats_to_disk: 0
2025/07/26-17:57:39.431544 5048                 Options.write_dbid_to_manifest: 0
2025/07/26-17:57:39.431545 5048                 Options.log_readahead_size: 0
2025/07/26-17:57:39.431547 5048                 Options.file_checksum_gen_factory: Unknown
2025/07/26-17:57:39.431549 5048                 Options.best_efforts_recovery: 0
2025/07/26-17:57:39.431551 5048                Options.max_bgerror_resume_count: 2147483647
2025/07/26-17:57:39.431553 5048            Options.bgerror_resume_retry_interval: 1000000
2025/07/26-17:57:39.431554 5048             Options.allow_data_in_errors: 0
2025/07/26-17:57:39.431556 5048             Options.db_host_id: __hostname__
2025/07/26-17:57:39.431558 5048             Options.enforce_single_del_contracts: true
2025/07/26-17:57:39.431560 5048             Options.max_background_jobs: 4
2025/07/26-17:57:39.431562 5048             Options.max_background_compactions: -1
2025/07/26-17:57:39.431564 5048             Options.max_subcompactions: 1
2025/07/26-17:57:39.431566 5048             Options.avoid_flush_during_shutdown: 0
2025/07/26-17:57:39.431568 5048           Options.writable_file_max_buffer_size: 1048576
2025/07/26-17:57:39.431569 5048             Options.delayed_write_rate : 16777216
2025/07/26-17:57:39.431571 5048             Options.max_total_wal_size: 0
2025/07/26-17:57:39.431573 5048             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/26-17:57:39.431575 5048                   Options.stats_dump_period_sec: 600
2025/07/26-17:57:39.431577 5048                 Options.stats_persist_period_sec: 600
2025/07/26-17:57:39.431578 5048                 Options.stats_history_buffer_size: 1048576
2025/07/26-17:57:39.431580 5048                          Options.max_open_files: 1000
2025/07/26-17:57:39.431582 5048                          Options.bytes_per_sync: 0
2025/07/26-17:57:39.431584 5048                      Options.wal_bytes_per_sync: 0
2025/07/26-17:57:39.431586 5048                   Options.strict_bytes_per_sync: 0
2025/07/26-17:57:39.431587 5048       Options.compaction_readahead_size: 2097152
2025/07/26-17:57:39.431589 5048                  Options.max_background_flushes: -1
2025/07/26-17:57:39.431591 5048 Options.daily_offpeak_time_utc: 
2025/07/26-17:57:39.431593 5048 Compression algorithms supported:
2025/07/26-17:57:39.431596 5048 	kZSTD supported: 1
2025/07/26-17:57:39.431598 5048 	kSnappyCompression supported: 1
2025/07/26-17:57:39.431600 5048 	kBZip2Compression supported: 1
2025/07/26-17:57:39.431602 5048 	kZlibCompression supported: 1
2025/07/26-17:57:39.431604 5048 	kLZ4Compression supported: 1
2025/07/26-17:57:39.431606 5048 	kXpressCompression supported: 0
2025/07/26-17:57:39.431608 5048 	kLZ4HCCompression supported: 1
2025/07/26-17:57:39.431619 5048 	kZSTDNotFinalCompression supported: 1
2025/07/26-17:57:39.431675 5048 Fast CRC32 supported: Not supported on x86
2025/07/26-17:57:39.431677 5048 DMutex implementation: std::mutex
2025/07/26-17:57:39.437442 5048 [db/db_impl/db_impl_open.cc:325] Creating manifest 1 
2025/07/26-17:57:39.446108 5048 [db/version_set.cc:5942] Recovering from manifest file: ./data/12D3KooWNuUDuxPw8LMSR79jkAkZtSaq4od4T2ze5bnrkR3Z2Mdo/MANIFEST-000001
2025/07/26-17:57:39.446225 5048 [db/column_family.cc:618] --------------- Options for column family [default]:
2025/07/26-17:57:39.446229 5048               Options.comparator: leveldb.BytewiseComparator
2025/07/26-17:57:39.446232 5048           Options.merge_operator: None
2025/07/26-17:57:39.446234 5048        Options.compaction_filter: None
2025/07/26-17:57:39.446235 5048        Options.compaction_filter_factory: None
2025/07/26-17:57:39.446237 5048  Options.sst_partitioner_factory: None
2025/07/26-17:57:39.446239 5048         Options.memtable_factory: SkipListFactory
2025/07/26-17:57:39.446241 5048            Options.table_factory: BlockBasedTable
2025/07/26-17:57:39.446268 5048            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000028558582EB0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000285585B5B70
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-17:57:39.446270 5048        Options.write_buffer_size: 67108864
2025/07/26-17:57:39.446272 5048  Options.max_write_buffer_number: 2
2025/07/26-17:57:39.446274 5048          Options.compression: Snappy
2025/07/26-17:57:39.446276 5048                  Options.bottommost_compression: Disabled
2025/07/26-17:57:39.446277 5048       Options.prefix_extractor: nullptr
2025/07/26-17:57:39.446279 5048   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-17:57:39.446281 5048             Options.num_levels: 7
2025/07/26-17:57:39.446283 5048        Options.min_write_buffer_number_to_merge: 1
2025/07/26-17:57:39.446284 5048     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-17:57:39.446286 5048     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-17:57:39.446288 5048            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-17:57:39.446290 5048                  Options.bottommost_compression_opts.level: 32767
2025/07/26-17:57:39.446292 5048               Options.bottommost_compression_opts.strategy: 0
2025/07/26-17:57:39.446293 5048         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-17:57:39.446295 5048         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:39.446297 5048         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-17:57:39.446299 5048                  Options.bottommost_compression_opts.enabled: false
2025/07/26-17:57:39.446301 5048         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:39.446303 5048         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:39.446304 5048            Options.compression_opts.window_bits: -14
2025/07/26-17:57:39.446307 5048                  Options.compression_opts.level: 32767
2025/07/26-17:57:39.446310 5048               Options.compression_opts.strategy: 0
2025/07/26-17:57:39.446312 5048         Options.compression_opts.max_dict_bytes: 0
2025/07/26-17:57:39.446314 5048         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:39.446316 5048         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:39.446317 5048         Options.compression_opts.parallel_threads: 1
2025/07/26-17:57:39.446319 5048                  Options.compression_opts.enabled: false
2025/07/26-17:57:39.446321 5048         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:39.446323 5048      Options.level0_file_num_compaction_trigger: 4
2025/07/26-17:57:39.446324 5048          Options.level0_slowdown_writes_trigger: 20
2025/07/26-17:57:39.446326 5048              Options.level0_stop_writes_trigger: 36
2025/07/26-17:57:39.446328 5048                   Options.target_file_size_base: 67108864
2025/07/26-17:57:39.446330 5048             Options.target_file_size_multiplier: 1
2025/07/26-17:57:39.446331 5048                Options.max_bytes_for_level_base: 268435456
2025/07/26-17:57:39.446333 5048 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-17:57:39.446335 5048          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-17:57:39.446337 5048 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-17:57:39.446339 5048 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-17:57:39.446341 5048 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-17:57:39.446342 5048 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-17:57:39.446344 5048 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-17:57:39.446346 5048 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-17:57:39.446348 5048 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-17:57:39.446350 5048       Options.max_sequential_skip_in_iterations: 8
2025/07/26-17:57:39.446351 5048                    Options.max_compaction_bytes: 1677721600
2025/07/26-17:57:39.446353 5048   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-17:57:39.446355 5048                        Options.arena_block_size: 1048576
2025/07/26-17:57:39.446357 5048   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-17:57:39.446359 5048   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-17:57:39.446360 5048                Options.disable_auto_compactions: 0
2025/07/26-17:57:39.446363 5048                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-17:57:39.446365 5048                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-17:57:39.446367 5048 Options.compaction_options_universal.size_ratio: 1
2025/07/26-17:57:39.446369 5048 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-17:57:39.446370 5048 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-17:57:39.446372 5048 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-17:57:39.446374 5048 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-17:57:39.446376 5048 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-17:57:39.446378 5048 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-17:57:39.446380 5048 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-17:57:39.446383 5048                   Options.table_properties_collectors: 
2025/07/26-17:57:39.446385 5048                   Options.inplace_update_support: 0
2025/07/26-17:57:39.446386 5048                 Options.inplace_update_num_locks: 10000
2025/07/26-17:57:39.446388 5048               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-17:57:39.446390 5048               Options.memtable_whole_key_filtering: 0
2025/07/26-17:57:39.446392 5048   Options.memtable_huge_page_size: 0
2025/07/26-17:57:39.446410 5048                           Options.bloom_locality: 0
2025/07/26-17:57:39.446412 5048                    Options.max_successive_merges: 0
2025/07/26-17:57:39.446414 5048                Options.optimize_filters_for_hits: 0
2025/07/26-17:57:39.446416 5048                Options.paranoid_file_checks: 0
2025/07/26-17:57:39.446418 5048                Options.force_consistency_checks: 1
2025/07/26-17:57:39.446420 5048                Options.report_bg_io_stats: 0
2025/07/26-17:57:39.446421 5048                               Options.ttl: 2592000
2025/07/26-17:57:39.446423 5048          Options.periodic_compaction_seconds: 0
2025/07/26-17:57:39.446425 5048                        Options.default_temperature: kUnknown
2025/07/26-17:57:39.446427 5048  Options.preclude_last_level_data_seconds: 0
2025/07/26-17:57:39.446429 5048    Options.preserve_internal_time_seconds: 0
2025/07/26-17:57:39.446430 5048                       Options.enable_blob_files: false
2025/07/26-17:57:39.446432 5048                           Options.min_blob_size: 0
2025/07/26-17:57:39.446434 5048                          Options.blob_file_size: 268435456
2025/07/26-17:57:39.446436 5048                   Options.blob_compression_type: NoCompression
2025/07/26-17:57:39.446438 5048          Options.enable_blob_garbage_collection: false
2025/07/26-17:57:39.446439 5048      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-17:57:39.446441 5048 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-17:57:39.446443 5048          Options.blob_compaction_readahead_size: 0
2025/07/26-17:57:39.446445 5048                Options.blob_file_starting_level: 0
2025/07/26-17:57:39.446447 5048         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-17:57:39.446449 5048            Options.memtable_max_range_deletions: 0
2025/07/26-17:57:39.447590 5048 [db/version_set.cc:5993] Recovered from manifest file:./data/12D3KooWNuUDuxPw8LMSR79jkAkZtSaq4od4T2ze5bnrkR3Z2Mdo/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/07/26-17:57:39.447605 5048 [db/version_set.cc:6002] Column family [default] (ID 0), log number is 0
2025/07/26-17:57:39.447872 5048 [db/db_impl/db_impl_open.cc:646] DB ID: f65dfe9a-6a06-11f0-98b6-d4e98a1a402d
2025/07/26-17:57:39.448872 5048 [db/version_set.cc:5439] Creating manifest 5
2025/07/26-17:57:39.459620 5048 [db/column_family.cc:618] --------------- Options for column family [tasks]:
2025/07/26-17:57:39.459631 5048               Options.comparator: leveldb.BytewiseComparator
2025/07/26-17:57:39.459633 5048           Options.merge_operator: None
2025/07/26-17:57:39.459635 5048        Options.compaction_filter: None
2025/07/26-17:57:39.459637 5048        Options.compaction_filter_factory: None
2025/07/26-17:57:39.459639 5048  Options.sst_partitioner_factory: None
2025/07/26-17:57:39.459640 5048         Options.memtable_factory: SkipListFactory
2025/07/26-17:57:39.459642 5048            Options.table_factory: BlockBasedTable
2025/07/26-17:57:39.459669 5048            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000002855858A6E0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000285585B5210
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-17:57:39.459674 5048        Options.write_buffer_size: 67108864
2025/07/26-17:57:39.459677 5048  Options.max_write_buffer_number: 2
2025/07/26-17:57:39.459679 5048          Options.compression: Snappy
2025/07/26-17:57:39.459681 5048                  Options.bottommost_compression: Disabled
2025/07/26-17:57:39.459682 5048       Options.prefix_extractor: nullptr
2025/07/26-17:57:39.459684 5048   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-17:57:39.459686 5048             Options.num_levels: 7
2025/07/26-17:57:39.459688 5048        Options.min_write_buffer_number_to_merge: 1
2025/07/26-17:57:39.459690 5048     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-17:57:39.459691 5048     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-17:57:39.459693 5048            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-17:57:39.459695 5048                  Options.bottommost_compression_opts.level: 32767
2025/07/26-17:57:39.459699 5048               Options.bottommost_compression_opts.strategy: 0
2025/07/26-17:57:39.459701 5048         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-17:57:39.459703 5048         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:39.459705 5048         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-17:57:39.459707 5048                  Options.bottommost_compression_opts.enabled: false
2025/07/26-17:57:39.459709 5048         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:39.459710 5048         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:39.459712 5048            Options.compression_opts.window_bits: -14
2025/07/26-17:57:39.459714 5048                  Options.compression_opts.level: 32767
2025/07/26-17:57:39.459716 5048               Options.compression_opts.strategy: 0
2025/07/26-17:57:39.459717 5048         Options.compression_opts.max_dict_bytes: 0
2025/07/26-17:57:39.459719 5048         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:39.459721 5048         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:39.459723 5048         Options.compression_opts.parallel_threads: 1
2025/07/26-17:57:39.459724 5048                  Options.compression_opts.enabled: false
2025/07/26-17:57:39.459726 5048         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:39.459728 5048      Options.level0_file_num_compaction_trigger: 4
2025/07/26-17:57:39.459730 5048          Options.level0_slowdown_writes_trigger: 20
2025/07/26-17:57:39.459731 5048              Options.level0_stop_writes_trigger: 36
2025/07/26-17:57:39.459733 5048                   Options.target_file_size_base: 67108864
2025/07/26-17:57:39.459735 5048             Options.target_file_size_multiplier: 1
2025/07/26-17:57:39.459737 5048                Options.max_bytes_for_level_base: 268435456
2025/07/26-17:57:39.459739 5048 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-17:57:39.459740 5048          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-17:57:39.459742 5048 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-17:57:39.459744 5048 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-17:57:39.459746 5048 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-17:57:39.459748 5048 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-17:57:39.459750 5048 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-17:57:39.459751 5048 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-17:57:39.459753 5048 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-17:57:39.459755 5048       Options.max_sequential_skip_in_iterations: 8
2025/07/26-17:57:39.459757 5048                    Options.max_compaction_bytes: 1677721600
2025/07/26-17:57:39.459759 5048   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-17:57:39.459761 5048                        Options.arena_block_size: 1048576
2025/07/26-17:57:39.459763 5048   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-17:57:39.459765 5048   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-17:57:39.459767 5048                Options.disable_auto_compactions: 0
2025/07/26-17:57:39.459769 5048                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-17:57:39.459771 5048                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-17:57:39.459773 5048 Options.compaction_options_universal.size_ratio: 1
2025/07/26-17:57:39.459775 5048 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-17:57:39.459777 5048 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-17:57:39.459778 5048 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-17:57:39.459780 5048 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-17:57:39.459782 5048 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-17:57:39.459784 5048 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-17:57:39.459786 5048 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-17:57:39.459789 5048                   Options.table_properties_collectors: 
2025/07/26-17:57:39.459791 5048                   Options.inplace_update_support: 0
2025/07/26-17:57:39.459793 5048                 Options.inplace_update_num_locks: 10000
2025/07/26-17:57:39.459795 5048               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-17:57:39.459796 5048               Options.memtable_whole_key_filtering: 0
2025/07/26-17:57:39.459798 5048   Options.memtable_huge_page_size: 0
2025/07/26-17:57:39.459800 5048                           Options.bloom_locality: 0
2025/07/26-17:57:39.459802 5048                    Options.max_successive_merges: 0
2025/07/26-17:57:39.459804 5048                Options.optimize_filters_for_hits: 0
2025/07/26-17:57:39.459805 5048                Options.paranoid_file_checks: 0
2025/07/26-17:57:39.459807 5048                Options.force_consistency_checks: 1
2025/07/26-17:57:39.459809 5048                Options.report_bg_io_stats: 0
2025/07/26-17:57:39.459811 5048                               Options.ttl: 2592000
2025/07/26-17:57:39.459812 5048          Options.periodic_compaction_seconds: 0
2025/07/26-17:57:39.459814 5048                        Options.default_temperature: kUnknown
2025/07/26-17:57:39.459816 5048  Options.preclude_last_level_data_seconds: 0
2025/07/26-17:57:39.459818 5048    Options.preserve_internal_time_seconds: 0
2025/07/26-17:57:39.459820 5048                       Options.enable_blob_files: false
2025/07/26-17:57:39.459821 5048                           Options.min_blob_size: 0
2025/07/26-17:57:39.459823 5048                          Options.blob_file_size: 268435456
2025/07/26-17:57:39.459825 5048                   Options.blob_compression_type: NoCompression
2025/07/26-17:57:39.459827 5048          Options.enable_blob_garbage_collection: false
2025/07/26-17:57:39.459828 5048      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-17:57:39.459831 5048 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-17:57:39.459833 5048          Options.blob_compaction_readahead_size: 0
2025/07/26-17:57:39.459834 5048                Options.blob_file_starting_level: 0
2025/07/26-17:57:39.459836 5048         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-17:57:39.459838 5048            Options.memtable_max_range_deletions: 0
2025/07/26-17:57:39.459974 5048 [db/db_impl/db_impl.cc:3637] Created column family [tasks] (ID 1)
2025/07/26-17:57:39.462727 5048 [db/column_family.cc:618] --------------- Options for column family [raft_state]:
2025/07/26-17:57:39.462730 5048               Options.comparator: leveldb.BytewiseComparator
2025/07/26-17:57:39.462733 5048           Options.merge_operator: None
2025/07/26-17:57:39.462736 5048        Options.compaction_filter: None
2025/07/26-17:57:39.462737 5048        Options.compaction_filter_factory: None
2025/07/26-17:57:39.462739 5048  Options.sst_partitioner_factory: None
2025/07/26-17:57:39.462741 5048         Options.memtable_factory: SkipListFactory
2025/07/26-17:57:39.462743 5048            Options.table_factory: BlockBasedTable
2025/07/26-17:57:39.462759 5048            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000028558589390)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000285585B5A80
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-17:57:39.462761 5048        Options.write_buffer_size: 67108864
2025/07/26-17:57:39.462763 5048  Options.max_write_buffer_number: 2
2025/07/26-17:57:39.462765 5048          Options.compression: Snappy
2025/07/26-17:57:39.462766 5048                  Options.bottommost_compression: Disabled
2025/07/26-17:57:39.462768 5048       Options.prefix_extractor: nullptr
2025/07/26-17:57:39.462770 5048   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-17:57:39.462772 5048             Options.num_levels: 7
2025/07/26-17:57:39.462774 5048        Options.min_write_buffer_number_to_merge: 1
2025/07/26-17:57:39.462775 5048     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-17:57:39.462777 5048     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-17:57:39.462779 5048            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-17:57:39.462781 5048                  Options.bottommost_compression_opts.level: 32767
2025/07/26-17:57:39.462782 5048               Options.bottommost_compression_opts.strategy: 0
2025/07/26-17:57:39.462784 5048         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-17:57:39.462786 5048         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:39.462788 5048         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-17:57:39.462790 5048                  Options.bottommost_compression_opts.enabled: false
2025/07/26-17:57:39.462791 5048         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:39.462793 5048         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:39.462795 5048            Options.compression_opts.window_bits: -14
2025/07/26-17:57:39.462797 5048                  Options.compression_opts.level: 32767
2025/07/26-17:57:39.462799 5048               Options.compression_opts.strategy: 0
2025/07/26-17:57:39.462800 5048         Options.compression_opts.max_dict_bytes: 0
2025/07/26-17:57:39.462802 5048         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:39.462804 5048         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:39.462806 5048         Options.compression_opts.parallel_threads: 1
2025/07/26-17:57:39.462807 5048                  Options.compression_opts.enabled: false
2025/07/26-17:57:39.462828 5048         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:39.462831 5048      Options.level0_file_num_compaction_trigger: 4
2025/07/26-17:57:39.462832 5048          Options.level0_slowdown_writes_trigger: 20
2025/07/26-17:57:39.462834 5048              Options.level0_stop_writes_trigger: 36
2025/07/26-17:57:39.462836 5048                   Options.target_file_size_base: 67108864
2025/07/26-17:57:39.462838 5048             Options.target_file_size_multiplier: 1
2025/07/26-17:57:39.462839 5048                Options.max_bytes_for_level_base: 268435456
2025/07/26-17:57:39.462841 5048 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-17:57:39.462843 5048          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-17:57:39.462845 5048 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-17:57:39.462847 5048 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-17:57:39.462849 5048 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-17:57:39.462850 5048 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-17:57:39.462852 5048 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-17:57:39.462854 5048 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-17:57:39.462856 5048 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-17:57:39.462857 5048       Options.max_sequential_skip_in_iterations: 8
2025/07/26-17:57:39.462859 5048                    Options.max_compaction_bytes: 1677721600
2025/07/26-17:57:39.462861 5048   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-17:57:39.462863 5048                        Options.arena_block_size: 1048576
2025/07/26-17:57:39.462864 5048   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-17:57:39.462866 5048   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-17:57:39.462868 5048                Options.disable_auto_compactions: 0
2025/07/26-17:57:39.462870 5048                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-17:57:39.462872 5048                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-17:57:39.462874 5048 Options.compaction_options_universal.size_ratio: 1
2025/07/26-17:57:39.462875 5048 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-17:57:39.462877 5048 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-17:57:39.462879 5048 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-17:57:39.462881 5048 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-17:57:39.462883 5048 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-17:57:39.462885 5048 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-17:57:39.462887 5048 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-17:57:39.462890 5048                   Options.table_properties_collectors: 
2025/07/26-17:57:39.462892 5048                   Options.inplace_update_support: 0
2025/07/26-17:57:39.462894 5048                 Options.inplace_update_num_locks: 10000
2025/07/26-17:57:39.462896 5048               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-17:57:39.462898 5048               Options.memtable_whole_key_filtering: 0
2025/07/26-17:57:39.462899 5048   Options.memtable_huge_page_size: 0
2025/07/26-17:57:39.462901 5048                           Options.bloom_locality: 0
2025/07/26-17:57:39.462903 5048                    Options.max_successive_merges: 0
2025/07/26-17:57:39.462904 5048                Options.optimize_filters_for_hits: 0
2025/07/26-17:57:39.462906 5048                Options.paranoid_file_checks: 0
2025/07/26-17:57:39.462908 5048                Options.force_consistency_checks: 1
2025/07/26-17:57:39.462910 5048                Options.report_bg_io_stats: 0
2025/07/26-17:57:39.462911 5048                               Options.ttl: 2592000
2025/07/26-17:57:39.462913 5048          Options.periodic_compaction_seconds: 0
2025/07/26-17:57:39.462916 5048                        Options.default_temperature: kUnknown
2025/07/26-17:57:39.462918 5048  Options.preclude_last_level_data_seconds: 0
2025/07/26-17:57:39.462920 5048    Options.preserve_internal_time_seconds: 0
2025/07/26-17:57:39.462921 5048                       Options.enable_blob_files: false
2025/07/26-17:57:39.462923 5048                           Options.min_blob_size: 0
2025/07/26-17:57:39.462925 5048                          Options.blob_file_size: 268435456
2025/07/26-17:57:39.462927 5048                   Options.blob_compression_type: NoCompression
2025/07/26-17:57:39.462928 5048          Options.enable_blob_garbage_collection: false
2025/07/26-17:57:39.462930 5048      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-17:57:39.462932 5048 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-17:57:39.462934 5048          Options.blob_compaction_readahead_size: 0
2025/07/26-17:57:39.462936 5048                Options.blob_file_starting_level: 0
2025/07/26-17:57:39.462938 5048         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-17:57:39.462940 5048            Options.memtable_max_range_deletions: 0
2025/07/26-17:57:39.463042 5048 [db/db_impl/db_impl.cc:3637] Created column family [raft_state] (ID 2)
2025/07/26-17:57:39.465691 5048 [db/column_family.cc:618] --------------- Options for column family [task_versions]:
2025/07/26-17:57:39.465708 5048               Options.comparator: leveldb.BytewiseComparator
2025/07/26-17:57:39.465710 5048           Options.merge_operator: None
2025/07/26-17:57:39.465712 5048        Options.compaction_filter: None
2025/07/26-17:57:39.465714 5048        Options.compaction_filter_factory: None
2025/07/26-17:57:39.465715 5048  Options.sst_partitioner_factory: None
2025/07/26-17:57:39.465717 5048         Options.memtable_factory: SkipListFactory
2025/07/26-17:57:39.465719 5048            Options.table_factory: BlockBasedTable
2025/07/26-17:57:39.465804 5048            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000285585897E0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000285585B5E40
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-17:57:39.465816 5048        Options.write_buffer_size: 67108864
2025/07/26-17:57:39.465818 5048  Options.max_write_buffer_number: 2
2025/07/26-17:57:39.465820 5048          Options.compression: Snappy
2025/07/26-17:57:39.465822 5048                  Options.bottommost_compression: Disabled
2025/07/26-17:57:39.465824 5048       Options.prefix_extractor: nullptr
2025/07/26-17:57:39.465826 5048   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-17:57:39.465828 5048             Options.num_levels: 7
2025/07/26-17:57:39.465829 5048        Options.min_write_buffer_number_to_merge: 1
2025/07/26-17:57:39.465831 5048     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-17:57:39.465833 5048     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-17:57:39.465835 5048            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-17:57:39.465843 5048                  Options.bottommost_compression_opts.level: 32767
2025/07/26-17:57:39.465848 5048               Options.bottommost_compression_opts.strategy: 0
2025/07/26-17:57:39.465849 5048         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-17:57:39.465851 5048         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:39.465853 5048         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-17:57:39.465855 5048                  Options.bottommost_compression_opts.enabled: false
2025/07/26-17:57:39.465857 5048         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:39.465858 5048         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:39.465860 5048            Options.compression_opts.window_bits: -14
2025/07/26-17:57:39.465862 5048                  Options.compression_opts.level: 32767
2025/07/26-17:57:39.465864 5048               Options.compression_opts.strategy: 0
2025/07/26-17:57:39.465866 5048         Options.compression_opts.max_dict_bytes: 0
2025/07/26-17:57:39.465867 5048         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:39.465869 5048         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:39.465871 5048         Options.compression_opts.parallel_threads: 1
2025/07/26-17:57:39.465873 5048                  Options.compression_opts.enabled: false
2025/07/26-17:57:39.465875 5048         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:39.465876 5048      Options.level0_file_num_compaction_trigger: 4
2025/07/26-17:57:39.465878 5048          Options.level0_slowdown_writes_trigger: 20
2025/07/26-17:57:39.465880 5048              Options.level0_stop_writes_trigger: 36
2025/07/26-17:57:39.465882 5048                   Options.target_file_size_base: 67108864
2025/07/26-17:57:39.465883 5048             Options.target_file_size_multiplier: 1
2025/07/26-17:57:39.465885 5048                Options.max_bytes_for_level_base: 268435456
2025/07/26-17:57:39.465887 5048 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-17:57:39.465889 5048          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-17:57:39.465892 5048 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-17:57:39.465894 5048 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-17:57:39.465896 5048 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-17:57:39.465898 5048 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-17:57:39.465900 5048 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-17:57:39.465901 5048 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-17:57:39.465903 5048 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-17:57:39.465905 5048       Options.max_sequential_skip_in_iterations: 8
2025/07/26-17:57:39.465907 5048                    Options.max_compaction_bytes: 1677721600
2025/07/26-17:57:39.465908 5048   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-17:57:39.465910 5048                        Options.arena_block_size: 1048576
2025/07/26-17:57:39.465912 5048   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-17:57:39.465914 5048   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-17:57:39.465916 5048                Options.disable_auto_compactions: 0
2025/07/26-17:57:39.465918 5048                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-17:57:39.465921 5048                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-17:57:39.465923 5048 Options.compaction_options_universal.size_ratio: 1
2025/07/26-17:57:39.465924 5048 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-17:57:39.465926 5048 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-17:57:39.465928 5048 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-17:57:39.465930 5048 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-17:57:39.465933 5048 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-17:57:39.465935 5048 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-17:57:39.465937 5048 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-17:57:39.465943 5048                   Options.table_properties_collectors: 
2025/07/26-17:57:39.465944 5048                   Options.inplace_update_support: 0
2025/07/26-17:57:39.465946 5048                 Options.inplace_update_num_locks: 10000
2025/07/26-17:57:39.465948 5048               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-17:57:39.465950 5048               Options.memtable_whole_key_filtering: 0
2025/07/26-17:57:39.465952 5048   Options.memtable_huge_page_size: 0
2025/07/26-17:57:39.465954 5048                           Options.bloom_locality: 0
2025/07/26-17:57:39.465955 5048                    Options.max_successive_merges: 0
2025/07/26-17:57:39.465957 5048                Options.optimize_filters_for_hits: 0
2025/07/26-17:57:39.465959 5048                Options.paranoid_file_checks: 0
2025/07/26-17:57:39.465961 5048                Options.force_consistency_checks: 1
2025/07/26-17:57:39.465963 5048                Options.report_bg_io_stats: 0
2025/07/26-17:57:39.465965 5048                               Options.ttl: 2592000
2025/07/26-17:57:39.465966 5048          Options.periodic_compaction_seconds: 0
2025/07/26-17:57:39.465968 5048                        Options.default_temperature: kUnknown
2025/07/26-17:57:39.465970 5048  Options.preclude_last_level_data_seconds: 0
2025/07/26-17:57:39.465972 5048    Options.preserve_internal_time_seconds: 0
2025/07/26-17:57:39.465974 5048                       Options.enable_blob_files: false
2025/07/26-17:57:39.465975 5048                           Options.min_blob_size: 0
2025/07/26-17:57:39.465977 5048                          Options.blob_file_size: 268435456
2025/07/26-17:57:39.465979 5048                   Options.blob_compression_type: NoCompression
2025/07/26-17:57:39.465981 5048          Options.enable_blob_garbage_collection: false
2025/07/26-17:57:39.465983 5048      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-17:57:39.465985 5048 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-17:57:39.465987 5048          Options.blob_compaction_readahead_size: 0
2025/07/26-17:57:39.465988 5048                Options.blob_file_starting_level: 0
2025/07/26-17:57:39.465990 5048         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-17:57:39.465992 5048            Options.memtable_max_range_deletions: 0
2025/07/26-17:57:39.466188 5048 [db/db_impl/db_impl.cc:3637] Created column family [task_versions] (ID 3)
2025/07/26-17:57:39.468655 5048 [db/column_family.cc:618] --------------- Options for column family [task_index]:
2025/07/26-17:57:39.468669 5048               Options.comparator: leveldb.BytewiseComparator
2025/07/26-17:57:39.468672 5048           Options.merge_operator: None
2025/07/26-17:57:39.468674 5048        Options.compaction_filter: None
2025/07/26-17:57:39.468676 5048        Options.compaction_filter_factory: None
2025/07/26-17:57:39.468679 5048  Options.sst_partitioner_factory: None
2025/07/26-17:57:39.468681 5048         Options.memtable_factory: SkipListFactory
2025/07/26-17:57:39.468683 5048            Options.table_factory: BlockBasedTable
2025/07/26-17:57:39.468705 5048            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000285585891E0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000285585B54E0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-17:57:39.468710 5048        Options.write_buffer_size: 67108864
2025/07/26-17:57:39.468714 5048  Options.max_write_buffer_number: 2
2025/07/26-17:57:39.468717 5048          Options.compression: Snappy
2025/07/26-17:57:39.468719 5048                  Options.bottommost_compression: Disabled
2025/07/26-17:57:39.468721 5048       Options.prefix_extractor: nullptr
2025/07/26-17:57:39.468723 5048   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-17:57:39.468726 5048             Options.num_levels: 7
2025/07/26-17:57:39.468728 5048        Options.min_write_buffer_number_to_merge: 1
2025/07/26-17:57:39.468730 5048     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-17:57:39.468732 5048     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-17:57:39.468735 5048            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-17:57:39.468737 5048                  Options.bottommost_compression_opts.level: 32767
2025/07/26-17:57:39.468739 5048               Options.bottommost_compression_opts.strategy: 0
2025/07/26-17:57:39.468742 5048         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-17:57:39.468744 5048         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:39.468746 5048         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-17:57:39.468748 5048                  Options.bottommost_compression_opts.enabled: false
2025/07/26-17:57:39.468751 5048         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:39.468753 5048         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:39.468755 5048            Options.compression_opts.window_bits: -14
2025/07/26-17:57:39.468758 5048                  Options.compression_opts.level: 32767
2025/07/26-17:57:39.468760 5048               Options.compression_opts.strategy: 0
2025/07/26-17:57:39.468762 5048         Options.compression_opts.max_dict_bytes: 0
2025/07/26-17:57:39.468764 5048         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:39.468766 5048         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:39.468769 5048         Options.compression_opts.parallel_threads: 1
2025/07/26-17:57:39.468771 5048                  Options.compression_opts.enabled: false
2025/07/26-17:57:39.468773 5048         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:39.468775 5048      Options.level0_file_num_compaction_trigger: 4
2025/07/26-17:57:39.468778 5048          Options.level0_slowdown_writes_trigger: 20
2025/07/26-17:57:39.468780 5048              Options.level0_stop_writes_trigger: 36
2025/07/26-17:57:39.468782 5048                   Options.target_file_size_base: 67108864
2025/07/26-17:57:39.468784 5048             Options.target_file_size_multiplier: 1
2025/07/26-17:57:39.468786 5048                Options.max_bytes_for_level_base: 268435456
2025/07/26-17:57:39.468789 5048 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-17:57:39.468791 5048          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-17:57:39.468794 5048 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-17:57:39.468796 5048 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-17:57:39.468798 5048 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-17:57:39.468800 5048 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-17:57:39.468803 5048 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-17:57:39.468806 5048 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-17:57:39.468808 5048 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-17:57:39.468811 5048       Options.max_sequential_skip_in_iterations: 8
2025/07/26-17:57:39.468813 5048                    Options.max_compaction_bytes: 1677721600
2025/07/26-17:57:39.468815 5048   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-17:57:39.468817 5048                        Options.arena_block_size: 1048576
2025/07/26-17:57:39.468820 5048   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-17:57:39.468822 5048   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-17:57:39.468824 5048                Options.disable_auto_compactions: 0
2025/07/26-17:57:39.468827 5048                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-17:57:39.468830 5048                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-17:57:39.468832 5048 Options.compaction_options_universal.size_ratio: 1
2025/07/26-17:57:39.468835 5048 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-17:57:39.468837 5048 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-17:57:39.468839 5048 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-17:57:39.468842 5048 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-17:57:39.468844 5048 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-17:57:39.468846 5048 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-17:57:39.468849 5048 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-17:57:39.468853 5048                   Options.table_properties_collectors: 
2025/07/26-17:57:39.468855 5048                   Options.inplace_update_support: 0
2025/07/26-17:57:39.468857 5048                 Options.inplace_update_num_locks: 10000
2025/07/26-17:57:39.468860 5048               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-17:57:39.468862 5048               Options.memtable_whole_key_filtering: 0
2025/07/26-17:57:39.468864 5048   Options.memtable_huge_page_size: 0
2025/07/26-17:57:39.468867 5048                           Options.bloom_locality: 0
2025/07/26-17:57:39.468869 5048                    Options.max_successive_merges: 0
2025/07/26-17:57:39.468872 5048                Options.optimize_filters_for_hits: 0
2025/07/26-17:57:39.468874 5048                Options.paranoid_file_checks: 0
2025/07/26-17:57:39.468876 5048                Options.force_consistency_checks: 1
2025/07/26-17:57:39.468877 5048                Options.report_bg_io_stats: 0
2025/07/26-17:57:39.468879 5048                               Options.ttl: 2592000
2025/07/26-17:57:39.468881 5048          Options.periodic_compaction_seconds: 0
2025/07/26-17:57:39.468883 5048                        Options.default_temperature: kUnknown
2025/07/26-17:57:39.468885 5048  Options.preclude_last_level_data_seconds: 0
2025/07/26-17:57:39.468886 5048    Options.preserve_internal_time_seconds: 0
2025/07/26-17:57:39.468888 5048                       Options.enable_blob_files: false
2025/07/26-17:57:39.468890 5048                           Options.min_blob_size: 0
2025/07/26-17:57:39.468891 5048                          Options.blob_file_size: 268435456
2025/07/26-17:57:39.468893 5048                   Options.blob_compression_type: NoCompression
2025/07/26-17:57:39.468895 5048          Options.enable_blob_garbage_collection: false
2025/07/26-17:57:39.468897 5048      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-17:57:39.468899 5048 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-17:57:39.468901 5048          Options.blob_compaction_readahead_size: 0
2025/07/26-17:57:39.468903 5048                Options.blob_file_starting_level: 0
2025/07/26-17:57:39.468905 5048         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-17:57:39.468907 5048            Options.memtable_max_range_deletions: 0
2025/07/26-17:57:39.469074 5048 [db/db_impl/db_impl.cc:3637] Created column family [task_index] (ID 4)
2025/07/26-17:57:39.475121 5048 [db/db_impl/db_impl_open.cc:2157] SstFileManager instance 00000285586099D0
2025/07/26-17:57:39.475315 5048 DB pointer 0000028558607E00
2025/07/26-17:57:39.475788 5e28 [db/db_impl/db_impl.cc:1140] ------- DUMPING STATS -------
2025/07/26-17:57:39.475797 5e28 [db/db_impl/db_impl.cc:1141] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000285585B5B70#18676 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 4.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** Compaction Stats [tasks] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [tasks] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000285585B5210#18676 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [tasks] **

** Compaction Stats [raft_state] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [raft_state] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000285585B5A80#18676 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2.4e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [raft_state] **

** Compaction Stats [task_versions] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_versions] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000285585B5E40#18676 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2.4e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_versions] **

** Compaction Stats [task_index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000285585B54E0#18676 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2.3e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_index] **
