2025/07/26-18:06:43.414809 59c0 RocksDB version: 8.10.0
2025/07/26-18:06:43.415395 59c0 Compile date 2023-12-15 13:01:14
2025/07/26-18:06:43.415406 59c0 DB SUMMARY
2025/07/26-18:06:43.415413 59c0 Host name (Env):  DESKTOP-RNBFHG8
2025/07/26-18:06:43.415418 59c0 DB Session ID:  EFIW0YZ1US0QEPAA3QPN
2025/07/26-18:06:43.415564 59c0 SST files in ./data/12D3KooWDNJMRM2CWKW65sPxBrtLYhZaMBuDiD8VyiA9jPFrQ3bx dir, Total Num: 0, files: 
2025/07/26-18:06:43.415571 59c0 Write Ahead Log file in ./data/12D3KooWDNJMRM2CWKW65sPxBrtLYhZaMBuDiD8VyiA9jPFrQ3bx: 
2025/07/26-18:06:43.415577 59c0                         Options.error_if_exists: 0
2025/07/26-18:06:43.415582 59c0                       Options.create_if_missing: 1
2025/07/26-18:06:43.415587 59c0                         Options.paranoid_checks: 1
2025/07/26-18:06:43.425504 59c0             Options.flush_verify_memtable_count: 1
2025/07/26-18:06:43.425522 59c0          Options.compaction_verify_record_count: 1
2025/07/26-18:06:43.425524 59c0                               Options.track_and_verify_wals_in_manifest: 0
2025/07/26-18:06:43.425526 59c0        Options.verify_sst_unique_id_in_manifest: 1
2025/07/26-18:06:43.425528 59c0                                     Options.env: 0000020FA04DC510
2025/07/26-18:06:43.425530 59c0                                      Options.fs: WinFS
2025/07/26-18:06:43.425533 59c0                                Options.info_log: 0000020FA0477050
2025/07/26-18:06:43.425534 59c0                Options.max_file_opening_threads: 16
2025/07/26-18:06:43.425536 59c0                              Options.statistics: 0000000000000000
2025/07/26-18:06:43.425538 59c0                               Options.use_fsync: 0
2025/07/26-18:06:43.425540 59c0                       Options.max_log_file_size: 0
2025/07/26-18:06:43.425542 59c0                  Options.max_manifest_file_size: 1073741824
2025/07/26-18:06:43.425543 59c0                   Options.log_file_time_to_roll: 0
2025/07/26-18:06:43.425545 59c0                       Options.keep_log_file_num: 1000
2025/07/26-18:06:43.425547 59c0                    Options.recycle_log_file_num: 0
2025/07/26-18:06:43.425549 59c0                         Options.allow_fallocate: 1
2025/07/26-18:06:43.425550 59c0                        Options.allow_mmap_reads: 0
2025/07/26-18:06:43.425552 59c0                       Options.allow_mmap_writes: 0
2025/07/26-18:06:43.425554 59c0                        Options.use_direct_reads: 0
2025/07/26-18:06:43.425556 59c0                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/26-18:06:43.425557 59c0          Options.create_missing_column_families: 1
2025/07/26-18:06:43.425559 59c0                              Options.db_log_dir: 
2025/07/26-18:06:43.425561 59c0                                 Options.wal_dir: 
2025/07/26-18:06:43.425563 59c0                Options.table_cache_numshardbits: 6
2025/07/26-18:06:43.425564 59c0                         Options.WAL_ttl_seconds: 0
2025/07/26-18:06:43.425566 59c0                       Options.WAL_size_limit_MB: 0
2025/07/26-18:06:43.425568 59c0                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/26-18:06:43.425570 59c0             Options.manifest_preallocation_size: 4194304
2025/07/26-18:06:43.425572 59c0                     Options.is_fd_close_on_exec: 1
2025/07/26-18:06:43.425573 59c0                   Options.advise_random_on_open: 1
2025/07/26-18:06:43.425575 59c0                    Options.db_write_buffer_size: 0
2025/07/26-18:06:43.425577 59c0                    Options.write_buffer_manager: 0000020FA04DC9C0
2025/07/26-18:06:43.425579 59c0         Options.access_hint_on_compaction_start: 1
2025/07/26-18:06:43.425580 59c0           Options.random_access_max_buffer_size: 1048576
2025/07/26-18:06:43.425582 59c0                      Options.use_adaptive_mutex: 0
2025/07/26-18:06:43.425584 59c0                            Options.rate_limiter: 0000000000000000
2025/07/26-18:06:43.425586 59c0     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/26-18:06:43.425588 59c0                       Options.wal_recovery_mode: 2
2025/07/26-18:06:43.425619 59c0                  Options.enable_thread_tracking: 0
2025/07/26-18:06:43.425624 59c0                  Options.enable_pipelined_write: 0
2025/07/26-18:06:43.425626 59c0                  Options.unordered_write: 0
2025/07/26-18:06:43.425628 59c0         Options.allow_concurrent_memtable_write: 1
2025/07/26-18:06:43.425630 59c0      Options.enable_write_thread_adaptive_yield: 1
2025/07/26-18:06:43.425631 59c0             Options.write_thread_max_yield_usec: 100
2025/07/26-18:06:43.425633 59c0            Options.write_thread_slow_yield_usec: 3
2025/07/26-18:06:43.425635 59c0                               Options.row_cache: None
2025/07/26-18:06:43.425637 59c0                              Options.wal_filter: None
2025/07/26-18:06:43.425638 59c0             Options.avoid_flush_during_recovery: 0
2025/07/26-18:06:43.425640 59c0             Options.allow_ingest_behind: 0
2025/07/26-18:06:43.425642 59c0             Options.two_write_queues: 0
2025/07/26-18:06:43.425644 59c0             Options.manual_wal_flush: 0
2025/07/26-18:06:43.425645 59c0             Options.wal_compression: 0
2025/07/26-18:06:43.425647 59c0             Options.atomic_flush: 0
2025/07/26-18:06:43.425649 59c0             Options.avoid_unnecessary_blocking_io: 0
2025/07/26-18:06:43.425650 59c0                 Options.persist_stats_to_disk: 0
2025/07/26-18:06:43.425652 59c0                 Options.write_dbid_to_manifest: 0
2025/07/26-18:06:43.425654 59c0                 Options.log_readahead_size: 0
2025/07/26-18:06:43.425656 59c0                 Options.file_checksum_gen_factory: Unknown
2025/07/26-18:06:43.425658 59c0                 Options.best_efforts_recovery: 0
2025/07/26-18:06:43.425659 59c0                Options.max_bgerror_resume_count: 2147483647
2025/07/26-18:06:43.425661 59c0            Options.bgerror_resume_retry_interval: 1000000
2025/07/26-18:06:43.425663 59c0             Options.allow_data_in_errors: 0
2025/07/26-18:06:43.425665 59c0             Options.db_host_id: __hostname__
2025/07/26-18:06:43.425666 59c0             Options.enforce_single_del_contracts: true
2025/07/26-18:06:43.425669 59c0             Options.max_background_jobs: 4
2025/07/26-18:06:43.425671 59c0             Options.max_background_compactions: -1
2025/07/26-18:06:43.425672 59c0             Options.max_subcompactions: 1
2025/07/26-18:06:43.425674 59c0             Options.avoid_flush_during_shutdown: 0
2025/07/26-18:06:43.425676 59c0           Options.writable_file_max_buffer_size: 1048576
2025/07/26-18:06:43.425678 59c0             Options.delayed_write_rate : 16777216
2025/07/26-18:06:43.425679 59c0             Options.max_total_wal_size: 0
2025/07/26-18:06:43.425681 59c0             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/26-18:06:43.425683 59c0                   Options.stats_dump_period_sec: 600
2025/07/26-18:06:43.425685 59c0                 Options.stats_persist_period_sec: 600
2025/07/26-18:06:43.425686 59c0                 Options.stats_history_buffer_size: 1048576
2025/07/26-18:06:43.425688 59c0                          Options.max_open_files: 1000
2025/07/26-18:06:43.425690 59c0                          Options.bytes_per_sync: 0
2025/07/26-18:06:43.425692 59c0                      Options.wal_bytes_per_sync: 0
2025/07/26-18:06:43.425693 59c0                   Options.strict_bytes_per_sync: 0
2025/07/26-18:06:43.425695 59c0       Options.compaction_readahead_size: 2097152
2025/07/26-18:06:43.425697 59c0                  Options.max_background_flushes: -1
2025/07/26-18:06:43.425699 59c0 Options.daily_offpeak_time_utc: 
2025/07/26-18:06:43.425700 59c0 Compression algorithms supported:
2025/07/26-18:06:43.425703 59c0 	kZSTD supported: 1
2025/07/26-18:06:43.425705 59c0 	kSnappyCompression supported: 1
2025/07/26-18:06:43.425707 59c0 	kBZip2Compression supported: 1
2025/07/26-18:06:43.425709 59c0 	kZlibCompression supported: 1
2025/07/26-18:06:43.425710 59c0 	kLZ4Compression supported: 1
2025/07/26-18:06:43.425712 59c0 	kXpressCompression supported: 0
2025/07/26-18:06:43.425714 59c0 	kLZ4HCCompression supported: 1
2025/07/26-18:06:43.425725 59c0 	kZSTDNotFinalCompression supported: 1
2025/07/26-18:06:43.425730 59c0 Fast CRC32 supported: Not supported on x86
2025/07/26-18:06:43.425732 59c0 DMutex implementation: std::mutex
2025/07/26-18:06:43.432364 59c0 [db/db_impl/db_impl_open.cc:325] Creating manifest 1 
2025/07/26-18:06:43.442317 59c0 [db/version_set.cc:5942] Recovering from manifest file: ./data/12D3KooWDNJMRM2CWKW65sPxBrtLYhZaMBuDiD8VyiA9jPFrQ3bx/MANIFEST-000001
2025/07/26-18:06:43.442449 59c0 [db/column_family.cc:618] --------------- Options for column family [default]:
2025/07/26-18:06:43.442452 59c0               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:06:43.442455 59c0           Options.merge_operator: None
2025/07/26-18:06:43.442457 59c0        Options.compaction_filter: None
2025/07/26-18:06:43.442458 59c0        Options.compaction_filter_factory: None
2025/07/26-18:06:43.442460 59c0  Options.sst_partitioner_factory: None
2025/07/26-18:06:43.442462 59c0         Options.memtable_factory: SkipListFactory
2025/07/26-18:06:43.442463 59c0            Options.table_factory: BlockBasedTable
2025/07/26-18:06:43.442517 59c0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000020FA0518720)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000020FA04DCF70
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:06:43.442520 59c0        Options.write_buffer_size: 67108864
2025/07/26-18:06:43.442522 59c0  Options.max_write_buffer_number: 2
2025/07/26-18:06:43.442524 59c0          Options.compression: Snappy
2025/07/26-18:06:43.442526 59c0                  Options.bottommost_compression: Disabled
2025/07/26-18:06:43.442528 59c0       Options.prefix_extractor: nullptr
2025/07/26-18:06:43.442530 59c0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:06:43.442531 59c0             Options.num_levels: 7
2025/07/26-18:06:43.442533 59c0        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:06:43.442535 59c0     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:06:43.442537 59c0     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:06:43.442538 59c0            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:06:43.442540 59c0                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:06:43.442542 59c0               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:06:43.442544 59c0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:06:43.442546 59c0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:06:43.442548 59c0         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:06:43.442550 59c0                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:06:43.442552 59c0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:06:43.442554 59c0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:06:43.442556 59c0            Options.compression_opts.window_bits: -14
2025/07/26-18:06:43.442564 59c0                  Options.compression_opts.level: 32767
2025/07/26-18:06:43.442567 59c0               Options.compression_opts.strategy: 0
2025/07/26-18:06:43.442568 59c0         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:06:43.442571 59c0         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:06:43.442572 59c0         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:06:43.442574 59c0         Options.compression_opts.parallel_threads: 1
2025/07/26-18:06:43.442576 59c0                  Options.compression_opts.enabled: false
2025/07/26-18:06:43.442578 59c0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:06:43.442580 59c0      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:06:43.442582 59c0          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:06:43.442584 59c0              Options.level0_stop_writes_trigger: 36
2025/07/26-18:06:43.442585 59c0                   Options.target_file_size_base: 67108864
2025/07/26-18:06:43.442587 59c0             Options.target_file_size_multiplier: 1
2025/07/26-18:06:43.442589 59c0                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:06:43.442591 59c0 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:06:43.442592 59c0          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:06:43.442595 59c0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:06:43.442597 59c0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:06:43.442598 59c0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:06:43.442600 59c0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:06:43.442602 59c0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:06:43.442604 59c0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:06:43.442605 59c0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:06:43.442607 59c0       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:06:43.442609 59c0                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:06:43.442611 59c0   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:06:43.442613 59c0                        Options.arena_block_size: 1048576
2025/07/26-18:06:43.442614 59c0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:06:43.442616 59c0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:06:43.442618 59c0                Options.disable_auto_compactions: 0
2025/07/26-18:06:43.442621 59c0                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:06:43.442623 59c0                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:06:43.442625 59c0 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:06:43.442627 59c0 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:06:43.442629 59c0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:06:43.442630 59c0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:06:43.442632 59c0 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:06:43.442634 59c0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:06:43.442636 59c0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:06:43.442638 59c0 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:06:43.442642 59c0                   Options.table_properties_collectors: 
2025/07/26-18:06:43.442643 59c0                   Options.inplace_update_support: 0
2025/07/26-18:06:43.442645 59c0                 Options.inplace_update_num_locks: 10000
2025/07/26-18:06:43.442647 59c0               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:06:43.442649 59c0               Options.memtable_whole_key_filtering: 0
2025/07/26-18:06:43.442651 59c0   Options.memtable_huge_page_size: 0
2025/07/26-18:06:43.442678 59c0                           Options.bloom_locality: 0
2025/07/26-18:06:43.442681 59c0                    Options.max_successive_merges: 0
2025/07/26-18:06:43.442682 59c0                Options.optimize_filters_for_hits: 0
2025/07/26-18:06:43.442684 59c0                Options.paranoid_file_checks: 0
2025/07/26-18:06:43.442686 59c0                Options.force_consistency_checks: 1
2025/07/26-18:06:43.442688 59c0                Options.report_bg_io_stats: 0
2025/07/26-18:06:43.442689 59c0                               Options.ttl: 2592000
2025/07/26-18:06:43.442691 59c0          Options.periodic_compaction_seconds: 0
2025/07/26-18:06:43.442693 59c0                        Options.default_temperature: kUnknown
2025/07/26-18:06:43.442695 59c0  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:06:43.442697 59c0    Options.preserve_internal_time_seconds: 0
2025/07/26-18:06:43.442698 59c0                       Options.enable_blob_files: false
2025/07/26-18:06:43.442700 59c0                           Options.min_blob_size: 0
2025/07/26-18:06:43.442702 59c0                          Options.blob_file_size: 268435456
2025/07/26-18:06:43.442704 59c0                   Options.blob_compression_type: NoCompression
2025/07/26-18:06:43.442706 59c0          Options.enable_blob_garbage_collection: false
2025/07/26-18:06:43.442707 59c0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:06:43.442709 59c0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:06:43.442711 59c0          Options.blob_compaction_readahead_size: 0
2025/07/26-18:06:43.442713 59c0                Options.blob_file_starting_level: 0
2025/07/26-18:06:43.442715 59c0         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:06:43.442717 59c0            Options.memtable_max_range_deletions: 0
2025/07/26-18:06:43.443523 59c0 [db/version_set.cc:5993] Recovered from manifest file:./data/12D3KooWDNJMRM2CWKW65sPxBrtLYhZaMBuDiD8VyiA9jPFrQ3bx/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/07/26-18:06:43.443535 59c0 [db/version_set.cc:6002] Column family [default] (ID 0), log number is 0
2025/07/26-18:06:43.443765 59c0 [db/db_impl/db_impl_open.cc:646] DB ID: 3a9cc759-6a08-11f0-98b6-d4e98a1a402d
2025/07/26-18:06:43.444814 59c0 [db/version_set.cc:5439] Creating manifest 5
2025/07/26-18:06:43.455880 59c0 [db/column_family.cc:618] --------------- Options for column family [tasks]:
2025/07/26-18:06:43.455888 59c0               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:06:43.455890 59c0           Options.merge_operator: None
2025/07/26-18:06:43.455892 59c0        Options.compaction_filter: None
2025/07/26-18:06:43.455894 59c0        Options.compaction_filter_factory: None
2025/07/26-18:06:43.455895 59c0  Options.sst_partitioner_factory: None
2025/07/26-18:06:43.455897 59c0         Options.memtable_factory: SkipListFactory
2025/07/26-18:06:43.455899 59c0            Options.table_factory: BlockBasedTable
2025/07/26-18:06:43.455919 59c0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000020FA05177F0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000020FA04DCAC0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:06:43.455923 59c0        Options.write_buffer_size: 67108864
2025/07/26-18:06:43.455926 59c0  Options.max_write_buffer_number: 2
2025/07/26-18:06:43.455927 59c0          Options.compression: Snappy
2025/07/26-18:06:43.455929 59c0                  Options.bottommost_compression: Disabled
2025/07/26-18:06:43.455931 59c0       Options.prefix_extractor: nullptr
2025/07/26-18:06:43.455933 59c0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:06:43.455935 59c0             Options.num_levels: 7
2025/07/26-18:06:43.455936 59c0        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:06:43.455938 59c0     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:06:43.455940 59c0     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:06:43.455941 59c0            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:06:43.455943 59c0                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:06:43.455945 59c0               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:06:43.455947 59c0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:06:43.455949 59c0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:06:43.455951 59c0         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:06:43.455952 59c0                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:06:43.455954 59c0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:06:43.455956 59c0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:06:43.455958 59c0            Options.compression_opts.window_bits: -14
2025/07/26-18:06:43.455960 59c0                  Options.compression_opts.level: 32767
2025/07/26-18:06:43.455962 59c0               Options.compression_opts.strategy: 0
2025/07/26-18:06:43.455963 59c0         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:06:43.455965 59c0         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:06:43.455967 59c0         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:06:43.455968 59c0         Options.compression_opts.parallel_threads: 1
2025/07/26-18:06:43.455970 59c0                  Options.compression_opts.enabled: false
2025/07/26-18:06:43.455972 59c0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:06:43.455974 59c0      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:06:43.455975 59c0          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:06:43.455977 59c0              Options.level0_stop_writes_trigger: 36
2025/07/26-18:06:43.455979 59c0                   Options.target_file_size_base: 67108864
2025/07/26-18:06:43.455981 59c0             Options.target_file_size_multiplier: 1
2025/07/26-18:06:43.455982 59c0                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:06:43.455984 59c0 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:06:43.455986 59c0          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:06:43.455988 59c0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:06:43.455990 59c0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:06:43.455992 59c0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:06:43.455993 59c0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:06:43.455995 59c0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:06:43.455997 59c0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:06:43.455999 59c0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:06:43.456000 59c0       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:06:43.456002 59c0                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:06:43.456004 59c0   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:06:43.456007 59c0                        Options.arena_block_size: 1048576
2025/07/26-18:06:43.456008 59c0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:06:43.456010 59c0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:06:43.456012 59c0                Options.disable_auto_compactions: 0
2025/07/26-18:06:43.456015 59c0                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:06:43.456017 59c0                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:06:43.456019 59c0 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:06:43.456020 59c0 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:06:43.456022 59c0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:06:43.456024 59c0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:06:43.456026 59c0 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:06:43.456029 59c0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:06:43.456030 59c0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:06:43.456032 59c0 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:06:43.456036 59c0                   Options.table_properties_collectors: 
2025/07/26-18:06:43.456037 59c0                   Options.inplace_update_support: 0
2025/07/26-18:06:43.456039 59c0                 Options.inplace_update_num_locks: 10000
2025/07/26-18:06:43.456041 59c0               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:06:43.456043 59c0               Options.memtable_whole_key_filtering: 0
2025/07/26-18:06:43.456045 59c0   Options.memtable_huge_page_size: 0
2025/07/26-18:06:43.456046 59c0                           Options.bloom_locality: 0
2025/07/26-18:06:43.456048 59c0                    Options.max_successive_merges: 0
2025/07/26-18:06:43.456050 59c0                Options.optimize_filters_for_hits: 0
2025/07/26-18:06:43.456051 59c0                Options.paranoid_file_checks: 0
2025/07/26-18:06:43.456053 59c0                Options.force_consistency_checks: 1
2025/07/26-18:06:43.456055 59c0                Options.report_bg_io_stats: 0
2025/07/26-18:06:43.456057 59c0                               Options.ttl: 2592000
2025/07/26-18:06:43.456058 59c0          Options.periodic_compaction_seconds: 0
2025/07/26-18:06:43.456060 59c0                        Options.default_temperature: kUnknown
2025/07/26-18:06:43.456062 59c0  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:06:43.456064 59c0    Options.preserve_internal_time_seconds: 0
2025/07/26-18:06:43.456066 59c0                       Options.enable_blob_files: false
2025/07/26-18:06:43.456067 59c0                           Options.min_blob_size: 0
2025/07/26-18:06:43.456069 59c0                          Options.blob_file_size: 268435456
2025/07/26-18:06:43.456071 59c0                   Options.blob_compression_type: NoCompression
2025/07/26-18:06:43.456073 59c0          Options.enable_blob_garbage_collection: false
2025/07/26-18:06:43.456074 59c0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:06:43.456077 59c0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:06:43.456079 59c0          Options.blob_compaction_readahead_size: 0
2025/07/26-18:06:43.456080 59c0                Options.blob_file_starting_level: 0
2025/07/26-18:06:43.456082 59c0         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:06:43.456084 59c0            Options.memtable_max_range_deletions: 0
2025/07/26-18:06:43.456215 59c0 [db/db_impl/db_impl.cc:3637] Created column family [tasks] (ID 1)
2025/07/26-18:06:43.458809 59c0 [db/column_family.cc:618] --------------- Options for column family [raft_state]:
2025/07/26-18:06:43.458815 59c0               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:06:43.458818 59c0           Options.merge_operator: None
2025/07/26-18:06:43.458821 59c0        Options.compaction_filter: None
2025/07/26-18:06:43.458822 59c0        Options.compaction_filter_factory: None
2025/07/26-18:06:43.458824 59c0  Options.sst_partitioner_factory: None
2025/07/26-18:06:43.458826 59c0         Options.memtable_factory: SkipListFactory
2025/07/26-18:06:43.458828 59c0            Options.table_factory: BlockBasedTable
2025/07/26-18:06:43.458844 59c0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000020FA0517CA0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000020FA04DD150
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:06:43.458848 59c0        Options.write_buffer_size: 67108864
2025/07/26-18:06:43.458850 59c0  Options.max_write_buffer_number: 2
2025/07/26-18:06:43.458852 59c0          Options.compression: Snappy
2025/07/26-18:06:43.458853 59c0                  Options.bottommost_compression: Disabled
2025/07/26-18:06:43.458855 59c0       Options.prefix_extractor: nullptr
2025/07/26-18:06:43.458857 59c0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:06:43.458859 59c0             Options.num_levels: 7
2025/07/26-18:06:43.458860 59c0        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:06:43.458862 59c0     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:06:43.458864 59c0     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:06:43.458866 59c0            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:06:43.458867 59c0                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:06:43.458869 59c0               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:06:43.458871 59c0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:06:43.458873 59c0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:06:43.458875 59c0         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:06:43.458876 59c0                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:06:43.458878 59c0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:06:43.458880 59c0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:06:43.458882 59c0            Options.compression_opts.window_bits: -14
2025/07/26-18:06:43.458884 59c0                  Options.compression_opts.level: 32767
2025/07/26-18:06:43.458886 59c0               Options.compression_opts.strategy: 0
2025/07/26-18:06:43.458887 59c0         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:06:43.458889 59c0         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:06:43.458891 59c0         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:06:43.458893 59c0         Options.compression_opts.parallel_threads: 1
2025/07/26-18:06:43.458894 59c0                  Options.compression_opts.enabled: false
2025/07/26-18:06:43.458920 59c0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:06:43.458923 59c0      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:06:43.458924 59c0          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:06:43.458926 59c0              Options.level0_stop_writes_trigger: 36
2025/07/26-18:06:43.458928 59c0                   Options.target_file_size_base: 67108864
2025/07/26-18:06:43.458930 59c0             Options.target_file_size_multiplier: 1
2025/07/26-18:06:43.458931 59c0                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:06:43.458933 59c0 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:06:43.458935 59c0          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:06:43.458937 59c0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:06:43.458939 59c0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:06:43.458941 59c0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:06:43.458943 59c0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:06:43.458944 59c0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:06:43.458946 59c0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:06:43.458948 59c0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:06:43.458949 59c0       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:06:43.458951 59c0                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:06:43.458953 59c0   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:06:43.458955 59c0                        Options.arena_block_size: 1048576
2025/07/26-18:06:43.458956 59c0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:06:43.458958 59c0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:06:43.458960 59c0                Options.disable_auto_compactions: 0
2025/07/26-18:06:43.458962 59c0                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:06:43.458964 59c0                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:06:43.458966 59c0 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:06:43.458968 59c0 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:06:43.458969 59c0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:06:43.458971 59c0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:06:43.458973 59c0 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:06:43.458975 59c0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:06:43.458977 59c0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:06:43.458979 59c0 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:06:43.458982 59c0                   Options.table_properties_collectors: 
2025/07/26-18:06:43.458984 59c0                   Options.inplace_update_support: 0
2025/07/26-18:06:43.458986 59c0                 Options.inplace_update_num_locks: 10000
2025/07/26-18:06:43.458988 59c0               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:06:43.458990 59c0               Options.memtable_whole_key_filtering: 0
2025/07/26-18:06:43.458991 59c0   Options.memtable_huge_page_size: 0
2025/07/26-18:06:43.458993 59c0                           Options.bloom_locality: 0
2025/07/26-18:06:43.458995 59c0                    Options.max_successive_merges: 0
2025/07/26-18:06:43.458997 59c0                Options.optimize_filters_for_hits: 0
2025/07/26-18:06:43.458998 59c0                Options.paranoid_file_checks: 0
2025/07/26-18:06:43.459000 59c0                Options.force_consistency_checks: 1
2025/07/26-18:06:43.459002 59c0                Options.report_bg_io_stats: 0
2025/07/26-18:06:43.459003 59c0                               Options.ttl: 2592000
2025/07/26-18:06:43.459005 59c0          Options.periodic_compaction_seconds: 0
2025/07/26-18:06:43.459008 59c0                        Options.default_temperature: kUnknown
2025/07/26-18:06:43.459010 59c0  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:06:43.459011 59c0    Options.preserve_internal_time_seconds: 0
2025/07/26-18:06:43.459013 59c0                       Options.enable_blob_files: false
2025/07/26-18:06:43.459015 59c0                           Options.min_blob_size: 0
2025/07/26-18:06:43.459017 59c0                          Options.blob_file_size: 268435456
2025/07/26-18:06:43.459019 59c0                   Options.blob_compression_type: NoCompression
2025/07/26-18:06:43.459020 59c0          Options.enable_blob_garbage_collection: false
2025/07/26-18:06:43.459022 59c0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:06:43.459024 59c0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:06:43.459026 59c0          Options.blob_compaction_readahead_size: 0
2025/07/26-18:06:43.459028 59c0                Options.blob_file_starting_level: 0
2025/07/26-18:06:43.459029 59c0         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:06:43.459031 59c0            Options.memtable_max_range_deletions: 0
2025/07/26-18:06:43.459158 59c0 [db/db_impl/db_impl.cc:3637] Created column family [raft_state] (ID 2)
2025/07/26-18:06:43.461742 59c0 [db/column_family.cc:618] --------------- Options for column family [task_versions]:
2025/07/26-18:06:43.461755 59c0               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:06:43.461758 59c0           Options.merge_operator: None
2025/07/26-18:06:43.461760 59c0        Options.compaction_filter: None
2025/07/26-18:06:43.461762 59c0        Options.compaction_filter_factory: None
2025/07/26-18:06:43.461765 59c0  Options.sst_partitioner_factory: None
2025/07/26-18:06:43.461767 59c0         Options.memtable_factory: SkipListFactory
2025/07/26-18:06:43.461769 59c0            Options.table_factory: BlockBasedTable
2025/07/26-18:06:43.461794 59c0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000020FA0517790)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000020FA04DD330
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:06:43.461797 59c0        Options.write_buffer_size: 67108864
2025/07/26-18:06:43.461799 59c0  Options.max_write_buffer_number: 2
2025/07/26-18:06:43.461802 59c0          Options.compression: Snappy
2025/07/26-18:06:43.461804 59c0                  Options.bottommost_compression: Disabled
2025/07/26-18:06:43.461806 59c0       Options.prefix_extractor: nullptr
2025/07/26-18:06:43.461808 59c0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:06:43.461811 59c0             Options.num_levels: 7
2025/07/26-18:06:43.461813 59c0        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:06:43.461815 59c0     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:06:43.461817 59c0     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:06:43.461819 59c0            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:06:43.461824 59c0                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:06:43.461827 59c0               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:06:43.461830 59c0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:06:43.461832 59c0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:06:43.461834 59c0         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:06:43.461836 59c0                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:06:43.461839 59c0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:06:43.461841 59c0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:06:43.461843 59c0            Options.compression_opts.window_bits: -14
2025/07/26-18:06:43.461846 59c0                  Options.compression_opts.level: 32767
2025/07/26-18:06:43.461848 59c0               Options.compression_opts.strategy: 0
2025/07/26-18:06:43.461850 59c0         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:06:43.461852 59c0         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:06:43.461855 59c0         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:06:43.461857 59c0         Options.compression_opts.parallel_threads: 1
2025/07/26-18:06:43.461859 59c0                  Options.compression_opts.enabled: false
2025/07/26-18:06:43.461861 59c0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:06:43.461863 59c0      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:06:43.461866 59c0          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:06:43.461868 59c0              Options.level0_stop_writes_trigger: 36
2025/07/26-18:06:43.461870 59c0                   Options.target_file_size_base: 67108864
2025/07/26-18:06:43.461872 59c0             Options.target_file_size_multiplier: 1
2025/07/26-18:06:43.461874 59c0                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:06:43.461877 59c0 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:06:43.461879 59c0          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:06:43.461882 59c0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:06:43.461884 59c0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:06:43.461886 59c0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:06:43.461889 59c0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:06:43.461891 59c0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:06:43.461893 59c0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:06:43.461895 59c0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:06:43.461897 59c0       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:06:43.461899 59c0                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:06:43.461902 59c0   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:06:43.461904 59c0                        Options.arena_block_size: 1048576
2025/07/26-18:06:43.461906 59c0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:06:43.461908 59c0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:06:43.461911 59c0                Options.disable_auto_compactions: 0
2025/07/26-18:06:43.461913 59c0                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:06:43.461916 59c0                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:06:43.461919 59c0 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:06:43.461921 59c0 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:06:43.461923 59c0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:06:43.461926 59c0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:06:43.461928 59c0 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:06:43.461931 59c0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:06:43.461934 59c0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:06:43.461936 59c0 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:06:43.461941 59c0                   Options.table_properties_collectors: 
2025/07/26-18:06:43.461944 59c0                   Options.inplace_update_support: 0
2025/07/26-18:06:43.461946 59c0                 Options.inplace_update_num_locks: 10000
2025/07/26-18:06:43.461947 59c0               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:06:43.461949 59c0               Options.memtable_whole_key_filtering: 0
2025/07/26-18:06:43.461951 59c0   Options.memtable_huge_page_size: 0
2025/07/26-18:06:43.461953 59c0                           Options.bloom_locality: 0
2025/07/26-18:06:43.461955 59c0                    Options.max_successive_merges: 0
2025/07/26-18:06:43.461956 59c0                Options.optimize_filters_for_hits: 0
2025/07/26-18:06:43.461958 59c0                Options.paranoid_file_checks: 0
2025/07/26-18:06:43.461960 59c0                Options.force_consistency_checks: 1
2025/07/26-18:06:43.461962 59c0                Options.report_bg_io_stats: 0
2025/07/26-18:06:43.461964 59c0                               Options.ttl: 2592000
2025/07/26-18:06:43.461965 59c0          Options.periodic_compaction_seconds: 0
2025/07/26-18:06:43.461967 59c0                        Options.default_temperature: kUnknown
2025/07/26-18:06:43.461969 59c0  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:06:43.461971 59c0    Options.preserve_internal_time_seconds: 0
2025/07/26-18:06:43.461973 59c0                       Options.enable_blob_files: false
2025/07/26-18:06:43.461974 59c0                           Options.min_blob_size: 0
2025/07/26-18:06:43.461976 59c0                          Options.blob_file_size: 268435456
2025/07/26-18:06:43.461978 59c0                   Options.blob_compression_type: NoCompression
2025/07/26-18:06:43.461980 59c0          Options.enable_blob_garbage_collection: false
2025/07/26-18:06:43.461982 59c0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:06:43.461984 59c0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:06:43.461986 59c0          Options.blob_compaction_readahead_size: 0
2025/07/26-18:06:43.461987 59c0                Options.blob_file_starting_level: 0
2025/07/26-18:06:43.461989 59c0         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:06:43.461991 59c0            Options.memtable_max_range_deletions: 0
2025/07/26-18:06:43.462143 59c0 [db/db_impl/db_impl.cc:3637] Created column family [task_versions] (ID 3)
2025/07/26-18:06:43.464754 59c0 [db/column_family.cc:618] --------------- Options for column family [task_index]:
2025/07/26-18:06:43.464764 59c0               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:06:43.464766 59c0           Options.merge_operator: None
2025/07/26-18:06:43.464768 59c0        Options.compaction_filter: None
2025/07/26-18:06:43.464770 59c0        Options.compaction_filter_factory: None
2025/07/26-18:06:43.464771 59c0  Options.sst_partitioner_factory: None
2025/07/26-18:06:43.464773 59c0         Options.memtable_factory: SkipListFactory
2025/07/26-18:06:43.464775 59c0            Options.table_factory: BlockBasedTable
2025/07/26-18:06:43.464793 59c0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000020FA05181E0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000020FA04DC7F0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:06:43.464797 59c0        Options.write_buffer_size: 67108864
2025/07/26-18:06:43.464800 59c0  Options.max_write_buffer_number: 2
2025/07/26-18:06:43.464801 59c0          Options.compression: Snappy
2025/07/26-18:06:43.464803 59c0                  Options.bottommost_compression: Disabled
2025/07/26-18:06:43.464805 59c0       Options.prefix_extractor: nullptr
2025/07/26-18:06:43.464807 59c0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:06:43.464809 59c0             Options.num_levels: 7
2025/07/26-18:06:43.464810 59c0        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:06:43.464812 59c0     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:06:43.464814 59c0     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:06:43.464816 59c0            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:06:43.464817 59c0                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:06:43.464819 59c0               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:06:43.464821 59c0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:06:43.464823 59c0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:06:43.464825 59c0         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:06:43.464826 59c0                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:06:43.464828 59c0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:06:43.464830 59c0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:06:43.464832 59c0            Options.compression_opts.window_bits: -14
2025/07/26-18:06:43.464833 59c0                  Options.compression_opts.level: 32767
2025/07/26-18:06:43.464835 59c0               Options.compression_opts.strategy: 0
2025/07/26-18:06:43.464837 59c0         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:06:43.464839 59c0         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:06:43.464840 59c0         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:06:43.464842 59c0         Options.compression_opts.parallel_threads: 1
2025/07/26-18:06:43.464844 59c0                  Options.compression_opts.enabled: false
2025/07/26-18:06:43.464846 59c0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:06:43.464847 59c0      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:06:43.464849 59c0          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:06:43.464851 59c0              Options.level0_stop_writes_trigger: 36
2025/07/26-18:06:43.464853 59c0                   Options.target_file_size_base: 67108864
2025/07/26-18:06:43.464854 59c0             Options.target_file_size_multiplier: 1
2025/07/26-18:06:43.464856 59c0                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:06:43.464858 59c0 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:06:43.464860 59c0          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:06:43.464862 59c0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:06:43.464864 59c0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:06:43.464865 59c0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:06:43.464867 59c0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:06:43.464869 59c0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:06:43.464871 59c0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:06:43.464873 59c0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:06:43.464875 59c0       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:06:43.464877 59c0                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:06:43.464878 59c0   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:06:43.464880 59c0                        Options.arena_block_size: 1048576
2025/07/26-18:06:43.464882 59c0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:06:43.464884 59c0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:06:43.464885 59c0                Options.disable_auto_compactions: 0
2025/07/26-18:06:43.464888 59c0                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:06:43.464890 59c0                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:06:43.464891 59c0 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:06:43.464893 59c0 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:06:43.464895 59c0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:06:43.464897 59c0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:06:43.464898 59c0 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:06:43.464901 59c0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:06:43.464902 59c0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:06:43.464904 59c0 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:06:43.464907 59c0                   Options.table_properties_collectors: 
2025/07/26-18:06:43.464909 59c0                   Options.inplace_update_support: 0
2025/07/26-18:06:43.464910 59c0                 Options.inplace_update_num_locks: 10000
2025/07/26-18:06:43.464912 59c0               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:06:43.464914 59c0               Options.memtable_whole_key_filtering: 0
2025/07/26-18:06:43.464916 59c0   Options.memtable_huge_page_size: 0
2025/07/26-18:06:43.464918 59c0                           Options.bloom_locality: 0
2025/07/26-18:06:43.464919 59c0                    Options.max_successive_merges: 0
2025/07/26-18:06:43.464921 59c0                Options.optimize_filters_for_hits: 0
2025/07/26-18:06:43.464923 59c0                Options.paranoid_file_checks: 0
2025/07/26-18:06:43.464925 59c0                Options.force_consistency_checks: 1
2025/07/26-18:06:43.464926 59c0                Options.report_bg_io_stats: 0
2025/07/26-18:06:43.464928 59c0                               Options.ttl: 2592000
2025/07/26-18:06:43.464930 59c0          Options.periodic_compaction_seconds: 0
2025/07/26-18:06:43.464932 59c0                        Options.default_temperature: kUnknown
2025/07/26-18:06:43.464933 59c0  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:06:43.464935 59c0    Options.preserve_internal_time_seconds: 0
2025/07/26-18:06:43.464937 59c0                       Options.enable_blob_files: false
2025/07/26-18:06:43.464939 59c0                           Options.min_blob_size: 0
2025/07/26-18:06:43.464940 59c0                          Options.blob_file_size: 268435456
2025/07/26-18:06:43.464942 59c0                   Options.blob_compression_type: NoCompression
2025/07/26-18:06:43.464944 59c0          Options.enable_blob_garbage_collection: false
2025/07/26-18:06:43.464946 59c0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:06:43.464948 59c0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:06:43.464950 59c0          Options.blob_compaction_readahead_size: 0
2025/07/26-18:06:43.464952 59c0                Options.blob_file_starting_level: 0
2025/07/26-18:06:43.464953 59c0         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:06:43.464956 59c0            Options.memtable_max_range_deletions: 0
2025/07/26-18:06:43.465104 59c0 [db/db_impl/db_impl.cc:3637] Created column family [task_index] (ID 4)
2025/07/26-18:06:43.471199 59c0 [db/db_impl/db_impl_open.cc:2157] SstFileManager instance 0000020FA2125010
2025/07/26-18:06:43.471399 59c0 DB pointer 0000020FA2126C80
2025/07/26-18:06:43.471861 5078 [db/db_impl/db_impl.cc:1140] ------- DUMPING STATS -------
2025/07/26-18:06:43.471870 5078 [db/db_impl/db_impl.cc:1141] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000020FA04DCF70#8068 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 3.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** Compaction Stats [tasks] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [tasks] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000020FA04DCAC0#8068 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [tasks] **

** Compaction Stats [raft_state] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [raft_state] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000020FA04DD150#8068 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [raft_state] **

** Compaction Stats [task_versions] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_versions] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000020FA04DD330#8068 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 1.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_versions] **

** Compaction Stats [task_index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000020FA04DC7F0#8068 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_index] **
