2025/07/26-18:25:02.999714 6d24 RocksDB version: 8.10.0
2025/07/26-18:25:03.000307 6d24 Compile date 2023-12-15 13:01:14
2025/07/26-18:25:03.000317 6d24 DB SUMMARY
2025/07/26-18:25:03.000324 6d24 Host name (Env):  DESKTOP-RNBFHG8
2025/07/26-18:25:03.000329 6d24 DB Session ID:  3O7M7QKMR39K54HAMUYF
2025/07/26-18:25:03.000476 6d24 SST files in ./data/12D3KooWCQxm2Y3Bs7wS2Jd2D9EgbsAxWkMnLY7bSWkGHRHVKV6A dir, Total Num: 0, files: 
2025/07/26-18:25:03.000482 6d24 Write Ahead Log file in ./data/12D3KooWCQxm2Y3Bs7wS2Jd2D9EgbsAxWkMnLY7bSWkGHRHVKV6A: 
2025/07/26-18:25:03.000488 6d24                         Options.error_if_exists: 0
2025/07/26-18:25:03.000493 6d24                       Options.create_if_missing: 1
2025/07/26-18:25:03.000498 6d24                         Options.paranoid_checks: 1
2025/07/26-18:25:03.004545 6d24             Options.flush_verify_memtable_count: 1
2025/07/26-18:25:03.004558 6d24          Options.compaction_verify_record_count: 1
2025/07/26-18:25:03.004560 6d24                               Options.track_and_verify_wals_in_manifest: 0
2025/07/26-18:25:03.004562 6d24        Options.verify_sst_unique_id_in_manifest: 1
2025/07/26-18:25:03.004564 6d24                                     Options.env: 0000018CE6F057B0
2025/07/26-18:25:03.004566 6d24                                      Options.fs: WinFS
2025/07/26-18:25:03.004568 6d24                                Options.info_log: 0000018CE6E973E0
2025/07/26-18:25:03.004570 6d24                Options.max_file_opening_threads: 16
2025/07/26-18:25:03.004572 6d24                              Options.statistics: 0000000000000000
2025/07/26-18:25:03.004574 6d24                               Options.use_fsync: 0
2025/07/26-18:25:03.004575 6d24                       Options.max_log_file_size: 0
2025/07/26-18:25:03.004577 6d24                  Options.max_manifest_file_size: 1073741824
2025/07/26-18:25:03.004581 6d24                   Options.log_file_time_to_roll: 0
2025/07/26-18:25:03.004583 6d24                       Options.keep_log_file_num: 1000
2025/07/26-18:25:03.004585 6d24                    Options.recycle_log_file_num: 0
2025/07/26-18:25:03.004587 6d24                         Options.allow_fallocate: 1
2025/07/26-18:25:03.004589 6d24                        Options.allow_mmap_reads: 0
2025/07/26-18:25:03.004592 6d24                       Options.allow_mmap_writes: 0
2025/07/26-18:25:03.004595 6d24                        Options.use_direct_reads: 0
2025/07/26-18:25:03.004597 6d24                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/26-18:25:03.004599 6d24          Options.create_missing_column_families: 1
2025/07/26-18:25:03.004601 6d24                              Options.db_log_dir: 
2025/07/26-18:25:03.004603 6d24                                 Options.wal_dir: 
2025/07/26-18:25:03.004606 6d24                Options.table_cache_numshardbits: 6
2025/07/26-18:25:03.004608 6d24                         Options.WAL_ttl_seconds: 0
2025/07/26-18:25:03.004610 6d24                       Options.WAL_size_limit_MB: 0
2025/07/26-18:25:03.004612 6d24                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/26-18:25:03.004613 6d24             Options.manifest_preallocation_size: 4194304
2025/07/26-18:25:03.004615 6d24                     Options.is_fd_close_on_exec: 1
2025/07/26-18:25:03.004617 6d24                   Options.advise_random_on_open: 1
2025/07/26-18:25:03.004618 6d24                    Options.db_write_buffer_size: 0
2025/07/26-18:25:03.004620 6d24                    Options.write_buffer_manager: 0000018CE6F04D60
2025/07/26-18:25:03.004622 6d24         Options.access_hint_on_compaction_start: 1
2025/07/26-18:25:03.004623 6d24           Options.random_access_max_buffer_size: 1048576
2025/07/26-18:25:03.004625 6d24                      Options.use_adaptive_mutex: 0
2025/07/26-18:25:03.004627 6d24                            Options.rate_limiter: 0000000000000000
2025/07/26-18:25:03.004629 6d24     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/26-18:25:03.004631 6d24                       Options.wal_recovery_mode: 2
2025/07/26-18:25:03.004664 6d24                  Options.enable_thread_tracking: 0
2025/07/26-18:25:03.004668 6d24                  Options.enable_pipelined_write: 0
2025/07/26-18:25:03.004670 6d24                  Options.unordered_write: 0
2025/07/26-18:25:03.004672 6d24         Options.allow_concurrent_memtable_write: 1
2025/07/26-18:25:03.004674 6d24      Options.enable_write_thread_adaptive_yield: 1
2025/07/26-18:25:03.004675 6d24             Options.write_thread_max_yield_usec: 100
2025/07/26-18:25:03.004677 6d24            Options.write_thread_slow_yield_usec: 3
2025/07/26-18:25:03.004679 6d24                               Options.row_cache: None
2025/07/26-18:25:03.004680 6d24                              Options.wal_filter: None
2025/07/26-18:25:03.004682 6d24             Options.avoid_flush_during_recovery: 0
2025/07/26-18:25:03.004684 6d24             Options.allow_ingest_behind: 0
2025/07/26-18:25:03.004685 6d24             Options.two_write_queues: 0
2025/07/26-18:25:03.004687 6d24             Options.manual_wal_flush: 0
2025/07/26-18:25:03.004689 6d24             Options.wal_compression: 0
2025/07/26-18:25:03.004690 6d24             Options.atomic_flush: 0
2025/07/26-18:25:03.004692 6d24             Options.avoid_unnecessary_blocking_io: 0
2025/07/26-18:25:03.004693 6d24                 Options.persist_stats_to_disk: 0
2025/07/26-18:25:03.004695 6d24                 Options.write_dbid_to_manifest: 0
2025/07/26-18:25:03.004697 6d24                 Options.log_readahead_size: 0
2025/07/26-18:25:03.004698 6d24                 Options.file_checksum_gen_factory: Unknown
2025/07/26-18:25:03.004700 6d24                 Options.best_efforts_recovery: 0
2025/07/26-18:25:03.004702 6d24                Options.max_bgerror_resume_count: 2147483647
2025/07/26-18:25:03.004703 6d24            Options.bgerror_resume_retry_interval: 1000000
2025/07/26-18:25:03.004705 6d24             Options.allow_data_in_errors: 0
2025/07/26-18:25:03.004707 6d24             Options.db_host_id: __hostname__
2025/07/26-18:25:03.004708 6d24             Options.enforce_single_del_contracts: true
2025/07/26-18:25:03.004710 6d24             Options.max_background_jobs: 4
2025/07/26-18:25:03.004712 6d24             Options.max_background_compactions: -1
2025/07/26-18:25:03.004714 6d24             Options.max_subcompactions: 1
2025/07/26-18:25:03.004715 6d24             Options.avoid_flush_during_shutdown: 0
2025/07/26-18:25:03.004717 6d24           Options.writable_file_max_buffer_size: 1048576
2025/07/26-18:25:03.004719 6d24             Options.delayed_write_rate : 16777216
2025/07/26-18:25:03.004720 6d24             Options.max_total_wal_size: 0
2025/07/26-18:25:03.004722 6d24             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/26-18:25:03.004724 6d24                   Options.stats_dump_period_sec: 600
2025/07/26-18:25:03.004725 6d24                 Options.stats_persist_period_sec: 600
2025/07/26-18:25:03.004727 6d24                 Options.stats_history_buffer_size: 1048576
2025/07/26-18:25:03.004729 6d24                          Options.max_open_files: 1000
2025/07/26-18:25:03.004731 6d24                          Options.bytes_per_sync: 0
2025/07/26-18:25:03.004732 6d24                      Options.wal_bytes_per_sync: 0
2025/07/26-18:25:03.004734 6d24                   Options.strict_bytes_per_sync: 0
2025/07/26-18:25:03.004736 6d24       Options.compaction_readahead_size: 2097152
2025/07/26-18:25:03.004737 6d24                  Options.max_background_flushes: -1
2025/07/26-18:25:03.004739 6d24 Options.daily_offpeak_time_utc: 
2025/07/26-18:25:03.004740 6d24 Compression algorithms supported:
2025/07/26-18:25:03.004743 6d24 	kZSTD supported: 1
2025/07/26-18:25:03.004745 6d24 	kSnappyCompression supported: 1
2025/07/26-18:25:03.004747 6d24 	kBZip2Compression supported: 1
2025/07/26-18:25:03.004748 6d24 	kZlibCompression supported: 1
2025/07/26-18:25:03.004750 6d24 	kLZ4Compression supported: 1
2025/07/26-18:25:03.004752 6d24 	kXpressCompression supported: 0
2025/07/26-18:25:03.004753 6d24 	kLZ4HCCompression supported: 1
2025/07/26-18:25:03.004765 6d24 	kZSTDNotFinalCompression supported: 1
2025/07/26-18:25:03.004770 6d24 Fast CRC32 supported: Not supported on x86
2025/07/26-18:25:03.004772 6d24 DMutex implementation: std::mutex
2025/07/26-18:25:03.011864 6d24 [db/db_impl/db_impl_open.cc:325] Creating manifest 1 
2025/07/26-18:25:03.021929 6d24 [db/version_set.cc:5942] Recovering from manifest file: ./data/12D3KooWCQxm2Y3Bs7wS2Jd2D9EgbsAxWkMnLY7bSWkGHRHVKV6A/MANIFEST-000001
2025/07/26-18:25:03.022058 6d24 [db/column_family.cc:618] --------------- Options for column family [default]:
2025/07/26-18:25:03.022063 6d24               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:25:03.022065 6d24           Options.merge_operator: None
2025/07/26-18:25:03.022067 6d24        Options.compaction_filter: None
2025/07/26-18:25:03.022069 6d24        Options.compaction_filter_factory: None
2025/07/26-18:25:03.022070 6d24  Options.sst_partitioner_factory: None
2025/07/26-18:25:03.022072 6d24         Options.memtable_factory: SkipListFactory
2025/07/26-18:25:03.022073 6d24            Options.table_factory: BlockBasedTable
2025/07/26-18:25:03.022106 6d24            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000018CE6ED2990)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000018CE6F05220
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:25:03.022108 6d24        Options.write_buffer_size: 67108864
2025/07/26-18:25:03.022110 6d24  Options.max_write_buffer_number: 2
2025/07/26-18:25:03.022112 6d24          Options.compression: Snappy
2025/07/26-18:25:03.022114 6d24                  Options.bottommost_compression: Disabled
2025/07/26-18:25:03.022115 6d24       Options.prefix_extractor: nullptr
2025/07/26-18:25:03.022117 6d24   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:25:03.022119 6d24             Options.num_levels: 7
2025/07/26-18:25:03.022120 6d24        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:25:03.022122 6d24     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:25:03.022123 6d24     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:25:03.022125 6d24            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:25:03.022127 6d24                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:25:03.022129 6d24               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:25:03.022130 6d24         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:25:03.022142 6d24         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:25:03.022144 6d24         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:25:03.022146 6d24                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:25:03.022149 6d24         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:25:03.022151 6d24         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:25:03.022152 6d24            Options.compression_opts.window_bits: -14
2025/07/26-18:25:03.022156 6d24                  Options.compression_opts.level: 32767
2025/07/26-18:25:03.022158 6d24               Options.compression_opts.strategy: 0
2025/07/26-18:25:03.022160 6d24         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:25:03.022162 6d24         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:25:03.022164 6d24         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:25:03.022166 6d24         Options.compression_opts.parallel_threads: 1
2025/07/26-18:25:03.022167 6d24                  Options.compression_opts.enabled: false
2025/07/26-18:25:03.022169 6d24         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:25:03.022171 6d24      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:25:03.022172 6d24          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:25:03.022174 6d24              Options.level0_stop_writes_trigger: 36
2025/07/26-18:25:03.022176 6d24                   Options.target_file_size_base: 67108864
2025/07/26-18:25:03.022178 6d24             Options.target_file_size_multiplier: 1
2025/07/26-18:25:03.022179 6d24                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:25:03.022181 6d24 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:25:03.022183 6d24          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:25:03.022185 6d24 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:25:03.022186 6d24 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:25:03.022188 6d24 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:25:03.022190 6d24 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:25:03.022191 6d24 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:25:03.022193 6d24 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:25:03.022195 6d24 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:25:03.022196 6d24       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:25:03.022198 6d24                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:25:03.022200 6d24   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:25:03.022201 6d24                        Options.arena_block_size: 1048576
2025/07/26-18:25:03.022203 6d24   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:25:03.022205 6d24   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:25:03.022206 6d24                Options.disable_auto_compactions: 0
2025/07/26-18:25:03.022209 6d24                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:25:03.022211 6d24                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:25:03.022213 6d24 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:25:03.022215 6d24 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:25:03.022216 6d24 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:25:03.022218 6d24 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:25:03.022220 6d24 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:25:03.022222 6d24 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:25:03.022224 6d24 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:25:03.022225 6d24 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:25:03.022228 6d24                   Options.table_properties_collectors: 
2025/07/26-18:25:03.022230 6d24                   Options.inplace_update_support: 0
2025/07/26-18:25:03.022232 6d24                 Options.inplace_update_num_locks: 10000
2025/07/26-18:25:03.022233 6d24               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:25:03.022235 6d24               Options.memtable_whole_key_filtering: 0
2025/07/26-18:25:03.022237 6d24   Options.memtable_huge_page_size: 0
2025/07/26-18:25:03.022262 6d24                           Options.bloom_locality: 0
2025/07/26-18:25:03.022265 6d24                    Options.max_successive_merges: 0
2025/07/26-18:25:03.022266 6d24                Options.optimize_filters_for_hits: 0
2025/07/26-18:25:03.022268 6d24                Options.paranoid_file_checks: 0
2025/07/26-18:25:03.022270 6d24                Options.force_consistency_checks: 1
2025/07/26-18:25:03.022271 6d24                Options.report_bg_io_stats: 0
2025/07/26-18:25:03.022273 6d24                               Options.ttl: 2592000
2025/07/26-18:25:03.022274 6d24          Options.periodic_compaction_seconds: 0
2025/07/26-18:25:03.022276 6d24                        Options.default_temperature: kUnknown
2025/07/26-18:25:03.022278 6d24  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:25:03.022280 6d24    Options.preserve_internal_time_seconds: 0
2025/07/26-18:25:03.022281 6d24                       Options.enable_blob_files: false
2025/07/26-18:25:03.022283 6d24                           Options.min_blob_size: 0
2025/07/26-18:25:03.022284 6d24                          Options.blob_file_size: 268435456
2025/07/26-18:25:03.022286 6d24                   Options.blob_compression_type: NoCompression
2025/07/26-18:25:03.022288 6d24          Options.enable_blob_garbage_collection: false
2025/07/26-18:25:03.022290 6d24      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:25:03.022292 6d24 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:25:03.022293 6d24          Options.blob_compaction_readahead_size: 0
2025/07/26-18:25:03.022295 6d24                Options.blob_file_starting_level: 0
2025/07/26-18:25:03.022297 6d24         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:25:03.022298 6d24            Options.memtable_max_range_deletions: 0
2025/07/26-18:25:03.023027 6d24 [db/version_set.cc:5993] Recovered from manifest file:./data/12D3KooWCQxm2Y3Bs7wS2Jd2D9EgbsAxWkMnLY7bSWkGHRHVKV6A/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/07/26-18:25:03.023035 6d24 [db/version_set.cc:6002] Column family [default] (ID 0), log number is 0
2025/07/26-18:25:03.023237 6d24 [db/db_impl/db_impl_open.cc:646] DB ID: ca044870-6a0a-11f0-98b6-d4e98a1a402d
2025/07/26-18:25:03.024117 6d24 [db/version_set.cc:5439] Creating manifest 5
2025/07/26-18:25:03.035298 6d24 [db/column_family.cc:618] --------------- Options for column family [tasks]:
2025/07/26-18:25:03.035311 6d24               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:25:03.035314 6d24           Options.merge_operator: None
2025/07/26-18:25:03.035316 6d24        Options.compaction_filter: None
2025/07/26-18:25:03.035318 6d24        Options.compaction_filter_factory: None
2025/07/26-18:25:03.035319 6d24  Options.sst_partitioner_factory: None
2025/07/26-18:25:03.035321 6d24         Options.memtable_factory: SkipListFactory
2025/07/26-18:25:03.035323 6d24            Options.table_factory: BlockBasedTable
2025/07/26-18:25:03.035343 6d24            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000018CE6ED8160)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000018CE6F04F50
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:25:03.035349 6d24        Options.write_buffer_size: 67108864
2025/07/26-18:25:03.035352 6d24  Options.max_write_buffer_number: 2
2025/07/26-18:25:03.035354 6d24          Options.compression: Snappy
2025/07/26-18:25:03.035355 6d24                  Options.bottommost_compression: Disabled
2025/07/26-18:25:03.035357 6d24       Options.prefix_extractor: nullptr
2025/07/26-18:25:03.035359 6d24   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:25:03.035361 6d24             Options.num_levels: 7
2025/07/26-18:25:03.035362 6d24        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:25:03.035364 6d24     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:25:03.035365 6d24     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:25:03.035367 6d24            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:25:03.035369 6d24                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:25:03.035371 6d24               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:25:03.035372 6d24         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:25:03.035374 6d24         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:25:03.035376 6d24         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:25:03.035377 6d24                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:25:03.035379 6d24         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:25:03.035381 6d24         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:25:03.035383 6d24            Options.compression_opts.window_bits: -14
2025/07/26-18:25:03.035384 6d24                  Options.compression_opts.level: 32767
2025/07/26-18:25:03.035386 6d24               Options.compression_opts.strategy: 0
2025/07/26-18:25:03.035388 6d24         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:25:03.035389 6d24         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:25:03.035391 6d24         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:25:03.035392 6d24         Options.compression_opts.parallel_threads: 1
2025/07/26-18:25:03.035394 6d24                  Options.compression_opts.enabled: false
2025/07/26-18:25:03.035396 6d24         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:25:03.035397 6d24      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:25:03.035399 6d24          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:25:03.035401 6d24              Options.level0_stop_writes_trigger: 36
2025/07/26-18:25:03.035403 6d24                   Options.target_file_size_base: 67108864
2025/07/26-18:25:03.035404 6d24             Options.target_file_size_multiplier: 1
2025/07/26-18:25:03.035406 6d24                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:25:03.035408 6d24 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:25:03.035409 6d24          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:25:03.035415 6d24 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:25:03.035417 6d24 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:25:03.035418 6d24 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:25:03.035420 6d24 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:25:03.035422 6d24 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:25:03.035423 6d24 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:25:03.035425 6d24 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:25:03.035427 6d24       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:25:03.035428 6d24                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:25:03.035431 6d24   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:25:03.035433 6d24                        Options.arena_block_size: 1048576
2025/07/26-18:25:03.035434 6d24   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:25:03.035436 6d24   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:25:03.035438 6d24                Options.disable_auto_compactions: 0
2025/07/26-18:25:03.035440 6d24                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:25:03.035442 6d24                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:25:03.035444 6d24 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:25:03.035446 6d24 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:25:03.035447 6d24 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:25:03.035449 6d24 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:25:03.035451 6d24 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:25:03.035453 6d24 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:25:03.035454 6d24 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:25:03.035456 6d24 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:25:03.035460 6d24                   Options.table_properties_collectors: 
2025/07/26-18:25:03.035462 6d24                   Options.inplace_update_support: 0
2025/07/26-18:25:03.035464 6d24                 Options.inplace_update_num_locks: 10000
2025/07/26-18:25:03.035465 6d24               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:25:03.035467 6d24               Options.memtable_whole_key_filtering: 0
2025/07/26-18:25:03.035469 6d24   Options.memtable_huge_page_size: 0
2025/07/26-18:25:03.035470 6d24                           Options.bloom_locality: 0
2025/07/26-18:25:03.035472 6d24                    Options.max_successive_merges: 0
2025/07/26-18:25:03.035474 6d24                Options.optimize_filters_for_hits: 0
2025/07/26-18:25:03.035475 6d24                Options.paranoid_file_checks: 0
2025/07/26-18:25:03.035477 6d24                Options.force_consistency_checks: 1
2025/07/26-18:25:03.035479 6d24                Options.report_bg_io_stats: 0
2025/07/26-18:25:03.035480 6d24                               Options.ttl: 2592000
2025/07/26-18:25:03.035482 6d24          Options.periodic_compaction_seconds: 0
2025/07/26-18:25:03.035484 6d24                        Options.default_temperature: kUnknown
2025/07/26-18:25:03.035486 6d24  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:25:03.035487 6d24    Options.preserve_internal_time_seconds: 0
2025/07/26-18:25:03.035489 6d24                       Options.enable_blob_files: false
2025/07/26-18:25:03.035491 6d24                           Options.min_blob_size: 0
2025/07/26-18:25:03.035492 6d24                          Options.blob_file_size: 268435456
2025/07/26-18:25:03.035494 6d24                   Options.blob_compression_type: NoCompression
2025/07/26-18:25:03.035496 6d24          Options.enable_blob_garbage_collection: false
2025/07/26-18:25:03.035498 6d24      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:25:03.035499 6d24 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:25:03.035501 6d24          Options.blob_compaction_readahead_size: 0
2025/07/26-18:25:03.035503 6d24                Options.blob_file_starting_level: 0
2025/07/26-18:25:03.035505 6d24         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:25:03.035506 6d24            Options.memtable_max_range_deletions: 0
2025/07/26-18:25:03.035681 6d24 [db/db_impl/db_impl.cc:3637] Created column family [tasks] (ID 1)
2025/07/26-18:25:03.038153 6d24 [db/column_family.cc:618] --------------- Options for column family [raft_state]:
2025/07/26-18:25:03.038158 6d24               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:25:03.038162 6d24           Options.merge_operator: None
2025/07/26-18:25:03.038164 6d24        Options.compaction_filter: None
2025/07/26-18:25:03.038166 6d24        Options.compaction_filter_factory: None
2025/07/26-18:25:03.038167 6d24  Options.sst_partitioner_factory: None
2025/07/26-18:25:03.038169 6d24         Options.memtable_factory: SkipListFactory
2025/07/26-18:25:03.038171 6d24            Options.table_factory: BlockBasedTable
2025/07/26-18:25:03.038187 6d24            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000018CE6ED7BC0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000018CE6F05310
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:25:03.038191 6d24        Options.write_buffer_size: 67108864
2025/07/26-18:25:03.038192 6d24  Options.max_write_buffer_number: 2
2025/07/26-18:25:03.038194 6d24          Options.compression: Snappy
2025/07/26-18:25:03.038196 6d24                  Options.bottommost_compression: Disabled
2025/07/26-18:25:03.038197 6d24       Options.prefix_extractor: nullptr
2025/07/26-18:25:03.038199 6d24   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:25:03.038201 6d24             Options.num_levels: 7
2025/07/26-18:25:03.038202 6d24        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:25:03.038204 6d24     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:25:03.038206 6d24     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:25:03.038207 6d24            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:25:03.038209 6d24                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:25:03.038211 6d24               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:25:03.038212 6d24         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:25:03.038214 6d24         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:25:03.038216 6d24         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:25:03.038217 6d24                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:25:03.038219 6d24         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:25:03.038221 6d24         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:25:03.038223 6d24            Options.compression_opts.window_bits: -14
2025/07/26-18:25:03.038224 6d24                  Options.compression_opts.level: 32767
2025/07/26-18:25:03.038226 6d24               Options.compression_opts.strategy: 0
2025/07/26-18:25:03.038227 6d24         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:25:03.038229 6d24         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:25:03.038231 6d24         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:25:03.038232 6d24         Options.compression_opts.parallel_threads: 1
2025/07/26-18:25:03.038234 6d24                  Options.compression_opts.enabled: false
2025/07/26-18:25:03.038262 6d24         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:25:03.038264 6d24      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:25:03.038266 6d24          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:25:03.038268 6d24              Options.level0_stop_writes_trigger: 36
2025/07/26-18:25:03.038269 6d24                   Options.target_file_size_base: 67108864
2025/07/26-18:25:03.038271 6d24             Options.target_file_size_multiplier: 1
2025/07/26-18:25:03.038273 6d24                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:25:03.038274 6d24 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:25:03.038276 6d24          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:25:03.038278 6d24 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:25:03.038280 6d24 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:25:03.038281 6d24 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:25:03.038283 6d24 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:25:03.038285 6d24 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:25:03.038286 6d24 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:25:03.038288 6d24 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:25:03.038290 6d24       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:25:03.038291 6d24                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:25:03.038293 6d24   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:25:03.038295 6d24                        Options.arena_block_size: 1048576
2025/07/26-18:25:03.038296 6d24   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:25:03.038298 6d24   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:25:03.038300 6d24                Options.disable_auto_compactions: 0
2025/07/26-18:25:03.038302 6d24                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:25:03.038304 6d24                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:25:03.038305 6d24 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:25:03.038307 6d24 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:25:03.038309 6d24 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:25:03.038311 6d24 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:25:03.038312 6d24 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:25:03.038314 6d24 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:25:03.038316 6d24 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:25:03.038318 6d24 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:25:03.038320 6d24                   Options.table_properties_collectors: 
2025/07/26-18:25:03.038322 6d24                   Options.inplace_update_support: 0
2025/07/26-18:25:03.038324 6d24                 Options.inplace_update_num_locks: 10000
2025/07/26-18:25:03.038325 6d24               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:25:03.038327 6d24               Options.memtable_whole_key_filtering: 0
2025/07/26-18:25:03.038329 6d24   Options.memtable_huge_page_size: 0
2025/07/26-18:25:03.038330 6d24                           Options.bloom_locality: 0
2025/07/26-18:25:03.038332 6d24                    Options.max_successive_merges: 0
2025/07/26-18:25:03.038334 6d24                Options.optimize_filters_for_hits: 0
2025/07/26-18:25:03.038335 6d24                Options.paranoid_file_checks: 0
2025/07/26-18:25:03.038337 6d24                Options.force_consistency_checks: 1
2025/07/26-18:25:03.038338 6d24                Options.report_bg_io_stats: 0
2025/07/26-18:25:03.038340 6d24                               Options.ttl: 2592000
2025/07/26-18:25:03.038342 6d24          Options.periodic_compaction_seconds: 0
2025/07/26-18:25:03.038344 6d24                        Options.default_temperature: kUnknown
2025/07/26-18:25:03.038346 6d24  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:25:03.038348 6d24    Options.preserve_internal_time_seconds: 0
2025/07/26-18:25:03.038349 6d24                       Options.enable_blob_files: false
2025/07/26-18:25:03.038351 6d24                           Options.min_blob_size: 0
2025/07/26-18:25:03.038353 6d24                          Options.blob_file_size: 268435456
2025/07/26-18:25:03.038354 6d24                   Options.blob_compression_type: NoCompression
2025/07/26-18:25:03.038356 6d24          Options.enable_blob_garbage_collection: false
2025/07/26-18:25:03.038358 6d24      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:25:03.038360 6d24 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:25:03.038362 6d24          Options.blob_compaction_readahead_size: 0
2025/07/26-18:25:03.038363 6d24                Options.blob_file_starting_level: 0
2025/07/26-18:25:03.038365 6d24         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:25:03.038367 6d24            Options.memtable_max_range_deletions: 0
2025/07/26-18:25:03.038492 6d24 [db/db_impl/db_impl.cc:3637] Created column family [raft_state] (ID 2)
2025/07/26-18:25:03.041145 6d24 [db/column_family.cc:618] --------------- Options for column family [task_versions]:
2025/07/26-18:25:03.041150 6d24               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:25:03.041152 6d24           Options.merge_operator: None
2025/07/26-18:25:03.041154 6d24        Options.compaction_filter: None
2025/07/26-18:25:03.041156 6d24        Options.compaction_filter_factory: None
2025/07/26-18:25:03.041157 6d24  Options.sst_partitioner_factory: None
2025/07/26-18:25:03.041159 6d24         Options.memtable_factory: SkipListFactory
2025/07/26-18:25:03.041160 6d24            Options.table_factory: BlockBasedTable
2025/07/26-18:25:03.041176 6d24            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000018CE6ED8F10)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000018CE6F05040
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:25:03.041179 6d24        Options.write_buffer_size: 67108864
2025/07/26-18:25:03.041181 6d24  Options.max_write_buffer_number: 2
2025/07/26-18:25:03.041183 6d24          Options.compression: Snappy
2025/07/26-18:25:03.041184 6d24                  Options.bottommost_compression: Disabled
2025/07/26-18:25:03.041186 6d24       Options.prefix_extractor: nullptr
2025/07/26-18:25:03.041188 6d24   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:25:03.041189 6d24             Options.num_levels: 7
2025/07/26-18:25:03.041191 6d24        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:25:03.041193 6d24     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:25:03.041194 6d24     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:25:03.041196 6d24            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:25:03.041199 6d24                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:25:03.041201 6d24               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:25:03.041203 6d24         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:25:03.041205 6d24         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:25:03.041206 6d24         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:25:03.041208 6d24                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:25:03.041210 6d24         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:25:03.041211 6d24         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:25:03.041213 6d24            Options.compression_opts.window_bits: -14
2025/07/26-18:25:03.041215 6d24                  Options.compression_opts.level: 32767
2025/07/26-18:25:03.041216 6d24               Options.compression_opts.strategy: 0
2025/07/26-18:25:03.041218 6d24         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:25:03.041220 6d24         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:25:03.041221 6d24         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:25:03.041223 6d24         Options.compression_opts.parallel_threads: 1
2025/07/26-18:25:03.041225 6d24                  Options.compression_opts.enabled: false
2025/07/26-18:25:03.041226 6d24         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:25:03.041228 6d24      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:25:03.041230 6d24          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:25:03.041231 6d24              Options.level0_stop_writes_trigger: 36
2025/07/26-18:25:03.041233 6d24                   Options.target_file_size_base: 67108864
2025/07/26-18:25:03.041235 6d24             Options.target_file_size_multiplier: 1
2025/07/26-18:25:03.041236 6d24                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:25:03.041238 6d24 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:25:03.041240 6d24          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:25:03.041242 6d24 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:25:03.041243 6d24 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:25:03.041245 6d24 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:25:03.041247 6d24 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:25:03.041248 6d24 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:25:03.041250 6d24 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:25:03.041252 6d24 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:25:03.041253 6d24       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:25:03.041255 6d24                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:25:03.041257 6d24   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:25:03.041258 6d24                        Options.arena_block_size: 1048576
2025/07/26-18:25:03.041260 6d24   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:25:03.041262 6d24   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:25:03.041263 6d24                Options.disable_auto_compactions: 0
2025/07/26-18:25:03.041266 6d24                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:25:03.041268 6d24                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:25:03.041269 6d24 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:25:03.041271 6d24 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:25:03.041273 6d24 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:25:03.041274 6d24 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:25:03.041277 6d24 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:25:03.041279 6d24 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:25:03.041281 6d24 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:25:03.041283 6d24 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:25:03.041286 6d24                   Options.table_properties_collectors: 
2025/07/26-18:25:03.041288 6d24                   Options.inplace_update_support: 0
2025/07/26-18:25:03.041290 6d24                 Options.inplace_update_num_locks: 10000
2025/07/26-18:25:03.041292 6d24               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:25:03.041294 6d24               Options.memtable_whole_key_filtering: 0
2025/07/26-18:25:03.041295 6d24   Options.memtable_huge_page_size: 0
2025/07/26-18:25:03.041297 6d24                           Options.bloom_locality: 0
2025/07/26-18:25:03.041298 6d24                    Options.max_successive_merges: 0
2025/07/26-18:25:03.041300 6d24                Options.optimize_filters_for_hits: 0
2025/07/26-18:25:03.041302 6d24                Options.paranoid_file_checks: 0
2025/07/26-18:25:03.041303 6d24                Options.force_consistency_checks: 1
2025/07/26-18:25:03.041305 6d24                Options.report_bg_io_stats: 0
2025/07/26-18:25:03.041307 6d24                               Options.ttl: 2592000
2025/07/26-18:25:03.041308 6d24          Options.periodic_compaction_seconds: 0
2025/07/26-18:25:03.041310 6d24                        Options.default_temperature: kUnknown
2025/07/26-18:25:03.041312 6d24  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:25:03.041313 6d24    Options.preserve_internal_time_seconds: 0
2025/07/26-18:25:03.041315 6d24                       Options.enable_blob_files: false
2025/07/26-18:25:03.041317 6d24                           Options.min_blob_size: 0
2025/07/26-18:25:03.041318 6d24                          Options.blob_file_size: 268435456
2025/07/26-18:25:03.041320 6d24                   Options.blob_compression_type: NoCompression
2025/07/26-18:25:03.041322 6d24          Options.enable_blob_garbage_collection: false
2025/07/26-18:25:03.041323 6d24      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:25:03.041325 6d24 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:25:03.041327 6d24          Options.blob_compaction_readahead_size: 0
2025/07/26-18:25:03.041329 6d24                Options.blob_file_starting_level: 0
2025/07/26-18:25:03.041330 6d24         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:25:03.041332 6d24            Options.memtable_max_range_deletions: 0
2025/07/26-18:25:03.041451 6d24 [db/db_impl/db_impl.cc:3637] Created column family [task_versions] (ID 3)
2025/07/26-18:25:03.043125 6d24 [db/column_family.cc:618] --------------- Options for column family [task_index]:
2025/07/26-18:25:03.043131 6d24               Options.comparator: leveldb.BytewiseComparator
2025/07/26-18:25:03.043133 6d24           Options.merge_operator: None
2025/07/26-18:25:03.043134 6d24        Options.compaction_filter: None
2025/07/26-18:25:03.043136 6d24        Options.compaction_filter_factory: None
2025/07/26-18:25:03.043138 6d24  Options.sst_partitioner_factory: None
2025/07/26-18:25:03.043139 6d24         Options.memtable_factory: SkipListFactory
2025/07/26-18:25:03.043141 6d24            Options.table_factory: BlockBasedTable
2025/07/26-18:25:03.043156 6d24            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000018CE6ED8880)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000018CE6F04B90
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-18:25:03.043160 6d24        Options.write_buffer_size: 67108864
2025/07/26-18:25:03.043162 6d24  Options.max_write_buffer_number: 2
2025/07/26-18:25:03.043164 6d24          Options.compression: Snappy
2025/07/26-18:25:03.043166 6d24                  Options.bottommost_compression: Disabled
2025/07/26-18:25:03.043167 6d24       Options.prefix_extractor: nullptr
2025/07/26-18:25:03.043169 6d24   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-18:25:03.043171 6d24             Options.num_levels: 7
2025/07/26-18:25:03.043172 6d24        Options.min_write_buffer_number_to_merge: 1
2025/07/26-18:25:03.043174 6d24     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-18:25:03.043175 6d24     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-18:25:03.043177 6d24            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-18:25:03.043179 6d24                  Options.bottommost_compression_opts.level: 32767
2025/07/26-18:25:03.043180 6d24               Options.bottommost_compression_opts.strategy: 0
2025/07/26-18:25:03.043182 6d24         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-18:25:03.043184 6d24         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:25:03.043186 6d24         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-18:25:03.043187 6d24                  Options.bottommost_compression_opts.enabled: false
2025/07/26-18:25:03.043189 6d24         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:25:03.043191 6d24         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:25:03.043192 6d24            Options.compression_opts.window_bits: -14
2025/07/26-18:25:03.043194 6d24                  Options.compression_opts.level: 32767
2025/07/26-18:25:03.043196 6d24               Options.compression_opts.strategy: 0
2025/07/26-18:25:03.043197 6d24         Options.compression_opts.max_dict_bytes: 0
2025/07/26-18:25:03.043199 6d24         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-18:25:03.043201 6d24         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-18:25:03.043202 6d24         Options.compression_opts.parallel_threads: 1
2025/07/26-18:25:03.043204 6d24                  Options.compression_opts.enabled: false
2025/07/26-18:25:03.043206 6d24         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-18:25:03.043207 6d24      Options.level0_file_num_compaction_trigger: 4
2025/07/26-18:25:03.043209 6d24          Options.level0_slowdown_writes_trigger: 20
2025/07/26-18:25:03.043211 6d24              Options.level0_stop_writes_trigger: 36
2025/07/26-18:25:03.043212 6d24                   Options.target_file_size_base: 67108864
2025/07/26-18:25:03.043214 6d24             Options.target_file_size_multiplier: 1
2025/07/26-18:25:03.043215 6d24                Options.max_bytes_for_level_base: 268435456
2025/07/26-18:25:03.043217 6d24 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-18:25:03.043219 6d24          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-18:25:03.043221 6d24 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-18:25:03.043222 6d24 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-18:25:03.043224 6d24 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-18:25:03.043226 6d24 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-18:25:03.043228 6d24 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-18:25:03.043230 6d24 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-18:25:03.043232 6d24 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-18:25:03.043234 6d24       Options.max_sequential_skip_in_iterations: 8
2025/07/26-18:25:03.043235 6d24                    Options.max_compaction_bytes: 1677721600
2025/07/26-18:25:03.043237 6d24   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-18:25:03.043239 6d24                        Options.arena_block_size: 1048576
2025/07/26-18:25:03.043240 6d24   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-18:25:03.043242 6d24   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-18:25:03.043244 6d24                Options.disable_auto_compactions: 0
2025/07/26-18:25:03.043246 6d24                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-18:25:03.043247 6d24                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-18:25:03.043249 6d24 Options.compaction_options_universal.size_ratio: 1
2025/07/26-18:25:03.043251 6d24 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-18:25:03.043252 6d24 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-18:25:03.043254 6d24 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-18:25:03.043256 6d24 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-18:25:03.043258 6d24 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-18:25:03.043259 6d24 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-18:25:03.043261 6d24 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-18:25:03.043263 6d24                   Options.table_properties_collectors: 
2025/07/26-18:25:03.043265 6d24                   Options.inplace_update_support: 0
2025/07/26-18:25:03.043267 6d24                 Options.inplace_update_num_locks: 10000
2025/07/26-18:25:03.043268 6d24               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-18:25:03.043270 6d24               Options.memtable_whole_key_filtering: 0
2025/07/26-18:25:03.043272 6d24   Options.memtable_huge_page_size: 0
2025/07/26-18:25:03.043273 6d24                           Options.bloom_locality: 0
2025/07/26-18:25:03.043275 6d24                    Options.max_successive_merges: 0
2025/07/26-18:25:03.043277 6d24                Options.optimize_filters_for_hits: 0
2025/07/26-18:25:03.043278 6d24                Options.paranoid_file_checks: 0
2025/07/26-18:25:03.043280 6d24                Options.force_consistency_checks: 1
2025/07/26-18:25:03.043282 6d24                Options.report_bg_io_stats: 0
2025/07/26-18:25:03.043283 6d24                               Options.ttl: 2592000
2025/07/26-18:25:03.043285 6d24          Options.periodic_compaction_seconds: 0
2025/07/26-18:25:03.043287 6d24                        Options.default_temperature: kUnknown
2025/07/26-18:25:03.043288 6d24  Options.preclude_last_level_data_seconds: 0
2025/07/26-18:25:03.043290 6d24    Options.preserve_internal_time_seconds: 0
2025/07/26-18:25:03.043292 6d24                       Options.enable_blob_files: false
2025/07/26-18:25:03.043293 6d24                           Options.min_blob_size: 0
2025/07/26-18:25:03.043295 6d24                          Options.blob_file_size: 268435456
2025/07/26-18:25:03.043297 6d24                   Options.blob_compression_type: NoCompression
2025/07/26-18:25:03.043298 6d24          Options.enable_blob_garbage_collection: false
2025/07/26-18:25:03.043300 6d24      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-18:25:03.043302 6d24 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-18:25:03.043304 6d24          Options.blob_compaction_readahead_size: 0
2025/07/26-18:25:03.043305 6d24                Options.blob_file_starting_level: 0
2025/07/26-18:25:03.043307 6d24         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-18:25:03.043309 6d24            Options.memtable_max_range_deletions: 0
2025/07/26-18:25:03.043432 6d24 [db/db_impl/db_impl.cc:3637] Created column family [task_index] (ID 4)
2025/07/26-18:25:03.051713 6d24 [db/db_impl/db_impl_open.cc:2157] SstFileManager instance 0000018CE8A1C950
2025/07/26-18:25:03.052136 6d24 DB pointer 0000018CE8A1AD80
2025/07/26-18:25:03.052614 6634 [db/db_impl/db_impl.cc:1140] ------- DUMPING STATS -------
2025/07/26-18:25:03.052621 6634 [db/db_impl/db_impl.cc:1141] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000018CE6F05220#26800 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 4.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** Compaction Stats [tasks] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [tasks] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000018CE6F04F50#26800 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [tasks] **

** Compaction Stats [raft_state] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [raft_state] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000018CE6F05310#26800 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [raft_state] **

** Compaction Stats [task_versions] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_versions] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000018CE6F05040#26800 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 1.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_versions] **

** Compaction Stats [task_index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000018CE6F04B90#26800 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 1.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_index] **
