2025/07/26-17:57:50.570129 6880 RocksDB version: 8.10.0
2025/07/26-17:57:50.570665 6880 Compile date 2023-12-15 13:01:14
2025/07/26-17:57:50.570676 6880 DB SUMMARY
2025/07/26-17:57:50.570683 6880 Host name (Env):  DESKTOP-RNBFHG8
2025/07/26-17:57:50.570688 6880 DB Session ID:  LVRVIYQGWFY9K3J8VZWS
2025/07/26-17:57:50.570831 6880 SST files in ./data/12D3KooWSND83wpdXQ58Sw1v2GfrenUQNxm1RGNAWasT4EcnnqKd dir, Total Num: 0, files: 
2025/07/26-17:57:50.570841 6880 Write Ahead Log file in ./data/12D3KooWSND83wpdXQ58Sw1v2GfrenUQNxm1RGNAWasT4EcnnqKd: 
2025/07/26-17:57:50.570846 6880                         Options.error_if_exists: 0
2025/07/26-17:57:50.570852 6880                       Options.create_if_missing: 1
2025/07/26-17:57:50.570859 6880                         Options.paranoid_checks: 1
2025/07/26-17:57:50.570949 6880             Options.flush_verify_memtable_count: 1
2025/07/26-17:57:50.570956 6880          Options.compaction_verify_record_count: 1
2025/07/26-17:57:50.570958 6880                               Options.track_and_verify_wals_in_manifest: 0
2025/07/26-17:57:50.570960 6880        Options.verify_sst_unique_id_in_manifest: 1
2025/07/26-17:57:50.570961 6880                                     Options.env: 000002B144E3A7C0
2025/07/26-17:57:50.570963 6880                                      Options.fs: WinFS
2025/07/26-17:57:50.570966 6880                                Options.info_log: 000002B144DC97F0
2025/07/26-17:57:50.570967 6880                Options.max_file_opening_threads: 16
2025/07/26-17:57:50.570969 6880                              Options.statistics: 0000000000000000
2025/07/26-17:57:50.570971 6880                               Options.use_fsync: 0
2025/07/26-17:57:50.570973 6880                       Options.max_log_file_size: 0
2025/07/26-17:57:50.570984 6880                  Options.max_manifest_file_size: 1073741824
2025/07/26-17:57:50.570991 6880                   Options.log_file_time_to_roll: 0
2025/07/26-17:57:50.570993 6880                       Options.keep_log_file_num: 1000
2025/07/26-17:57:50.570995 6880                    Options.recycle_log_file_num: 0
2025/07/26-17:57:50.570996 6880                         Options.allow_fallocate: 1
2025/07/26-17:57:50.570998 6880                        Options.allow_mmap_reads: 0
2025/07/26-17:57:50.571000 6880                       Options.allow_mmap_writes: 0
2025/07/26-17:57:50.571002 6880                        Options.use_direct_reads: 0
2025/07/26-17:57:50.571004 6880                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/26-17:57:50.571005 6880          Options.create_missing_column_families: 1
2025/07/26-17:57:50.571007 6880                              Options.db_log_dir: 
2025/07/26-17:57:50.571009 6880                                 Options.wal_dir: 
2025/07/26-17:57:50.571011 6880                Options.table_cache_numshardbits: 6
2025/07/26-17:57:50.571012 6880                         Options.WAL_ttl_seconds: 0
2025/07/26-17:57:50.571014 6880                       Options.WAL_size_limit_MB: 0
2025/07/26-17:57:50.571016 6880                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/26-17:57:50.571018 6880             Options.manifest_preallocation_size: 4194304
2025/07/26-17:57:50.571020 6880                     Options.is_fd_close_on_exec: 1
2025/07/26-17:57:50.571021 6880                   Options.advise_random_on_open: 1
2025/07/26-17:57:50.571023 6880                    Options.db_write_buffer_size: 0
2025/07/26-17:57:50.571025 6880                    Options.write_buffer_manager: 000002B144E3A220
2025/07/26-17:57:50.571027 6880         Options.access_hint_on_compaction_start: 1
2025/07/26-17:57:50.571028 6880           Options.random_access_max_buffer_size: 1048576
2025/07/26-17:57:50.571030 6880                      Options.use_adaptive_mutex: 0
2025/07/26-17:57:50.571032 6880                            Options.rate_limiter: 0000000000000000
2025/07/26-17:57:50.571034 6880     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/26-17:57:50.571036 6880                       Options.wal_recovery_mode: 2
2025/07/26-17:57:50.571058 6880                  Options.enable_thread_tracking: 0
2025/07/26-17:57:50.571061 6880                  Options.enable_pipelined_write: 0
2025/07/26-17:57:50.571063 6880                  Options.unordered_write: 0
2025/07/26-17:57:50.571064 6880         Options.allow_concurrent_memtable_write: 1
2025/07/26-17:57:50.571066 6880      Options.enable_write_thread_adaptive_yield: 1
2025/07/26-17:57:50.571068 6880             Options.write_thread_max_yield_usec: 100
2025/07/26-17:57:50.571070 6880            Options.write_thread_slow_yield_usec: 3
2025/07/26-17:57:50.571071 6880                               Options.row_cache: None
2025/07/26-17:57:50.571073 6880                              Options.wal_filter: None
2025/07/26-17:57:50.571075 6880             Options.avoid_flush_during_recovery: 0
2025/07/26-17:57:50.571077 6880             Options.allow_ingest_behind: 0
2025/07/26-17:57:50.571079 6880             Options.two_write_queues: 0
2025/07/26-17:57:50.571080 6880             Options.manual_wal_flush: 0
2025/07/26-17:57:50.571082 6880             Options.wal_compression: 0
2025/07/26-17:57:50.571084 6880             Options.atomic_flush: 0
2025/07/26-17:57:50.571086 6880             Options.avoid_unnecessary_blocking_io: 0
2025/07/26-17:57:50.571087 6880                 Options.persist_stats_to_disk: 0
2025/07/26-17:57:50.571089 6880                 Options.write_dbid_to_manifest: 0
2025/07/26-17:57:50.571091 6880                 Options.log_readahead_size: 0
2025/07/26-17:57:50.571093 6880                 Options.file_checksum_gen_factory: Unknown
2025/07/26-17:57:50.571095 6880                 Options.best_efforts_recovery: 0
2025/07/26-17:57:50.571096 6880                Options.max_bgerror_resume_count: 2147483647
2025/07/26-17:57:50.571098 6880            Options.bgerror_resume_retry_interval: 1000000
2025/07/26-17:57:50.571100 6880             Options.allow_data_in_errors: 0
2025/07/26-17:57:50.571117 6880             Options.db_host_id: __hostname__
2025/07/26-17:57:50.571119 6880             Options.enforce_single_del_contracts: true
2025/07/26-17:57:50.571121 6880             Options.max_background_jobs: 4
2025/07/26-17:57:50.571123 6880             Options.max_background_compactions: -1
2025/07/26-17:57:50.571125 6880             Options.max_subcompactions: 1
2025/07/26-17:57:50.571127 6880             Options.avoid_flush_during_shutdown: 0
2025/07/26-17:57:50.571128 6880           Options.writable_file_max_buffer_size: 1048576
2025/07/26-17:57:50.571130 6880             Options.delayed_write_rate : 16777216
2025/07/26-17:57:50.571132 6880             Options.max_total_wal_size: 0
2025/07/26-17:57:50.571134 6880             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/26-17:57:50.571136 6880                   Options.stats_dump_period_sec: 600
2025/07/26-17:57:50.571137 6880                 Options.stats_persist_period_sec: 600
2025/07/26-17:57:50.571139 6880                 Options.stats_history_buffer_size: 1048576
2025/07/26-17:57:50.571141 6880                          Options.max_open_files: 1000
2025/07/26-17:57:50.571143 6880                          Options.bytes_per_sync: 0
2025/07/26-17:57:50.571145 6880                      Options.wal_bytes_per_sync: 0
2025/07/26-17:57:50.571147 6880                   Options.strict_bytes_per_sync: 0
2025/07/26-17:57:50.571148 6880       Options.compaction_readahead_size: 2097152
2025/07/26-17:57:50.571150 6880                  Options.max_background_flushes: -1
2025/07/26-17:57:50.571152 6880 Options.daily_offpeak_time_utc: 
2025/07/26-17:57:50.571153 6880 Compression algorithms supported:
2025/07/26-17:57:50.571156 6880 	kZSTD supported: 1
2025/07/26-17:57:50.571158 6880 	kSnappyCompression supported: 1
2025/07/26-17:57:50.571160 6880 	kBZip2Compression supported: 1
2025/07/26-17:57:50.571162 6880 	kZlibCompression supported: 1
2025/07/26-17:57:50.571164 6880 	kLZ4Compression supported: 1
2025/07/26-17:57:50.571165 6880 	kXpressCompression supported: 0
2025/07/26-17:57:50.571167 6880 	kLZ4HCCompression supported: 1
2025/07/26-17:57:50.571178 6880 	kZSTDNotFinalCompression supported: 1
2025/07/26-17:57:50.571182 6880 Fast CRC32 supported: Not supported on x86
2025/07/26-17:57:50.571184 6880 DMutex implementation: std::mutex
2025/07/26-17:57:50.576932 6880 [db/db_impl/db_impl_open.cc:325] Creating manifest 1 
2025/07/26-17:57:50.587417 6880 [db/version_set.cc:5942] Recovering from manifest file: ./data/12D3KooWSND83wpdXQ58Sw1v2GfrenUQNxm1RGNAWasT4EcnnqKd/MANIFEST-000001
2025/07/26-17:57:50.587531 6880 [db/column_family.cc:618] --------------- Options for column family [default]:
2025/07/26-17:57:50.587535 6880               Options.comparator: leveldb.BytewiseComparator
2025/07/26-17:57:50.587538 6880           Options.merge_operator: None
2025/07/26-17:57:50.587539 6880        Options.compaction_filter: None
2025/07/26-17:57:50.587541 6880        Options.compaction_filter_factory: None
2025/07/26-17:57:50.587543 6880  Options.sst_partitioner_factory: None
2025/07/26-17:57:50.587545 6880         Options.memtable_factory: SkipListFactory
2025/07/26-17:57:50.587546 6880            Options.table_factory: BlockBasedTable
2025/07/26-17:57:50.587573 6880            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000002B144E75230)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 000002B144E3A140
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-17:57:50.587576 6880        Options.write_buffer_size: 67108864
2025/07/26-17:57:50.587578 6880  Options.max_write_buffer_number: 2
2025/07/26-17:57:50.587579 6880          Options.compression: Snappy
2025/07/26-17:57:50.587581 6880                  Options.bottommost_compression: Disabled
2025/07/26-17:57:50.587583 6880       Options.prefix_extractor: nullptr
2025/07/26-17:57:50.587585 6880   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-17:57:50.587587 6880             Options.num_levels: 7
2025/07/26-17:57:50.587588 6880        Options.min_write_buffer_number_to_merge: 1
2025/07/26-17:57:50.587590 6880     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-17:57:50.587592 6880     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-17:57:50.587594 6880            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-17:57:50.587595 6880                  Options.bottommost_compression_opts.level: 32767
2025/07/26-17:57:50.587597 6880               Options.bottommost_compression_opts.strategy: 0
2025/07/26-17:57:50.587599 6880         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-17:57:50.587601 6880         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:50.587602 6880         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-17:57:50.587604 6880                  Options.bottommost_compression_opts.enabled: false
2025/07/26-17:57:50.587606 6880         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:50.587608 6880         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:50.587610 6880            Options.compression_opts.window_bits: -14
2025/07/26-17:57:50.587613 6880                  Options.compression_opts.level: 32767
2025/07/26-17:57:50.587616 6880               Options.compression_opts.strategy: 0
2025/07/26-17:57:50.587617 6880         Options.compression_opts.max_dict_bytes: 0
2025/07/26-17:57:50.587619 6880         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:50.587621 6880         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:50.587623 6880         Options.compression_opts.parallel_threads: 1
2025/07/26-17:57:50.587624 6880                  Options.compression_opts.enabled: false
2025/07/26-17:57:50.587626 6880         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:50.587628 6880      Options.level0_file_num_compaction_trigger: 4
2025/07/26-17:57:50.587630 6880          Options.level0_slowdown_writes_trigger: 20
2025/07/26-17:57:50.587632 6880              Options.level0_stop_writes_trigger: 36
2025/07/26-17:57:50.587633 6880                   Options.target_file_size_base: 67108864
2025/07/26-17:57:50.587635 6880             Options.target_file_size_multiplier: 1
2025/07/26-17:57:50.587637 6880                Options.max_bytes_for_level_base: 268435456
2025/07/26-17:57:50.587639 6880 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-17:57:50.587641 6880          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-17:57:50.587643 6880 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-17:57:50.587645 6880 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-17:57:50.587646 6880 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-17:57:50.587648 6880 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-17:57:50.587650 6880 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-17:57:50.587652 6880 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-17:57:50.587654 6880 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-17:57:50.587656 6880       Options.max_sequential_skip_in_iterations: 8
2025/07/26-17:57:50.587658 6880                    Options.max_compaction_bytes: 1677721600
2025/07/26-17:57:50.587659 6880   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-17:57:50.587662 6880                        Options.arena_block_size: 1048576
2025/07/26-17:57:50.587663 6880   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-17:57:50.587666 6880   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-17:57:50.587667 6880                Options.disable_auto_compactions: 0
2025/07/26-17:57:50.587670 6880                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-17:57:50.587673 6880                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-17:57:50.587675 6880 Options.compaction_options_universal.size_ratio: 1
2025/07/26-17:57:50.587676 6880 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-17:57:50.587678 6880 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-17:57:50.587680 6880 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-17:57:50.587682 6880 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-17:57:50.587684 6880 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-17:57:50.587686 6880 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-17:57:50.587688 6880 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-17:57:50.587691 6880                   Options.table_properties_collectors: 
2025/07/26-17:57:50.587692 6880                   Options.inplace_update_support: 0
2025/07/26-17:57:50.587694 6880                 Options.inplace_update_num_locks: 10000
2025/07/26-17:57:50.587696 6880               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-17:57:50.587698 6880               Options.memtable_whole_key_filtering: 0
2025/07/26-17:57:50.587700 6880   Options.memtable_huge_page_size: 0
2025/07/26-17:57:50.587721 6880                           Options.bloom_locality: 0
2025/07/26-17:57:50.587902 6880                    Options.max_successive_merges: 0
2025/07/26-17:57:50.587904 6880                Options.optimize_filters_for_hits: 0
2025/07/26-17:57:50.587906 6880                Options.paranoid_file_checks: 0
2025/07/26-17:57:50.587908 6880                Options.force_consistency_checks: 1
2025/07/26-17:57:50.587910 6880                Options.report_bg_io_stats: 0
2025/07/26-17:57:50.587911 6880                               Options.ttl: 2592000
2025/07/26-17:57:50.587913 6880          Options.periodic_compaction_seconds: 0
2025/07/26-17:57:50.587915 6880                        Options.default_temperature: kUnknown
2025/07/26-17:57:50.587917 6880  Options.preclude_last_level_data_seconds: 0
2025/07/26-17:57:50.587919 6880    Options.preserve_internal_time_seconds: 0
2025/07/26-17:57:50.587921 6880                       Options.enable_blob_files: false
2025/07/26-17:57:50.587923 6880                           Options.min_blob_size: 0
2025/07/26-17:57:50.587924 6880                          Options.blob_file_size: 268435456
2025/07/26-17:57:50.587926 6880                   Options.blob_compression_type: NoCompression
2025/07/26-17:57:50.587928 6880          Options.enable_blob_garbage_collection: false
2025/07/26-17:57:50.587930 6880      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-17:57:50.587932 6880 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-17:57:50.587934 6880          Options.blob_compaction_readahead_size: 0
2025/07/26-17:57:50.587936 6880                Options.blob_file_starting_level: 0
2025/07/26-17:57:50.587937 6880         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-17:57:50.587939 6880            Options.memtable_max_range_deletions: 0
2025/07/26-17:57:50.588966 6880 [db/version_set.cc:5993] Recovered from manifest file:./data/12D3KooWSND83wpdXQ58Sw1v2GfrenUQNxm1RGNAWasT4EcnnqKd/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/07/26-17:57:50.588974 6880 [db/version_set.cc:6002] Column family [default] (ID 0), log number is 0
2025/07/26-17:57:50.589189 6880 [db/db_impl/db_impl_open.cc:646] DB ID: fd0322de-6a06-11f0-98b6-d4e98a1a402d
2025/07/26-17:57:50.590083 6880 [db/version_set.cc:5439] Creating manifest 5
2025/07/26-17:57:50.599932 6880 [db/column_family.cc:618] --------------- Options for column family [tasks]:
2025/07/26-17:57:50.599947 6880               Options.comparator: leveldb.BytewiseComparator
2025/07/26-17:57:50.599949 6880           Options.merge_operator: None
2025/07/26-17:57:50.599951 6880        Options.compaction_filter: None
2025/07/26-17:57:50.599953 6880        Options.compaction_filter_factory: None
2025/07/26-17:57:50.599955 6880  Options.sst_partitioner_factory: None
2025/07/26-17:57:50.599957 6880         Options.memtable_factory: SkipListFactory
2025/07/26-17:57:50.599958 6880            Options.table_factory: BlockBasedTable
2025/07/26-17:57:50.599981 6880            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000002B144E054D0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 000002B144E39D80
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-17:57:50.599985 6880        Options.write_buffer_size: 67108864
2025/07/26-17:57:50.599989 6880  Options.max_write_buffer_number: 2
2025/07/26-17:57:50.599990 6880          Options.compression: Snappy
2025/07/26-17:57:50.599992 6880                  Options.bottommost_compression: Disabled
2025/07/26-17:57:50.599994 6880       Options.prefix_extractor: nullptr
2025/07/26-17:57:50.599996 6880   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-17:57:50.599998 6880             Options.num_levels: 7
2025/07/26-17:57:50.599999 6880        Options.min_write_buffer_number_to_merge: 1
2025/07/26-17:57:50.600001 6880     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-17:57:50.600003 6880     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-17:57:50.600005 6880            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-17:57:50.600006 6880                  Options.bottommost_compression_opts.level: 32767
2025/07/26-17:57:50.600008 6880               Options.bottommost_compression_opts.strategy: 0
2025/07/26-17:57:50.600010 6880         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-17:57:50.600012 6880         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:50.600014 6880         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-17:57:50.600016 6880                  Options.bottommost_compression_opts.enabled: false
2025/07/26-17:57:50.600017 6880         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:50.600019 6880         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:50.600021 6880            Options.compression_opts.window_bits: -14
2025/07/26-17:57:50.600023 6880                  Options.compression_opts.level: 32767
2025/07/26-17:57:50.600025 6880               Options.compression_opts.strategy: 0
2025/07/26-17:57:50.600026 6880         Options.compression_opts.max_dict_bytes: 0
2025/07/26-17:57:50.600028 6880         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:50.600030 6880         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:50.600032 6880         Options.compression_opts.parallel_threads: 1
2025/07/26-17:57:50.600033 6880                  Options.compression_opts.enabled: false
2025/07/26-17:57:50.600035 6880         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:50.600037 6880      Options.level0_file_num_compaction_trigger: 4
2025/07/26-17:57:50.600039 6880          Options.level0_slowdown_writes_trigger: 20
2025/07/26-17:57:50.600041 6880              Options.level0_stop_writes_trigger: 36
2025/07/26-17:57:50.600042 6880                   Options.target_file_size_base: 67108864
2025/07/26-17:57:50.600044 6880             Options.target_file_size_multiplier: 1
2025/07/26-17:57:50.600046 6880                Options.max_bytes_for_level_base: 268435456
2025/07/26-17:57:50.600048 6880 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-17:57:50.600049 6880          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-17:57:50.600051 6880 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-17:57:50.600053 6880 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-17:57:50.600055 6880 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-17:57:50.600057 6880 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-17:57:50.600059 6880 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-17:57:50.600060 6880 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-17:57:50.600062 6880 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-17:57:50.600064 6880       Options.max_sequential_skip_in_iterations: 8
2025/07/26-17:57:50.600065 6880                    Options.max_compaction_bytes: 1677721600
2025/07/26-17:57:50.600068 6880   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-17:57:50.600070 6880                        Options.arena_block_size: 1048576
2025/07/26-17:57:50.600072 6880   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-17:57:50.600074 6880   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-17:57:50.600076 6880                Options.disable_auto_compactions: 0
2025/07/26-17:57:50.600078 6880                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-17:57:50.600081 6880                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-17:57:50.600083 6880 Options.compaction_options_universal.size_ratio: 1
2025/07/26-17:57:50.600085 6880 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-17:57:50.600086 6880 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-17:57:50.600088 6880 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-17:57:50.600090 6880 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-17:57:50.600093 6880 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-17:57:50.600094 6880 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-17:57:50.600096 6880 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-17:57:50.600100 6880                   Options.table_properties_collectors: 
2025/07/26-17:57:50.600102 6880                   Options.inplace_update_support: 0
2025/07/26-17:57:50.600104 6880                 Options.inplace_update_num_locks: 10000
2025/07/26-17:57:50.600105 6880               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-17:57:50.600107 6880               Options.memtable_whole_key_filtering: 0
2025/07/26-17:57:50.600109 6880   Options.memtable_huge_page_size: 0
2025/07/26-17:57:50.600111 6880                           Options.bloom_locality: 0
2025/07/26-17:57:50.600113 6880                    Options.max_successive_merges: 0
2025/07/26-17:57:50.600114 6880                Options.optimize_filters_for_hits: 0
2025/07/26-17:57:50.600116 6880                Options.paranoid_file_checks: 0
2025/07/26-17:57:50.600118 6880                Options.force_consistency_checks: 1
2025/07/26-17:57:50.600120 6880                Options.report_bg_io_stats: 0
2025/07/26-17:57:50.600121 6880                               Options.ttl: 2592000
2025/07/26-17:57:50.600123 6880          Options.periodic_compaction_seconds: 0
2025/07/26-17:57:50.600125 6880                        Options.default_temperature: kUnknown
2025/07/26-17:57:50.600127 6880  Options.preclude_last_level_data_seconds: 0
2025/07/26-17:57:50.600129 6880    Options.preserve_internal_time_seconds: 0
2025/07/26-17:57:50.600130 6880                       Options.enable_blob_files: false
2025/07/26-17:57:50.600132 6880                           Options.min_blob_size: 0
2025/07/26-17:57:50.600134 6880                          Options.blob_file_size: 268435456
2025/07/26-17:57:50.600136 6880                   Options.blob_compression_type: NoCompression
2025/07/26-17:57:50.600138 6880          Options.enable_blob_garbage_collection: false
2025/07/26-17:57:50.600139 6880      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-17:57:50.600141 6880 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-17:57:50.600143 6880          Options.blob_compaction_readahead_size: 0
2025/07/26-17:57:50.600145 6880                Options.blob_file_starting_level: 0
2025/07/26-17:57:50.600147 6880         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-17:57:50.600149 6880            Options.memtable_max_range_deletions: 0
2025/07/26-17:57:50.600325 6880 [db/db_impl/db_impl.cc:3637] Created column family [tasks] (ID 1)
2025/07/26-17:57:50.602846 6880 [db/column_family.cc:618] --------------- Options for column family [raft_state]:
2025/07/26-17:57:50.602856 6880               Options.comparator: leveldb.BytewiseComparator
2025/07/26-17:57:50.602860 6880           Options.merge_operator: None
2025/07/26-17:57:50.602864 6880        Options.compaction_filter: None
2025/07/26-17:57:50.602866 6880        Options.compaction_filter_factory: None
2025/07/26-17:57:50.602868 6880  Options.sst_partitioner_factory: None
2025/07/26-17:57:50.602871 6880         Options.memtable_factory: SkipListFactory
2025/07/26-17:57:50.602873 6880            Options.table_factory: BlockBasedTable
2025/07/26-17:57:50.602894 6880            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000002B144E050B0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 000002B144E3A320
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-17:57:50.602898 6880        Options.write_buffer_size: 67108864
2025/07/26-17:57:50.602900 6880  Options.max_write_buffer_number: 2
2025/07/26-17:57:50.602902 6880          Options.compression: Snappy
2025/07/26-17:57:50.602905 6880                  Options.bottommost_compression: Disabled
2025/07/26-17:57:50.602907 6880       Options.prefix_extractor: nullptr
2025/07/26-17:57:50.602909 6880   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-17:57:50.602912 6880             Options.num_levels: 7
2025/07/26-17:57:50.602914 6880        Options.min_write_buffer_number_to_merge: 1
2025/07/26-17:57:50.602916 6880     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-17:57:50.602918 6880     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-17:57:50.602921 6880            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-17:57:50.602923 6880                  Options.bottommost_compression_opts.level: 32767
2025/07/26-17:57:50.602926 6880               Options.bottommost_compression_opts.strategy: 0
2025/07/26-17:57:50.602928 6880         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-17:57:50.602930 6880         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:50.602933 6880         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-17:57:50.602935 6880                  Options.bottommost_compression_opts.enabled: false
2025/07/26-17:57:50.602937 6880         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:50.602940 6880         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:50.602942 6880            Options.compression_opts.window_bits: -14
2025/07/26-17:57:50.602945 6880                  Options.compression_opts.level: 32767
2025/07/26-17:57:50.602947 6880               Options.compression_opts.strategy: 0
2025/07/26-17:57:50.602949 6880         Options.compression_opts.max_dict_bytes: 0
2025/07/26-17:57:50.602951 6880         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:50.602953 6880         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:50.602956 6880         Options.compression_opts.parallel_threads: 1
2025/07/26-17:57:50.602958 6880                  Options.compression_opts.enabled: false
2025/07/26-17:57:50.602991 6880         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:50.602994 6880      Options.level0_file_num_compaction_trigger: 4
2025/07/26-17:57:50.602997 6880          Options.level0_slowdown_writes_trigger: 20
2025/07/26-17:57:50.602999 6880              Options.level0_stop_writes_trigger: 36
2025/07/26-17:57:50.603001 6880                   Options.target_file_size_base: 67108864
2025/07/26-17:57:50.603003 6880             Options.target_file_size_multiplier: 1
2025/07/26-17:57:50.603006 6880                Options.max_bytes_for_level_base: 268435456
2025/07/26-17:57:50.603008 6880 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-17:57:50.603010 6880          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-17:57:50.603013 6880 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-17:57:50.603015 6880 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-17:57:50.603018 6880 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-17:57:50.603020 6880 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-17:57:50.603022 6880 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-17:57:50.603024 6880 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-17:57:50.603026 6880 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-17:57:50.603029 6880       Options.max_sequential_skip_in_iterations: 8
2025/07/26-17:57:50.603031 6880                    Options.max_compaction_bytes: 1677721600
2025/07/26-17:57:50.603033 6880   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-17:57:50.603035 6880                        Options.arena_block_size: 1048576
2025/07/26-17:57:50.603037 6880   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-17:57:50.603038 6880   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-17:57:50.603040 6880                Options.disable_auto_compactions: 0
2025/07/26-17:57:50.603043 6880                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-17:57:50.603045 6880                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-17:57:50.603047 6880 Options.compaction_options_universal.size_ratio: 1
2025/07/26-17:57:50.603049 6880 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-17:57:50.603051 6880 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-17:57:50.603052 6880 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-17:57:50.603054 6880 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-17:57:50.603057 6880 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-17:57:50.603059 6880 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-17:57:50.603061 6880 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-17:57:50.603064 6880                   Options.table_properties_collectors: 
2025/07/26-17:57:50.603066 6880                   Options.inplace_update_support: 0
2025/07/26-17:57:50.603067 6880                 Options.inplace_update_num_locks: 10000
2025/07/26-17:57:50.603069 6880               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-17:57:50.603071 6880               Options.memtable_whole_key_filtering: 0
2025/07/26-17:57:50.603073 6880   Options.memtable_huge_page_size: 0
2025/07/26-17:57:50.603075 6880                           Options.bloom_locality: 0
2025/07/26-17:57:50.603076 6880                    Options.max_successive_merges: 0
2025/07/26-17:57:50.603078 6880                Options.optimize_filters_for_hits: 0
2025/07/26-17:57:50.603080 6880                Options.paranoid_file_checks: 0
2025/07/26-17:57:50.603082 6880                Options.force_consistency_checks: 1
2025/07/26-17:57:50.603083 6880                Options.report_bg_io_stats: 0
2025/07/26-17:57:50.603085 6880                               Options.ttl: 2592000
2025/07/26-17:57:50.603087 6880          Options.periodic_compaction_seconds: 0
2025/07/26-17:57:50.603089 6880                        Options.default_temperature: kUnknown
2025/07/26-17:57:50.603091 6880  Options.preclude_last_level_data_seconds: 0
2025/07/26-17:57:50.603093 6880    Options.preserve_internal_time_seconds: 0
2025/07/26-17:57:50.603095 6880                       Options.enable_blob_files: false
2025/07/26-17:57:50.603097 6880                           Options.min_blob_size: 0
2025/07/26-17:57:50.603099 6880                          Options.blob_file_size: 268435456
2025/07/26-17:57:50.603100 6880                   Options.blob_compression_type: NoCompression
2025/07/26-17:57:50.603102 6880          Options.enable_blob_garbage_collection: false
2025/07/26-17:57:50.603104 6880      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-17:57:50.603106 6880 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-17:57:50.603108 6880          Options.blob_compaction_readahead_size: 0
2025/07/26-17:57:50.603110 6880                Options.blob_file_starting_level: 0
2025/07/26-17:57:50.603111 6880         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-17:57:50.603113 6880            Options.memtable_max_range_deletions: 0
2025/07/26-17:57:50.603282 6880 [db/db_impl/db_impl.cc:3637] Created column family [raft_state] (ID 2)
2025/07/26-17:57:50.605769 6880 [db/column_family.cc:618] --------------- Options for column family [task_versions]:
2025/07/26-17:57:50.605779 6880               Options.comparator: leveldb.BytewiseComparator
2025/07/26-17:57:50.605781 6880           Options.merge_operator: None
2025/07/26-17:57:50.605783 6880        Options.compaction_filter: None
2025/07/26-17:57:50.605785 6880        Options.compaction_filter_factory: None
2025/07/26-17:57:50.605786 6880  Options.sst_partitioner_factory: None
2025/07/26-17:57:50.605788 6880         Options.memtable_factory: SkipListFactory
2025/07/26-17:57:50.605790 6880            Options.table_factory: BlockBasedTable
2025/07/26-17:57:50.605810 6880            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000002B144E74D20)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 000002B144E3A410
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-17:57:50.605813 6880        Options.write_buffer_size: 67108864
2025/07/26-17:57:50.605815 6880  Options.max_write_buffer_number: 2
2025/07/26-17:57:50.605817 6880          Options.compression: Snappy
2025/07/26-17:57:50.605818 6880                  Options.bottommost_compression: Disabled
2025/07/26-17:57:50.605820 6880       Options.prefix_extractor: nullptr
2025/07/26-17:57:50.605822 6880   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-17:57:50.605824 6880             Options.num_levels: 7
2025/07/26-17:57:50.605826 6880        Options.min_write_buffer_number_to_merge: 1
2025/07/26-17:57:50.605827 6880     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-17:57:50.605829 6880     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-17:57:50.605831 6880            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-17:57:50.605834 6880                  Options.bottommost_compression_opts.level: 32767
2025/07/26-17:57:50.605837 6880               Options.bottommost_compression_opts.strategy: 0
2025/07/26-17:57:50.605839 6880         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-17:57:50.605841 6880         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:50.605843 6880         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-17:57:50.605845 6880                  Options.bottommost_compression_opts.enabled: false
2025/07/26-17:57:50.605846 6880         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:50.605848 6880         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:50.605850 6880            Options.compression_opts.window_bits: -14
2025/07/26-17:57:50.605852 6880                  Options.compression_opts.level: 32767
2025/07/26-17:57:50.605854 6880               Options.compression_opts.strategy: 0
2025/07/26-17:57:50.605855 6880         Options.compression_opts.max_dict_bytes: 0
2025/07/26-17:57:50.605857 6880         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:50.605859 6880         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:50.605861 6880         Options.compression_opts.parallel_threads: 1
2025/07/26-17:57:50.605862 6880                  Options.compression_opts.enabled: false
2025/07/26-17:57:50.605864 6880         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:50.605866 6880      Options.level0_file_num_compaction_trigger: 4
2025/07/26-17:57:50.605868 6880          Options.level0_slowdown_writes_trigger: 20
2025/07/26-17:57:50.605870 6880              Options.level0_stop_writes_trigger: 36
2025/07/26-17:57:50.605871 6880                   Options.target_file_size_base: 67108864
2025/07/26-17:57:50.605873 6880             Options.target_file_size_multiplier: 1
2025/07/26-17:57:50.605875 6880                Options.max_bytes_for_level_base: 268435456
2025/07/26-17:57:50.605877 6880 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-17:57:50.605879 6880          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-17:57:50.605881 6880 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-17:57:50.605883 6880 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-17:57:50.605885 6880 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-17:57:50.605887 6880 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-17:57:50.605888 6880 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-17:57:50.605890 6880 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-17:57:50.605892 6880 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-17:57:50.605893 6880       Options.max_sequential_skip_in_iterations: 8
2025/07/26-17:57:50.605895 6880                    Options.max_compaction_bytes: 1677721600
2025/07/26-17:57:50.605897 6880   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-17:57:50.605899 6880                        Options.arena_block_size: 1048576
2025/07/26-17:57:50.605901 6880   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-17:57:50.605903 6880   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-17:57:50.605904 6880                Options.disable_auto_compactions: 0
2025/07/26-17:57:50.605906 6880                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-17:57:50.605909 6880                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-17:57:50.605911 6880 Options.compaction_options_universal.size_ratio: 1
2025/07/26-17:57:50.605912 6880 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-17:57:50.605914 6880 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-17:57:50.605916 6880 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-17:57:50.605918 6880 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-17:57:50.605920 6880 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-17:57:50.605923 6880 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-17:57:50.605924 6880 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-17:57:50.605929 6880                   Options.table_properties_collectors: 
2025/07/26-17:57:50.605931 6880                   Options.inplace_update_support: 0
2025/07/26-17:57:50.605933 6880                 Options.inplace_update_num_locks: 10000
2025/07/26-17:57:50.605935 6880               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-17:57:50.605937 6880               Options.memtable_whole_key_filtering: 0
2025/07/26-17:57:50.605938 6880   Options.memtable_huge_page_size: 0
2025/07/26-17:57:50.605940 6880                           Options.bloom_locality: 0
2025/07/26-17:57:50.605942 6880                    Options.max_successive_merges: 0
2025/07/26-17:57:50.605944 6880                Options.optimize_filters_for_hits: 0
2025/07/26-17:57:50.605945 6880                Options.paranoid_file_checks: 0
2025/07/26-17:57:50.605947 6880                Options.force_consistency_checks: 1
2025/07/26-17:57:50.605949 6880                Options.report_bg_io_stats: 0
2025/07/26-17:57:50.605950 6880                               Options.ttl: 2592000
2025/07/26-17:57:50.605952 6880          Options.periodic_compaction_seconds: 0
2025/07/26-17:57:50.605954 6880                        Options.default_temperature: kUnknown
2025/07/26-17:57:50.605956 6880  Options.preclude_last_level_data_seconds: 0
2025/07/26-17:57:50.605958 6880    Options.preserve_internal_time_seconds: 0
2025/07/26-17:57:50.605959 6880                       Options.enable_blob_files: false
2025/07/26-17:57:50.605961 6880                           Options.min_blob_size: 0
2025/07/26-17:57:50.605963 6880                          Options.blob_file_size: 268435456
2025/07/26-17:57:50.605965 6880                   Options.blob_compression_type: NoCompression
2025/07/26-17:57:50.605967 6880          Options.enable_blob_garbage_collection: false
2025/07/26-17:57:50.605968 6880      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-17:57:50.605970 6880 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-17:57:50.605972 6880          Options.blob_compaction_readahead_size: 0
2025/07/26-17:57:50.605974 6880                Options.blob_file_starting_level: 0
2025/07/26-17:57:50.605976 6880         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-17:57:50.605978 6880            Options.memtable_max_range_deletions: 0
2025/07/26-17:57:50.606116 6880 [db/db_impl/db_impl.cc:3637] Created column family [task_versions] (ID 3)
2025/07/26-17:57:50.607849 6880 [db/column_family.cc:618] --------------- Options for column family [task_index]:
2025/07/26-17:57:50.607858 6880               Options.comparator: leveldb.BytewiseComparator
2025/07/26-17:57:50.607860 6880           Options.merge_operator: None
2025/07/26-17:57:50.607862 6880        Options.compaction_filter: None
2025/07/26-17:57:50.607864 6880        Options.compaction_filter_factory: None
2025/07/26-17:57:50.607866 6880  Options.sst_partitioner_factory: None
2025/07/26-17:57:50.607868 6880         Options.memtable_factory: SkipListFactory
2025/07/26-17:57:50.607870 6880            Options.table_factory: BlockBasedTable
2025/07/26-17:57:50.607891 6880            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000002B144E75170)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 000002B144E3A500
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-17:57:50.607894 6880        Options.write_buffer_size: 67108864
2025/07/26-17:57:50.607898 6880  Options.max_write_buffer_number: 2
2025/07/26-17:57:50.607900 6880          Options.compression: Snappy
2025/07/26-17:57:50.607902 6880                  Options.bottommost_compression: Disabled
2025/07/26-17:57:50.607903 6880       Options.prefix_extractor: nullptr
2025/07/26-17:57:50.607905 6880   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-17:57:50.607907 6880             Options.num_levels: 7
2025/07/26-17:57:50.607909 6880        Options.min_write_buffer_number_to_merge: 1
2025/07/26-17:57:50.607911 6880     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-17:57:50.607912 6880     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-17:57:50.607914 6880            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-17:57:50.607916 6880                  Options.bottommost_compression_opts.level: 32767
2025/07/26-17:57:50.607918 6880               Options.bottommost_compression_opts.strategy: 0
2025/07/26-17:57:50.607920 6880         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-17:57:50.607921 6880         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:50.607923 6880         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-17:57:50.607925 6880                  Options.bottommost_compression_opts.enabled: false
2025/07/26-17:57:50.607927 6880         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:50.607929 6880         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:50.607931 6880            Options.compression_opts.window_bits: -14
2025/07/26-17:57:50.607932 6880                  Options.compression_opts.level: 32767
2025/07/26-17:57:50.607934 6880               Options.compression_opts.strategy: 0
2025/07/26-17:57:50.607936 6880         Options.compression_opts.max_dict_bytes: 0
2025/07/26-17:57:50.607938 6880         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:50.607939 6880         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:50.607941 6880         Options.compression_opts.parallel_threads: 1
2025/07/26-17:57:50.607943 6880                  Options.compression_opts.enabled: false
2025/07/26-17:57:50.607945 6880         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:50.607947 6880      Options.level0_file_num_compaction_trigger: 4
2025/07/26-17:57:50.607948 6880          Options.level0_slowdown_writes_trigger: 20
2025/07/26-17:57:50.607950 6880              Options.level0_stop_writes_trigger: 36
2025/07/26-17:57:50.607952 6880                   Options.target_file_size_base: 67108864
2025/07/26-17:57:50.607953 6880             Options.target_file_size_multiplier: 1
2025/07/26-17:57:50.607955 6880                Options.max_bytes_for_level_base: 268435456
2025/07/26-17:57:50.607957 6880 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-17:57:50.607959 6880          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-17:57:50.607961 6880 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-17:57:50.607963 6880 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-17:57:50.607965 6880 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-17:57:50.607966 6880 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-17:57:50.607969 6880 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-17:57:50.607971 6880 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-17:57:50.607973 6880 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-17:57:50.607975 6880       Options.max_sequential_skip_in_iterations: 8
2025/07/26-17:57:50.607976 6880                    Options.max_compaction_bytes: 1677721600
2025/07/26-17:57:50.607978 6880   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-17:57:50.607980 6880                        Options.arena_block_size: 1048576
2025/07/26-17:57:50.607982 6880   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-17:57:50.607984 6880   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-17:57:50.607985 6880                Options.disable_auto_compactions: 0
2025/07/26-17:57:50.607987 6880                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-17:57:50.607989 6880                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-17:57:50.607991 6880 Options.compaction_options_universal.size_ratio: 1
2025/07/26-17:57:50.607993 6880 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-17:57:50.607995 6880 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-17:57:50.607996 6880 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-17:57:50.607998 6880 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-17:57:50.608000 6880 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-17:57:50.608002 6880 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-17:57:50.608004 6880 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-17:57:50.608007 6880                   Options.table_properties_collectors: 
2025/07/26-17:57:50.608009 6880                   Options.inplace_update_support: 0
2025/07/26-17:57:50.608011 6880                 Options.inplace_update_num_locks: 10000
2025/07/26-17:57:50.608012 6880               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-17:57:50.608014 6880               Options.memtable_whole_key_filtering: 0
2025/07/26-17:57:50.608016 6880   Options.memtable_huge_page_size: 0
2025/07/26-17:57:50.608018 6880                           Options.bloom_locality: 0
2025/07/26-17:57:50.608020 6880                    Options.max_successive_merges: 0
2025/07/26-17:57:50.608021 6880                Options.optimize_filters_for_hits: 0
2025/07/26-17:57:50.608023 6880                Options.paranoid_file_checks: 0
2025/07/26-17:57:50.608025 6880                Options.force_consistency_checks: 1
2025/07/26-17:57:50.608027 6880                Options.report_bg_io_stats: 0
2025/07/26-17:57:50.608028 6880                               Options.ttl: 2592000
2025/07/26-17:57:50.608030 6880          Options.periodic_compaction_seconds: 0
2025/07/26-17:57:50.608032 6880                        Options.default_temperature: kUnknown
2025/07/26-17:57:50.608034 6880  Options.preclude_last_level_data_seconds: 0
2025/07/26-17:57:50.608036 6880    Options.preserve_internal_time_seconds: 0
2025/07/26-17:57:50.608037 6880                       Options.enable_blob_files: false
2025/07/26-17:57:50.608039 6880                           Options.min_blob_size: 0
2025/07/26-17:57:50.608041 6880                          Options.blob_file_size: 268435456
2025/07/26-17:57:50.608043 6880                   Options.blob_compression_type: NoCompression
2025/07/26-17:57:50.608044 6880          Options.enable_blob_garbage_collection: false
2025/07/26-17:57:50.608046 6880      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-17:57:50.608048 6880 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-17:57:50.608050 6880          Options.blob_compaction_readahead_size: 0
2025/07/26-17:57:50.608052 6880                Options.blob_file_starting_level: 0
2025/07/26-17:57:50.608054 6880         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-17:57:50.608058 6880            Options.memtable_max_range_deletions: 0
2025/07/26-17:57:50.608208 6880 [db/db_impl/db_impl.cc:3637] Created column family [task_index] (ID 4)
2025/07/26-17:57:50.615136 6880 [db/db_impl/db_impl_open.cc:2157] SstFileManager instance 000002B1469032A0
2025/07/26-17:57:50.615376 6880 DB pointer 000002B1469096C0
2025/07/26-17:57:50.615886 4094 [db/db_impl/db_impl.cc:1140] ------- DUMPING STATS -------
2025/07/26-17:57:50.615899 4094 [db/db_impl/db_impl.cc:1141] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000002B144E3A140#2404 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 4.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** Compaction Stats [tasks] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [tasks] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000002B144E39D80#2404 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2.2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [tasks] **

** Compaction Stats [raft_state] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [raft_state] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000002B144E3A320#2404 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [raft_state] **

** Compaction Stats [task_versions] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_versions] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000002B144E3A410#2404 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2.7e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_versions] **

** Compaction Stats [task_index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000002B144E3A500#2404 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2.7e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_index] **
