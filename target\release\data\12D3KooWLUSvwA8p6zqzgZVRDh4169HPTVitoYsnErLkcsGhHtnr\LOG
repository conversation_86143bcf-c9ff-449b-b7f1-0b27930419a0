2025/07/26-17:57:04.998465 4404 RocksDB version: 8.10.0
2025/07/26-17:57:04.998972 4404 Compile date 2023-12-15 13:01:14
2025/07/26-17:57:04.998987 4404 DB SUMMARY
2025/07/26-17:57:04.998993 4404 Host name (Env):  DESKTOP-RNBFHG8
2025/07/26-17:57:04.998998 4404 DB Session ID:  58RVG0I7B9JM9NCMMAIY
2025/07/26-17:57:04.999147 4404 SST files in ./data/12D3KooWLUSvwA8p6zqzgZVRDh4169HPTVitoYsnErLkcsGhHtnr dir, Total Num: 0, files: 
2025/07/26-17:57:04.999154 4404 Write Ahead Log file in ./data/12D3KooWLUSvwA8p6zqzgZVRDh4169HPTVitoYsnErLkcsGhHtnr: 
2025/07/26-17:57:04.999160 4404                         Options.error_if_exists: 0
2025/07/26-17:57:04.999165 4404                       Options.create_if_missing: 1
2025/07/26-17:57:04.999170 4404                         Options.paranoid_checks: 1
2025/07/26-17:57:04.999250 4404             Options.flush_verify_memtable_count: 1
2025/07/26-17:57:04.999343 4404          Options.compaction_verify_record_count: 1
2025/07/26-17:57:04.999345 4404                               Options.track_and_verify_wals_in_manifest: 0
2025/07/26-17:57:04.999347 4404        Options.verify_sst_unique_id_in_manifest: 1
2025/07/26-17:57:04.999349 4404                                     Options.env: 00000223B88B1D00
2025/07/26-17:57:04.999351 4404                                      Options.fs: WinFS
2025/07/26-17:57:04.999353 4404                                Options.info_log: 00000223BA3ECD00
2025/07/26-17:57:04.999355 4404                Options.max_file_opening_threads: 16
2025/07/26-17:57:04.999357 4404                              Options.statistics: 0000000000000000
2025/07/26-17:57:04.999374 4404                               Options.use_fsync: 0
2025/07/26-17:57:04.999377 4404                       Options.max_log_file_size: 0
2025/07/26-17:57:04.999379 4404                  Options.max_manifest_file_size: 1073741824
2025/07/26-17:57:04.999380 4404                   Options.log_file_time_to_roll: 0
2025/07/26-17:57:04.999382 4404                       Options.keep_log_file_num: 1000
2025/07/26-17:57:04.999384 4404                    Options.recycle_log_file_num: 0
2025/07/26-17:57:04.999386 4404                         Options.allow_fallocate: 1
2025/07/26-17:57:04.999388 4404                        Options.allow_mmap_reads: 0
2025/07/26-17:57:04.999389 4404                       Options.allow_mmap_writes: 0
2025/07/26-17:57:04.999391 4404                        Options.use_direct_reads: 0
2025/07/26-17:57:04.999393 4404                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/26-17:57:04.999395 4404          Options.create_missing_column_families: 1
2025/07/26-17:57:04.999396 4404                              Options.db_log_dir: 
2025/07/26-17:57:04.999398 4404                                 Options.wal_dir: 
2025/07/26-17:57:04.999400 4404                Options.table_cache_numshardbits: 6
2025/07/26-17:57:04.999402 4404                         Options.WAL_ttl_seconds: 0
2025/07/26-17:57:04.999404 4404                       Options.WAL_size_limit_MB: 0
2025/07/26-17:57:04.999417 4404                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/26-17:57:04.999418 4404             Options.manifest_preallocation_size: 4194304
2025/07/26-17:57:04.999420 4404                     Options.is_fd_close_on_exec: 1
2025/07/26-17:57:04.999422 4404                   Options.advise_random_on_open: 1
2025/07/26-17:57:04.999424 4404                    Options.db_write_buffer_size: 0
2025/07/26-17:57:04.999426 4404                    Options.write_buffer_manager: 00000223B88B1940
2025/07/26-17:57:04.999428 4404         Options.access_hint_on_compaction_start: 1
2025/07/26-17:57:04.999429 4404           Options.random_access_max_buffer_size: 1048576
2025/07/26-17:57:04.999431 4404                      Options.use_adaptive_mutex: 0
2025/07/26-17:57:04.999433 4404                            Options.rate_limiter: 0000000000000000
2025/07/26-17:57:04.999435 4404     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/26-17:57:04.999437 4404                       Options.wal_recovery_mode: 2
2025/07/26-17:57:04.999455 4404                  Options.enable_thread_tracking: 0
2025/07/26-17:57:04.999458 4404                  Options.enable_pipelined_write: 0
2025/07/26-17:57:04.999460 4404                  Options.unordered_write: 0
2025/07/26-17:57:04.999462 4404         Options.allow_concurrent_memtable_write: 1
2025/07/26-17:57:04.999464 4404      Options.enable_write_thread_adaptive_yield: 1
2025/07/26-17:57:04.999466 4404             Options.write_thread_max_yield_usec: 100
2025/07/26-17:57:04.999467 4404            Options.write_thread_slow_yield_usec: 3
2025/07/26-17:57:04.999469 4404                               Options.row_cache: None
2025/07/26-17:57:04.999471 4404                              Options.wal_filter: None
2025/07/26-17:57:04.999473 4404             Options.avoid_flush_during_recovery: 0
2025/07/26-17:57:04.999475 4404             Options.allow_ingest_behind: 0
2025/07/26-17:57:04.999476 4404             Options.two_write_queues: 0
2025/07/26-17:57:04.999478 4404             Options.manual_wal_flush: 0
2025/07/26-17:57:04.999480 4404             Options.wal_compression: 0
2025/07/26-17:57:04.999482 4404             Options.atomic_flush: 0
2025/07/26-17:57:04.999483 4404             Options.avoid_unnecessary_blocking_io: 0
2025/07/26-17:57:04.999485 4404                 Options.persist_stats_to_disk: 0
2025/07/26-17:57:04.999487 4404                 Options.write_dbid_to_manifest: 0
2025/07/26-17:57:04.999489 4404                 Options.log_readahead_size: 0
2025/07/26-17:57:04.999491 4404                 Options.file_checksum_gen_factory: Unknown
2025/07/26-17:57:04.999493 4404                 Options.best_efforts_recovery: 0
2025/07/26-17:57:04.999494 4404                Options.max_bgerror_resume_count: 2147483647
2025/07/26-17:57:04.999496 4404            Options.bgerror_resume_retry_interval: 1000000
2025/07/26-17:57:04.999498 4404             Options.allow_data_in_errors: 0
2025/07/26-17:57:04.999500 4404             Options.db_host_id: __hostname__
2025/07/26-17:57:04.999501 4404             Options.enforce_single_del_contracts: true
2025/07/26-17:57:04.999503 4404             Options.max_background_jobs: 4
2025/07/26-17:57:04.999505 4404             Options.max_background_compactions: -1
2025/07/26-17:57:04.999532 4404             Options.max_subcompactions: 1
2025/07/26-17:57:04.999547 4404             Options.avoid_flush_during_shutdown: 0
2025/07/26-17:57:04.999553 4404           Options.writable_file_max_buffer_size: 1048576
2025/07/26-17:57:04.999557 4404             Options.delayed_write_rate : 16777216
2025/07/26-17:57:04.999561 4404             Options.max_total_wal_size: 0
2025/07/26-17:57:04.999564 4404             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/26-17:57:04.999568 4404                   Options.stats_dump_period_sec: 600
2025/07/26-17:57:04.999571 4404                 Options.stats_persist_period_sec: 600
2025/07/26-17:57:04.999575 4404                 Options.stats_history_buffer_size: 1048576
2025/07/26-17:57:04.999578 4404                          Options.max_open_files: 1000
2025/07/26-17:57:04.999582 4404                          Options.bytes_per_sync: 0
2025/07/26-17:57:04.999585 4404                      Options.wal_bytes_per_sync: 0
2025/07/26-17:57:04.999589 4404                   Options.strict_bytes_per_sync: 0
2025/07/26-17:57:04.999592 4404       Options.compaction_readahead_size: 2097152
2025/07/26-17:57:04.999596 4404                  Options.max_background_flushes: -1
2025/07/26-17:57:04.999599 4404 Options.daily_offpeak_time_utc: 
2025/07/26-17:57:04.999603 4404 Compression algorithms supported:
2025/07/26-17:57:04.999635 4404 	kZSTD supported: 1
2025/07/26-17:57:04.999652 4404 	kSnappyCompression supported: 1
2025/07/26-17:57:04.999656 4404 	kBZip2Compression supported: 1
2025/07/26-17:57:04.999658 4404 	kZlibCompression supported: 1
2025/07/26-17:57:04.999660 4404 	kLZ4Compression supported: 1
2025/07/26-17:57:04.999662 4404 	kXpressCompression supported: 0
2025/07/26-17:57:04.999664 4404 	kLZ4HCCompression supported: 1
2025/07/26-17:57:04.999688 4404 	kZSTDNotFinalCompression supported: 1
2025/07/26-17:57:04.999699 4404 Fast CRC32 supported: Not supported on x86
2025/07/26-17:57:04.999701 4404 DMutex implementation: std::mutex
2025/07/26-17:57:05.006344 4404 [db/db_impl/db_impl_open.cc:325] Creating manifest 1 
2025/07/26-17:57:05.015344 4404 [db/version_set.cc:5942] Recovering from manifest file: ./data/12D3KooWLUSvwA8p6zqzgZVRDh4169HPTVitoYsnErLkcsGhHtnr/MANIFEST-000001
2025/07/26-17:57:05.015513 4404 [db/column_family.cc:618] --------------- Options for column family [default]:
2025/07/26-17:57:05.015521 4404               Options.comparator: leveldb.BytewiseComparator
2025/07/26-17:57:05.015526 4404           Options.merge_operator: None
2025/07/26-17:57:05.015528 4404        Options.compaction_filter: None
2025/07/26-17:57:05.015529 4404        Options.compaction_filter_factory: None
2025/07/26-17:57:05.015531 4404  Options.sst_partitioner_factory: None
2025/07/26-17:57:05.015533 4404         Options.memtable_factory: SkipListFactory
2025/07/26-17:57:05.015535 4404            Options.table_factory: BlockBasedTable
2025/07/26-17:57:05.015564 4404            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000223B898D430)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000223B88B1C20
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-17:57:05.015567 4404        Options.write_buffer_size: 67108864
2025/07/26-17:57:05.015569 4404  Options.max_write_buffer_number: 2
2025/07/26-17:57:05.015570 4404          Options.compression: Snappy
2025/07/26-17:57:05.015572 4404                  Options.bottommost_compression: Disabled
2025/07/26-17:57:05.015574 4404       Options.prefix_extractor: nullptr
2025/07/26-17:57:05.015576 4404   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-17:57:05.015578 4404             Options.num_levels: 7
2025/07/26-17:57:05.015579 4404        Options.min_write_buffer_number_to_merge: 1
2025/07/26-17:57:05.015581 4404     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-17:57:05.015583 4404     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-17:57:05.015585 4404            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-17:57:05.015586 4404                  Options.bottommost_compression_opts.level: 32767
2025/07/26-17:57:05.015588 4404               Options.bottommost_compression_opts.strategy: 0
2025/07/26-17:57:05.015590 4404         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-17:57:05.015592 4404         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:05.015594 4404         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-17:57:05.015595 4404                  Options.bottommost_compression_opts.enabled: false
2025/07/26-17:57:05.015597 4404         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:05.015599 4404         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:05.015601 4404            Options.compression_opts.window_bits: -14
2025/07/26-17:57:05.015605 4404                  Options.compression_opts.level: 32767
2025/07/26-17:57:05.015607 4404               Options.compression_opts.strategy: 0
2025/07/26-17:57:05.015609 4404         Options.compression_opts.max_dict_bytes: 0
2025/07/26-17:57:05.015611 4404         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:05.015612 4404         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:05.015614 4404         Options.compression_opts.parallel_threads: 1
2025/07/26-17:57:05.015616 4404                  Options.compression_opts.enabled: false
2025/07/26-17:57:05.015618 4404         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:05.015619 4404      Options.level0_file_num_compaction_trigger: 4
2025/07/26-17:57:05.015621 4404          Options.level0_slowdown_writes_trigger: 20
2025/07/26-17:57:05.015623 4404              Options.level0_stop_writes_trigger: 36
2025/07/26-17:57:05.015625 4404                   Options.target_file_size_base: 67108864
2025/07/26-17:57:05.015626 4404             Options.target_file_size_multiplier: 1
2025/07/26-17:57:05.015628 4404                Options.max_bytes_for_level_base: 268435456
2025/07/26-17:57:05.015630 4404 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-17:57:05.015632 4404          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-17:57:05.015634 4404 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-17:57:05.015636 4404 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-17:57:05.015637 4404 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-17:57:05.015639 4404 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-17:57:05.015641 4404 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-17:57:05.015643 4404 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-17:57:05.015644 4404 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-17:57:05.015646 4404       Options.max_sequential_skip_in_iterations: 8
2025/07/26-17:57:05.015648 4404                    Options.max_compaction_bytes: 1677721600
2025/07/26-17:57:05.015650 4404   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-17:57:05.015651 4404                        Options.arena_block_size: 1048576
2025/07/26-17:57:05.015653 4404   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-17:57:05.015655 4404   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-17:57:05.015657 4404                Options.disable_auto_compactions: 0
2025/07/26-17:57:05.015659 4404                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-17:57:05.015661 4404                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-17:57:05.015663 4404 Options.compaction_options_universal.size_ratio: 1
2025/07/26-17:57:05.015665 4404 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-17:57:05.015667 4404 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-17:57:05.015668 4404 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-17:57:05.015670 4404 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-17:57:05.015673 4404 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-17:57:05.015674 4404 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-17:57:05.015676 4404 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-17:57:05.015679 4404                   Options.table_properties_collectors: 
2025/07/26-17:57:05.015681 4404                   Options.inplace_update_support: 0
2025/07/26-17:57:05.015683 4404                 Options.inplace_update_num_locks: 10000
2025/07/26-17:57:05.015685 4404               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-17:57:05.015686 4404               Options.memtable_whole_key_filtering: 0
2025/07/26-17:57:05.015688 4404   Options.memtable_huge_page_size: 0
2025/07/26-17:57:05.015709 4404                           Options.bloom_locality: 0
2025/07/26-17:57:05.015711 4404                    Options.max_successive_merges: 0
2025/07/26-17:57:05.015713 4404                Options.optimize_filters_for_hits: 0
2025/07/26-17:57:05.015715 4404                Options.paranoid_file_checks: 0
2025/07/26-17:57:05.015717 4404                Options.force_consistency_checks: 1
2025/07/26-17:57:05.015718 4404                Options.report_bg_io_stats: 0
2025/07/26-17:57:05.015721 4404                               Options.ttl: 2592000
2025/07/26-17:57:05.015723 4404          Options.periodic_compaction_seconds: 0
2025/07/26-17:57:05.015727 4404                        Options.default_temperature: kUnknown
2025/07/26-17:57:05.015729 4404  Options.preclude_last_level_data_seconds: 0
2025/07/26-17:57:05.015730 4404    Options.preserve_internal_time_seconds: 0
2025/07/26-17:57:05.015732 4404                       Options.enable_blob_files: false
2025/07/26-17:57:05.015734 4404                           Options.min_blob_size: 0
2025/07/26-17:57:05.015736 4404                          Options.blob_file_size: 268435456
2025/07/26-17:57:05.015738 4404                   Options.blob_compression_type: NoCompression
2025/07/26-17:57:05.015739 4404          Options.enable_blob_garbage_collection: false
2025/07/26-17:57:05.015741 4404      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-17:57:05.015743 4404 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-17:57:05.015745 4404          Options.blob_compaction_readahead_size: 0
2025/07/26-17:57:05.015747 4404                Options.blob_file_starting_level: 0
2025/07/26-17:57:05.015749 4404         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-17:57:05.015750 4404            Options.memtable_max_range_deletions: 0
2025/07/26-17:57:05.016914 4404 [db/version_set.cc:5993] Recovered from manifest file:./data/12D3KooWLUSvwA8p6zqzgZVRDh4169HPTVitoYsnErLkcsGhHtnr/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/07/26-17:57:05.016924 4404 [db/version_set.cc:6002] Column family [default] (ID 0), log number is 0
2025/07/26-17:57:05.017150 4404 [db/db_impl/db_impl_open.cc:646] DB ID: e1d99f5b-6a06-11f0-98b6-d4e98a1a402d
2025/07/26-17:57:05.018167 4404 [db/version_set.cc:5439] Creating manifest 5
2025/07/26-17:57:05.027765 4404 [db/column_family.cc:618] --------------- Options for column family [tasks]:
2025/07/26-17:57:05.027774 4404               Options.comparator: leveldb.BytewiseComparator
2025/07/26-17:57:05.027776 4404           Options.merge_operator: None
2025/07/26-17:57:05.027778 4404        Options.compaction_filter: None
2025/07/26-17:57:05.027780 4404        Options.compaction_filter_factory: None
2025/07/26-17:57:05.027782 4404  Options.sst_partitioner_factory: None
2025/07/26-17:57:05.027783 4404         Options.memtable_factory: SkipListFactory
2025/07/26-17:57:05.027785 4404            Options.table_factory: BlockBasedTable
2025/07/26-17:57:05.027809 4404            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000223B8910240)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000223B88B13B0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-17:57:05.027815 4404        Options.write_buffer_size: 67108864
2025/07/26-17:57:05.027819 4404  Options.max_write_buffer_number: 2
2025/07/26-17:57:05.027821 4404          Options.compression: Snappy
2025/07/26-17:57:05.027823 4404                  Options.bottommost_compression: Disabled
2025/07/26-17:57:05.027825 4404       Options.prefix_extractor: nullptr
2025/07/26-17:57:05.027826 4404   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-17:57:05.027828 4404             Options.num_levels: 7
2025/07/26-17:57:05.027830 4404        Options.min_write_buffer_number_to_merge: 1
2025/07/26-17:57:05.027831 4404     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-17:57:05.027833 4404     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-17:57:05.027835 4404            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-17:57:05.027837 4404                  Options.bottommost_compression_opts.level: 32767
2025/07/26-17:57:05.027839 4404               Options.bottommost_compression_opts.strategy: 0
2025/07/26-17:57:05.027840 4404         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-17:57:05.027842 4404         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:05.027844 4404         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-17:57:05.027846 4404                  Options.bottommost_compression_opts.enabled: false
2025/07/26-17:57:05.027848 4404         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:05.027850 4404         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:05.027851 4404            Options.compression_opts.window_bits: -14
2025/07/26-17:57:05.027853 4404                  Options.compression_opts.level: 32767
2025/07/26-17:57:05.027855 4404               Options.compression_opts.strategy: 0
2025/07/26-17:57:05.027857 4404         Options.compression_opts.max_dict_bytes: 0
2025/07/26-17:57:05.027858 4404         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:05.027860 4404         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:05.027862 4404         Options.compression_opts.parallel_threads: 1
2025/07/26-17:57:05.027864 4404                  Options.compression_opts.enabled: false
2025/07/26-17:57:05.027865 4404         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:05.027867 4404      Options.level0_file_num_compaction_trigger: 4
2025/07/26-17:57:05.027869 4404          Options.level0_slowdown_writes_trigger: 20
2025/07/26-17:57:05.027871 4404              Options.level0_stop_writes_trigger: 36
2025/07/26-17:57:05.027872 4404                   Options.target_file_size_base: 67108864
2025/07/26-17:57:05.027874 4404             Options.target_file_size_multiplier: 1
2025/07/26-17:57:05.027876 4404                Options.max_bytes_for_level_base: 268435456
2025/07/26-17:57:05.027877 4404 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-17:57:05.027879 4404          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-17:57:05.027881 4404 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-17:57:05.027883 4404 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-17:57:05.027885 4404 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-17:57:05.027887 4404 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-17:57:05.027888 4404 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-17:57:05.027890 4404 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-17:57:05.027892 4404 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-17:57:05.027894 4404       Options.max_sequential_skip_in_iterations: 8
2025/07/26-17:57:05.027895 4404                    Options.max_compaction_bytes: 1677721600
2025/07/26-17:57:05.027898 4404   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-17:57:05.027900 4404                        Options.arena_block_size: 1048576
2025/07/26-17:57:05.027902 4404   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-17:57:05.027903 4404   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-17:57:05.027905 4404                Options.disable_auto_compactions: 0
2025/07/26-17:57:05.027908 4404                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-17:57:05.027910 4404                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-17:57:05.027912 4404 Options.compaction_options_universal.size_ratio: 1
2025/07/26-17:57:05.027913 4404 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-17:57:05.027915 4404 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-17:57:05.027917 4404 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-17:57:05.027919 4404 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-17:57:05.027921 4404 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-17:57:05.027923 4404 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-17:57:05.027925 4404 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-17:57:05.027928 4404                   Options.table_properties_collectors: 
2025/07/26-17:57:05.027930 4404                   Options.inplace_update_support: 0
2025/07/26-17:57:05.027931 4404                 Options.inplace_update_num_locks: 10000
2025/07/26-17:57:05.027933 4404               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-17:57:05.027935 4404               Options.memtable_whole_key_filtering: 0
2025/07/26-17:57:05.027937 4404   Options.memtable_huge_page_size: 0
2025/07/26-17:57:05.027939 4404                           Options.bloom_locality: 0
2025/07/26-17:57:05.027940 4404                    Options.max_successive_merges: 0
2025/07/26-17:57:05.027942 4404                Options.optimize_filters_for_hits: 0
2025/07/26-17:57:05.027944 4404                Options.paranoid_file_checks: 0
2025/07/26-17:57:05.027946 4404                Options.force_consistency_checks: 1
2025/07/26-17:57:05.027947 4404                Options.report_bg_io_stats: 0
2025/07/26-17:57:05.027949 4404                               Options.ttl: 2592000
2025/07/26-17:57:05.027951 4404          Options.periodic_compaction_seconds: 0
2025/07/26-17:57:05.027953 4404                        Options.default_temperature: kUnknown
2025/07/26-17:57:05.027955 4404  Options.preclude_last_level_data_seconds: 0
2025/07/26-17:57:05.027956 4404    Options.preserve_internal_time_seconds: 0
2025/07/26-17:57:05.027958 4404                       Options.enable_blob_files: false
2025/07/26-17:57:05.027960 4404                           Options.min_blob_size: 0
2025/07/26-17:57:05.027962 4404                          Options.blob_file_size: 268435456
2025/07/26-17:57:05.027963 4404                   Options.blob_compression_type: NoCompression
2025/07/26-17:57:05.027965 4404          Options.enable_blob_garbage_collection: false
2025/07/26-17:57:05.027967 4404      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-17:57:05.027969 4404 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-17:57:05.027971 4404          Options.blob_compaction_readahead_size: 0
2025/07/26-17:57:05.027973 4404                Options.blob_file_starting_level: 0
2025/07/26-17:57:05.027974 4404         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-17:57:05.027976 4404            Options.memtable_max_range_deletions: 0
2025/07/26-17:57:05.028118 4404 [db/db_impl/db_impl.cc:3637] Created column family [tasks] (ID 1)
2025/07/26-17:57:05.030712 4404 [db/column_family.cc:618] --------------- Options for column family [raft_state]:
2025/07/26-17:57:05.030718 4404               Options.comparator: leveldb.BytewiseComparator
2025/07/26-17:57:05.030721 4404           Options.merge_operator: None
2025/07/26-17:57:05.030724 4404        Options.compaction_filter: None
2025/07/26-17:57:05.030726 4404        Options.compaction_filter_factory: None
2025/07/26-17:57:05.030728 4404  Options.sst_partitioner_factory: None
2025/07/26-17:57:05.030729 4404         Options.memtable_factory: SkipListFactory
2025/07/26-17:57:05.030731 4404            Options.table_factory: BlockBasedTable
2025/07/26-17:57:05.030745 4404            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000223B89103F0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000223B88B0FF0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-17:57:05.030749 4404        Options.write_buffer_size: 67108864
2025/07/26-17:57:05.030751 4404  Options.max_write_buffer_number: 2
2025/07/26-17:57:05.030753 4404          Options.compression: Snappy
2025/07/26-17:57:05.030754 4404                  Options.bottommost_compression: Disabled
2025/07/26-17:57:05.030756 4404       Options.prefix_extractor: nullptr
2025/07/26-17:57:05.030758 4404   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-17:57:05.030760 4404             Options.num_levels: 7
2025/07/26-17:57:05.030761 4404        Options.min_write_buffer_number_to_merge: 1
2025/07/26-17:57:05.030763 4404     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-17:57:05.030765 4404     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-17:57:05.030767 4404            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-17:57:05.030768 4404                  Options.bottommost_compression_opts.level: 32767
2025/07/26-17:57:05.030770 4404               Options.bottommost_compression_opts.strategy: 0
2025/07/26-17:57:05.030772 4404         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-17:57:05.030774 4404         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:05.030775 4404         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-17:57:05.030777 4404                  Options.bottommost_compression_opts.enabled: false
2025/07/26-17:57:05.030779 4404         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:05.030781 4404         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:05.030783 4404            Options.compression_opts.window_bits: -14
2025/07/26-17:57:05.030784 4404                  Options.compression_opts.level: 32767
2025/07/26-17:57:05.030786 4404               Options.compression_opts.strategy: 0
2025/07/26-17:57:05.030788 4404         Options.compression_opts.max_dict_bytes: 0
2025/07/26-17:57:05.030790 4404         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:05.030791 4404         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:05.030793 4404         Options.compression_opts.parallel_threads: 1
2025/07/26-17:57:05.030795 4404                  Options.compression_opts.enabled: false
2025/07/26-17:57:05.030819 4404         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:05.030821 4404      Options.level0_file_num_compaction_trigger: 4
2025/07/26-17:57:05.030823 4404          Options.level0_slowdown_writes_trigger: 20
2025/07/26-17:57:05.030825 4404              Options.level0_stop_writes_trigger: 36
2025/07/26-17:57:05.030827 4404                   Options.target_file_size_base: 67108864
2025/07/26-17:57:05.030828 4404             Options.target_file_size_multiplier: 1
2025/07/26-17:57:05.030830 4404                Options.max_bytes_for_level_base: 268435456
2025/07/26-17:57:05.030832 4404 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-17:57:05.030834 4404          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-17:57:05.030836 4404 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-17:57:05.030838 4404 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-17:57:05.030839 4404 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-17:57:05.030841 4404 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-17:57:05.030843 4404 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-17:57:05.030845 4404 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-17:57:05.030846 4404 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-17:57:05.030848 4404       Options.max_sequential_skip_in_iterations: 8
2025/07/26-17:57:05.030850 4404                    Options.max_compaction_bytes: 1677721600
2025/07/26-17:57:05.030851 4404   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-17:57:05.030853 4404                        Options.arena_block_size: 1048576
2025/07/26-17:57:05.030855 4404   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-17:57:05.030857 4404   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-17:57:05.030859 4404                Options.disable_auto_compactions: 0
2025/07/26-17:57:05.030861 4404                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-17:57:05.030863 4404                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-17:57:05.030865 4404 Options.compaction_options_universal.size_ratio: 1
2025/07/26-17:57:05.030866 4404 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-17:57:05.030868 4404 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-17:57:05.030870 4404 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-17:57:05.030872 4404 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-17:57:05.030874 4404 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-17:57:05.030876 4404 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-17:57:05.030877 4404 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-17:57:05.030880 4404                   Options.table_properties_collectors: 
2025/07/26-17:57:05.030882 4404                   Options.inplace_update_support: 0
2025/07/26-17:57:05.030884 4404                 Options.inplace_update_num_locks: 10000
2025/07/26-17:57:05.030886 4404               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-17:57:05.030888 4404               Options.memtable_whole_key_filtering: 0
2025/07/26-17:57:05.030889 4404   Options.memtable_huge_page_size: 0
2025/07/26-17:57:05.030891 4404                           Options.bloom_locality: 0
2025/07/26-17:57:05.030893 4404                    Options.max_successive_merges: 0
2025/07/26-17:57:05.030895 4404                Options.optimize_filters_for_hits: 0
2025/07/26-17:57:05.030897 4404                Options.paranoid_file_checks: 0
2025/07/26-17:57:05.030898 4404                Options.force_consistency_checks: 1
2025/07/26-17:57:05.030900 4404                Options.report_bg_io_stats: 0
2025/07/26-17:57:05.030902 4404                               Options.ttl: 2592000
2025/07/26-17:57:05.030903 4404          Options.periodic_compaction_seconds: 0
2025/07/26-17:57:05.030906 4404                        Options.default_temperature: kUnknown
2025/07/26-17:57:05.030908 4404  Options.preclude_last_level_data_seconds: 0
2025/07/26-17:57:05.030910 4404    Options.preserve_internal_time_seconds: 0
2025/07/26-17:57:05.030912 4404                       Options.enable_blob_files: false
2025/07/26-17:57:05.030913 4404                           Options.min_blob_size: 0
2025/07/26-17:57:05.030915 4404                          Options.blob_file_size: 268435456
2025/07/26-17:57:05.030917 4404                   Options.blob_compression_type: NoCompression
2025/07/26-17:57:05.030919 4404          Options.enable_blob_garbage_collection: false
2025/07/26-17:57:05.030920 4404      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-17:57:05.030922 4404 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-17:57:05.030924 4404          Options.blob_compaction_readahead_size: 0
2025/07/26-17:57:05.030926 4404                Options.blob_file_starting_level: 0
2025/07/26-17:57:05.030928 4404         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-17:57:05.030930 4404            Options.memtable_max_range_deletions: 0
2025/07/26-17:57:05.031051 4404 [db/db_impl/db_impl.cc:3637] Created column family [raft_state] (ID 2)
2025/07/26-17:57:05.032793 4404 [db/column_family.cc:618] --------------- Options for column family [task_versions]:
2025/07/26-17:57:05.032805 4404               Options.comparator: leveldb.BytewiseComparator
2025/07/26-17:57:05.032808 4404           Options.merge_operator: None
2025/07/26-17:57:05.032810 4404        Options.compaction_filter: None
2025/07/26-17:57:05.032812 4404        Options.compaction_filter_factory: None
2025/07/26-17:57:05.032814 4404  Options.sst_partitioner_factory: None
2025/07/26-17:57:05.032816 4404         Options.memtable_factory: SkipListFactory
2025/07/26-17:57:05.032818 4404            Options.table_factory: BlockBasedTable
2025/07/26-17:57:05.032836 4404            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000223B8910000)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000223B88B1590
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-17:57:05.032839 4404        Options.write_buffer_size: 67108864
2025/07/26-17:57:05.032841 4404  Options.max_write_buffer_number: 2
2025/07/26-17:57:05.032843 4404          Options.compression: Snappy
2025/07/26-17:57:05.032845 4404                  Options.bottommost_compression: Disabled
2025/07/26-17:57:05.032847 4404       Options.prefix_extractor: nullptr
2025/07/26-17:57:05.032849 4404   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-17:57:05.032851 4404             Options.num_levels: 7
2025/07/26-17:57:05.032853 4404        Options.min_write_buffer_number_to_merge: 1
2025/07/26-17:57:05.032855 4404     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-17:57:05.032857 4404     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-17:57:05.032859 4404            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-17:57:05.032864 4404                  Options.bottommost_compression_opts.level: 32767
2025/07/26-17:57:05.032867 4404               Options.bottommost_compression_opts.strategy: 0
2025/07/26-17:57:05.032869 4404         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-17:57:05.032871 4404         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:05.032873 4404         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-17:57:05.032875 4404                  Options.bottommost_compression_opts.enabled: false
2025/07/26-17:57:05.032877 4404         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:05.032879 4404         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:05.032881 4404            Options.compression_opts.window_bits: -14
2025/07/26-17:57:05.032883 4404                  Options.compression_opts.level: 32767
2025/07/26-17:57:05.032885 4404               Options.compression_opts.strategy: 0
2025/07/26-17:57:05.032887 4404         Options.compression_opts.max_dict_bytes: 0
2025/07/26-17:57:05.032888 4404         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:05.032890 4404         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:05.032892 4404         Options.compression_opts.parallel_threads: 1
2025/07/26-17:57:05.032894 4404                  Options.compression_opts.enabled: false
2025/07/26-17:57:05.032896 4404         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:05.032898 4404      Options.level0_file_num_compaction_trigger: 4
2025/07/26-17:57:05.032900 4404          Options.level0_slowdown_writes_trigger: 20
2025/07/26-17:57:05.032902 4404              Options.level0_stop_writes_trigger: 36
2025/07/26-17:57:05.032904 4404                   Options.target_file_size_base: 67108864
2025/07/26-17:57:05.032906 4404             Options.target_file_size_multiplier: 1
2025/07/26-17:57:05.032907 4404                Options.max_bytes_for_level_base: 268435456
2025/07/26-17:57:05.032909 4404 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-17:57:05.032911 4404          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-17:57:05.032914 4404 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-17:57:05.032916 4404 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-17:57:05.032918 4404 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-17:57:05.032920 4404 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-17:57:05.032922 4404 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-17:57:05.032924 4404 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-17:57:05.032926 4404 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-17:57:05.032928 4404       Options.max_sequential_skip_in_iterations: 8
2025/07/26-17:57:05.032929 4404                    Options.max_compaction_bytes: 1677721600
2025/07/26-17:57:05.032931 4404   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-17:57:05.032933 4404                        Options.arena_block_size: 1048576
2025/07/26-17:57:05.032935 4404   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-17:57:05.032937 4404   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-17:57:05.032939 4404                Options.disable_auto_compactions: 0
2025/07/26-17:57:05.032942 4404                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-17:57:05.032944 4404                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-17:57:05.032946 4404 Options.compaction_options_universal.size_ratio: 1
2025/07/26-17:57:05.032948 4404 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-17:57:05.032950 4404 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-17:57:05.032952 4404 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-17:57:05.032954 4404 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-17:57:05.032957 4404 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-17:57:05.032959 4404 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-17:57:05.032961 4404 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-17:57:05.032965 4404                   Options.table_properties_collectors: 
2025/07/26-17:57:05.032967 4404                   Options.inplace_update_support: 0
2025/07/26-17:57:05.032969 4404                 Options.inplace_update_num_locks: 10000
2025/07/26-17:57:05.032970 4404               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-17:57:05.032973 4404               Options.memtable_whole_key_filtering: 0
2025/07/26-17:57:05.032974 4404   Options.memtable_huge_page_size: 0
2025/07/26-17:57:05.032976 4404                           Options.bloom_locality: 0
2025/07/26-17:57:05.032978 4404                    Options.max_successive_merges: 0
2025/07/26-17:57:05.032980 4404                Options.optimize_filters_for_hits: 0
2025/07/26-17:57:05.032982 4404                Options.paranoid_file_checks: 0
2025/07/26-17:57:05.032984 4404                Options.force_consistency_checks: 1
2025/07/26-17:57:05.032986 4404                Options.report_bg_io_stats: 0
2025/07/26-17:57:05.032988 4404                               Options.ttl: 2592000
2025/07/26-17:57:05.032990 4404          Options.periodic_compaction_seconds: 0
2025/07/26-17:57:05.032992 4404                        Options.default_temperature: kUnknown
2025/07/26-17:57:05.032994 4404  Options.preclude_last_level_data_seconds: 0
2025/07/26-17:57:05.032995 4404    Options.preserve_internal_time_seconds: 0
2025/07/26-17:57:05.032997 4404                       Options.enable_blob_files: false
2025/07/26-17:57:05.032999 4404                           Options.min_blob_size: 0
2025/07/26-17:57:05.033001 4404                          Options.blob_file_size: 268435456
2025/07/26-17:57:05.033003 4404                   Options.blob_compression_type: NoCompression
2025/07/26-17:57:05.033005 4404          Options.enable_blob_garbage_collection: false
2025/07/26-17:57:05.033007 4404      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-17:57:05.033009 4404 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-17:57:05.033011 4404          Options.blob_compaction_readahead_size: 0
2025/07/26-17:57:05.033013 4404                Options.blob_file_starting_level: 0
2025/07/26-17:57:05.033015 4404         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-17:57:05.033017 4404            Options.memtable_max_range_deletions: 0
2025/07/26-17:57:05.033180 4404 [db/db_impl/db_impl.cc:3637] Created column family [task_versions] (ID 3)
2025/07/26-17:57:05.035816 4404 [db/column_family.cc:618] --------------- Options for column family [task_index]:
2025/07/26-17:57:05.035833 4404               Options.comparator: leveldb.BytewiseComparator
2025/07/26-17:57:05.035836 4404           Options.merge_operator: None
2025/07/26-17:57:05.035838 4404        Options.compaction_filter: None
2025/07/26-17:57:05.035840 4404        Options.compaction_filter_factory: None
2025/07/26-17:57:05.035843 4404  Options.sst_partitioner_factory: None
2025/07/26-17:57:05.035845 4404         Options.memtable_factory: SkipListFactory
2025/07/26-17:57:05.035847 4404            Options.table_factory: BlockBasedTable
2025/07/26-17:57:05.035870 4404            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000223B88D1BF0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 00000223B88B12C0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/26-17:57:05.035875 4404        Options.write_buffer_size: 67108864
2025/07/26-17:57:05.035880 4404  Options.max_write_buffer_number: 2
2025/07/26-17:57:05.035882 4404          Options.compression: Snappy
2025/07/26-17:57:05.035884 4404                  Options.bottommost_compression: Disabled
2025/07/26-17:57:05.035887 4404       Options.prefix_extractor: nullptr
2025/07/26-17:57:05.035889 4404   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/26-17:57:05.035891 4404             Options.num_levels: 7
2025/07/26-17:57:05.035893 4404        Options.min_write_buffer_number_to_merge: 1
2025/07/26-17:57:05.035895 4404     Options.max_write_buffer_number_to_maintain: 0
2025/07/26-17:57:05.035898 4404     Options.max_write_buffer_size_to_maintain: 0
2025/07/26-17:57:05.035900 4404            Options.bottommost_compression_opts.window_bits: -14
2025/07/26-17:57:05.035902 4404                  Options.bottommost_compression_opts.level: 32767
2025/07/26-17:57:05.035904 4404               Options.bottommost_compression_opts.strategy: 0
2025/07/26-17:57:05.035907 4404         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/26-17:57:05.035909 4404         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:05.035911 4404         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/26-17:57:05.035913 4404                  Options.bottommost_compression_opts.enabled: false
2025/07/26-17:57:05.035917 4404         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:05.035918 4404         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:05.035920 4404            Options.compression_opts.window_bits: -14
2025/07/26-17:57:05.035922 4404                  Options.compression_opts.level: 32767
2025/07/26-17:57:05.035924 4404               Options.compression_opts.strategy: 0
2025/07/26-17:57:05.035925 4404         Options.compression_opts.max_dict_bytes: 0
2025/07/26-17:57:05.035927 4404         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/26-17:57:05.035929 4404         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/26-17:57:05.035931 4404         Options.compression_opts.parallel_threads: 1
2025/07/26-17:57:05.035932 4404                  Options.compression_opts.enabled: false
2025/07/26-17:57:05.035934 4404         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/26-17:57:05.035936 4404      Options.level0_file_num_compaction_trigger: 4
2025/07/26-17:57:05.035938 4404          Options.level0_slowdown_writes_trigger: 20
2025/07/26-17:57:05.035939 4404              Options.level0_stop_writes_trigger: 36
2025/07/26-17:57:05.035941 4404                   Options.target_file_size_base: 67108864
2025/07/26-17:57:05.035943 4404             Options.target_file_size_multiplier: 1
2025/07/26-17:57:05.035944 4404                Options.max_bytes_for_level_base: 268435456
2025/07/26-17:57:05.035946 4404 Options.level_compaction_dynamic_level_bytes: 1
2025/07/26-17:57:05.035948 4404          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/26-17:57:05.035951 4404 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/26-17:57:05.035952 4404 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/26-17:57:05.035954 4404 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/26-17:57:05.035956 4404 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/26-17:57:05.035958 4404 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/26-17:57:05.035960 4404 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/26-17:57:05.035962 4404 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/26-17:57:05.035964 4404       Options.max_sequential_skip_in_iterations: 8
2025/07/26-17:57:05.035965 4404                    Options.max_compaction_bytes: 1677721600
2025/07/26-17:57:05.035967 4404   Options.ignore_max_compaction_bytes_for_input: true
2025/07/26-17:57:05.035969 4404                        Options.arena_block_size: 1048576
2025/07/26-17:57:05.035971 4404   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/26-17:57:05.035972 4404   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/26-17:57:05.035974 4404                Options.disable_auto_compactions: 0
2025/07/26-17:57:05.035976 4404                        Options.compaction_style: kCompactionStyleLevel
2025/07/26-17:57:05.035979 4404                          Options.compaction_pri: kMinOverlappingRatio
2025/07/26-17:57:05.035980 4404 Options.compaction_options_universal.size_ratio: 1
2025/07/26-17:57:05.035982 4404 Options.compaction_options_universal.min_merge_width: 2
2025/07/26-17:57:05.035984 4404 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/26-17:57:05.035986 4404 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/26-17:57:05.035988 4404 Options.compaction_options_universal.compression_size_percent: -1
2025/07/26-17:57:05.035990 4404 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/26-17:57:05.035992 4404 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/26-17:57:05.035993 4404 Options.compaction_options_fifo.allow_compaction: 0
2025/07/26-17:57:05.035998 4404                   Options.table_properties_collectors: 
2025/07/26-17:57:05.036000 4404                   Options.inplace_update_support: 0
2025/07/26-17:57:05.036002 4404                 Options.inplace_update_num_locks: 10000
2025/07/26-17:57:05.036003 4404               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/26-17:57:05.036005 4404               Options.memtable_whole_key_filtering: 0
2025/07/26-17:57:05.036007 4404   Options.memtable_huge_page_size: 0
2025/07/26-17:57:05.036009 4404                           Options.bloom_locality: 0
2025/07/26-17:57:05.036011 4404                    Options.max_successive_merges: 0
2025/07/26-17:57:05.036012 4404                Options.optimize_filters_for_hits: 0
2025/07/26-17:57:05.036014 4404                Options.paranoid_file_checks: 0
2025/07/26-17:57:05.036016 4404                Options.force_consistency_checks: 1
2025/07/26-17:57:05.036017 4404                Options.report_bg_io_stats: 0
2025/07/26-17:57:05.036019 4404                               Options.ttl: 2592000
2025/07/26-17:57:05.036021 4404          Options.periodic_compaction_seconds: 0
2025/07/26-17:57:05.036023 4404                        Options.default_temperature: kUnknown
2025/07/26-17:57:05.036025 4404  Options.preclude_last_level_data_seconds: 0
2025/07/26-17:57:05.036026 4404    Options.preserve_internal_time_seconds: 0
2025/07/26-17:57:05.036028 4404                       Options.enable_blob_files: false
2025/07/26-17:57:05.036030 4404                           Options.min_blob_size: 0
2025/07/26-17:57:05.036032 4404                          Options.blob_file_size: 268435456
2025/07/26-17:57:05.036033 4404                   Options.blob_compression_type: NoCompression
2025/07/26-17:57:05.036035 4404          Options.enable_blob_garbage_collection: false
2025/07/26-17:57:05.036037 4404      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/26-17:57:05.036039 4404 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/26-17:57:05.036041 4404          Options.blob_compaction_readahead_size: 0
2025/07/26-17:57:05.036042 4404                Options.blob_file_starting_level: 0
2025/07/26-17:57:05.036044 4404         Options.experimental_mempurge_threshold: 0.000000
2025/07/26-17:57:05.036047 4404            Options.memtable_max_range_deletions: 0
2025/07/26-17:57:05.036234 4404 [db/db_impl/db_impl.cc:3637] Created column family [task_index] (ID 4)
2025/07/26-17:57:05.043383 4404 [db/db_impl/db_impl_open.cc:2157] SstFileManager instance 00000223BA3EF6E0
2025/07/26-17:57:05.043595 4404 DB pointer 00000223BA3E9100
2025/07/26-17:57:05.044105 3288 [db/db_impl/db_impl.cc:1140] ------- DUMPING STATS -------
2025/07/26-17:57:05.044115 3288 [db/db_impl/db_impl.cc:1141] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000223B88B1C20#5312 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 5.2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** Compaction Stats [tasks] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [tasks] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000223B88B13B0#5312 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2.3e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [tasks] **

** Compaction Stats [raft_state] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [raft_state] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000223B88B0FF0#5312 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2.1e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [raft_state] **

** Compaction Stats [task_versions] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_versions] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000223B88B1590#5312 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2.1e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_versions] **

** Compaction Stats [task_index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [task_index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@00000223B88B12C0#5312 capacity: 32.00 MB seed: 1753221743 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [task_index] **
